<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Tree extends Model
{
	protected $fillable = [
		'label',
		'alternative_label',
		'rfid_id',
		'status',
		'sampling_point_id'
	];

	public function sampling_point()
	{
		return $this->belongsTo('App\Models\SamplingPoint');
	}

	public function tree_visits()
	{
		return $this->hasMany('App\Models\TreeVisit');
	}

	public function rfid()
    {
        return $this->belongsTo('App\Models\Rfid', 'rfid_id');
    }
}
