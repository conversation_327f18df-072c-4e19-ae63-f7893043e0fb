<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Rfid extends Model
{
	protected $fillable = [
		'code',
		'property_id',
		'user_id'
	];

	public function tree()
	{
		return $this->hasOne('App\Models\Tree', 'rfid_id');
	}

	public function samplingPoint()
	{
		return $this->belongsTo('App\Models\SamplingPoint', 'property_id');
	}

	public function user()
	{
		return $this->belongsTo('App\User', 'user_id');
	}
}
