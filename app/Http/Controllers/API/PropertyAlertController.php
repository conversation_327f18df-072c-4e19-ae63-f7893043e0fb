<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Property;
use App\Models\TreeVisit;
use App\Models\HomogeneousArea;
use App\Models\VisitInformation;
use App\Models\Stratum;
use App\Models\SamplingPoint;
use App\Models\Tree;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class PropertyAlertController extends Controller
{
    /**
     * Buscar dados da última poda de uma propriedade
     */
    public function getLastPruning($propertyId)
    {
        try {
            $property = Property::find($propertyId);

            if (!$property) {
                return response()->json(['success' => false, 'message' => 'Propriedade não encontrada'], 404);
            }

            $areaIds = HomogeneousArea::where('property_id', $propertyId)->pluck('id');

            if ($areaIds->isEmpty()) {
                return response()->json(['success' => false, 'message' => 'Nenhuma área homogênea encontrada'], 404);
            }

            $lastPruning = VisitInformation::whereIn('homogeneous_area_id', $areaIds)
                ->where('pruned', 1)
                ->orderBy('date', 'desc')
                ->first();

            Log::info('Última poda encontrada:', ['lastPruning' => $lastPruning]);

            if (!$lastPruning) {
                return response()->json([
                    'success' => true,
                    'status' => 'never',
                    'message' => 'Nunca podada',
                    'last_pruning_date' => null,
                    'months_ago' => null,
                    'needs_maintenance' => true
                ]);
            }

            $lastDate = Carbon::parse($lastPruning->date);
            $monthsAgo = Carbon::now()->diffInMonths($lastDate);
            $needsMaintenance = $monthsAgo > 6;

            return response()->json([
                'success' => true,
                'last_pruning_date' => $lastDate->toDateString(),
                'months_ago' => $monthsAgo,
                'status' => $needsMaintenance ? 'maintenance' : 'ok',
                'message' => $needsMaintenance ? 'Poda de Manutenção' : 'Em dia',
                'needs_maintenance' => $needsMaintenance
            ]);
        } catch (\Exception $e) {
            Log::error('Erro em getLastPruning: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Erro ao processar os dados'], 500);
        }
    }

	public function getWitchsBroomData($propertyId)
	{
		try {
			$property = Property::find($propertyId);

			if (!$property) {
				return response()->json(['success' => false, 'message' => 'Propriedade não encontrada'], 404);
			}

			$areaIds = HomogeneousArea::where('property_id', $propertyId)->pluck('id');
			if ($areaIds->isEmpty()) {
				return response()->json(['success' => false, 'message' => 'Nenhuma área homogênea encontrada'], 404);
			}

			$now = Carbon::now();
			if ($now->month >= 10) {
				$startDate = Carbon::create($now->year, 10, 1)->startOfMonth();
				$endDate = Carbon::create($now->year + 1, 9, 30)->endOfMonth();
			} else {
				$startDate = Carbon::create($now->year - 1, 10, 1)->startOfMonth();
				$endDate = Carbon::create($now->year, 9, 30)->endOfMonth();
			}

			$result = DB::table('tree_visits')
			->join('trees', 'tree_visits.tree_id', '=', 'trees.id')
			->join('sampling_points', 'trees.sampling_point_id', '=', 'sampling_points.id')
			->join('strata', 'sampling_points.stratum_id', '=', 'strata.id')

			->leftJoin('bobbins', 'tree_visits.bobbin_id', '=', 'bobbins.id')
			->leftJoin('smalls', 'tree_visits.small_id', '=', 'smalls.id')
			->leftJoin('mediums', 'tree_visits.medium_id', '=', 'mediums.id')
			->leftJoin('mediums2', 'tree_visits.medium2_id', '=', 'mediums2.id')
			->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
			->leftJoin('adults', 'tree_visits.adult_id', '=', 'adults.id')
			->leftJoin('adults2', 'tree_visits.adult2_id', '=', 'adults2.id')
			->leftJoin('matures', 'tree_visits.mature_id', '=', 'matures.id')
			->leftJoin('matures2', 'tree_visits.mature2_id', '=', 'matures2.id')
			->leftJoin('matures3', 'tree_visits.mature3_id', '=', 'matures3.id')
			->leftJoin('matures4', 'tree_visits.mature4_id', '=', 'matures4.id')

			->whereIn('strata.homogeneous_area_id', $areaIds)
			->whereBetween('tree_visits.date', [$startDate, $endDate])
			->selectRaw('
				COUNT(tree_visits.id) as visits_analyzed,
				COALESCE(SUM(
					COALESCE(bobbins.total, 0) +
					COALESCE(smalls.total, 0) +
					COALESCE(mediums.total, 0) +
					COALESCE(mediums2.total, 0) +
					COALESCE(mediums3.total, 0) +
					COALESCE(adults.total, 0) +
					COALESCE(adults2.total, 0) +
					COALESCE(matures.total, 0) +
					COALESCE(matures2.total, 0) +
					COALESCE(matures3.total, 0) +
					COALESCE(matures4.total, 0)
				), 0) as total_fruits,
				COALESCE(SUM(
					COALESCE(smalls.witchs_broom, 0) +
					COALESCE(mediums2.witchs_broom, 0) +
					COALESCE(mediums3.witchs_broom, 0) +
					COALESCE(adults.witchs_broom, 0) +
					COALESCE(adults2.witchs_broom, 0) +
					COALESCE(matures.witchs_broom, 0) +
					COALESCE(matures2.witchs_broom, 0) +
					COALESCE(matures3.witchs_broom, 0) +
					COALESCE(matures4.witchs_broom, 0)
				), 0) as witchs_broom_fruits
			')
			->first();

			if (!$result || $result->visits_analyzed == 0) {
				return response()->json([
					'success' => true,
					'status' => 'no_data',
					'total_fruits' => 0,
					'witchs_broom_fruits' => 0,
					'percentage' => 0,
					'severity' => 'unknown',
					'visits_analyzed' => 0,
					'period_start' => $startDate->toDateString(),
					'period_end' => $endDate->toDateString()
				]);
			}

			$totalFruits = $result->total_fruits;
			$wb = $result->witchs_broom_fruits;
			$percentage = $totalFruits > 0 ? ($wb / $totalFruits) * 100 : 0;
			$severity = $this->getWitchsBroomSeverity($percentage);

			return response()->json([
				'success' => true,
				'status' => $severity,
				'total_fruits' => $totalFruits,
				'witchs_broom_fruits' => $wb,
				'percentage' => round($percentage, 1),
				'severity' => $severity,
				'visits_analyzed' => $result->visits_analyzed,
				'period_start' => $startDate->toDateString(),
				'period_end' => $endDate->toDateString()
			]);
		} catch (\Exception $e) {
			Log::error('Erro em getWitchsBroomData: ' . $e->getMessage());
			return response()->json(['success' => false, 'message' => 'Erro ao processar os dados'], 500);
		}
	}

    /**
     * Determinar severidade da vassoura de bruxa baseada na porcentagem
     */
    private function getWitchsBroomSeverity($percentage)
    {
        if ($percentage > 20) {
            return 'critical';
        } elseif ($percentage > 10) {
            return 'high';
        } elseif ($percentage > 1) {
            return 'medium';
        } else {
            return 'low';
        }
    }
}
