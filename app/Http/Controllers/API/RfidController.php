<?php

namespace App\Http\Controllers\API;

use App\Models\Rfid;
use App\Models\Tree;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

use App\Models\SamplingPoint;
use Illuminate\Support\Facades\Auth;

class RfidController extends BaseController
{
	protected $messages = [
		'code.required' => 'Código é obrigatório',
		'code.unique' => 'O código já está em uso para esta propriedade',
		'property_id.required' => 'Propriedade é obrigatória',
	];

	/**
	 * Exibe todos os RFIDs
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function index()
	{
		$user = Auth::user();

		if ($user->isAdmin()) {
			$rfids = Rfid::with('user')->orderBy('id', 'asc')->get();
		} else {
			$rfids = Rfid::with('user')->where('user_id', $user->id)->orderBy('id', 'asc')->get();
		}

		return $this->send_response($rfids);
	}

	/**
	 * Cria um novo RFID
	 *
	 * @bodyParam code string required Código único do RFID. Example: 12345ABC
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @return \Illuminate\Http\Response
	 */
	public function store(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'code' => 'required|string|max:255',
		], $this->messages);

		$user = Auth::user();

		$rfidQuery = Rfid::where('code', $request->code);
		if (!$user->isAdmin()) {
			$rfidQuery->where('user_id', $user->id);
		}

		if ($rfidQuery->exists()) {
			return $this->send_error('Erro ao tentar cadastrar o RFID', ['code' => 'O código já está em uso para este usuário']);
		}

		if ($validator->fails()) {
			return $this->send_error('Erro ao tentar cadastrar o RFID', $validator->errors());
		}

		$rfid = new Rfid();
		$rfid->code = $request->get('code');
		$rfid->user_id = auth()->id();
		$rfid->save();

		return $this->send_response($rfid, 'RFID cadastrado com sucesso', 201);
	}

	/**
	 * Exibe um RFID específico
	 *
	 * @param  \App\Models\Rfid  $rfid
	 * @return \Illuminate\Http\Response
	 */
	public function show(Rfid $rfid)
	{
		return $this->send_response($rfid);
	}

	/**
	 * Atualiza um RFID específico
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @param  \App\Models\Rfid  $rfid
	 * @return \Illuminate\Http\Response
	 */
	public function update(Request $request, Rfid $rfid)
	{
		$validator = Validator::make($request->all(), [
			'code' => 'required|string|max:255',
		], $this->messages);

		$user = auth()->user();

		$rfidQuery = Rfid::where('code', $request->code)
			->where('id', '!=', $rfid->id);

		if (!$user->isAdmin()) {
			$rfidQuery->where('user_id', $user->id);
		}
		if ($rfidQuery->exists()) {
			return $this->send_error('Erro ao tentar cadastrar o RFID', ['code' => 'O código já está em uso para este usuário']);
		}

		if ($validator->fails()) {
			return $this->send_error('Erro ao tentar atualizar o RFID', $validator->errors());
		}

		$rfid->code = $request->get('code');
		$rfid->user_id = auth()->id();
		$rfid->update();

		return $this->send_response($rfid, 'RFID atualizado com sucesso');
	}

	/**
	 * Remove um RFID específico
	 *
	 * @param  \App\Models\Rfid  $rfid
	 * @return \Illuminate\Http\Response
	 */
	public function destroy(Rfid $rfid)
	{
		$tree = $rfid->tree;
		if ($tree) {
			$tree->rfid_id = null;
			$tree->save();
		}

		$rfid->delete();
		return $this->send_response(null, 'RFID removido com sucesso');
	}

	/**
	 * Exibe a árvore associada a um RFID específico
	 *
	 * @param  \App\Models\Rfid  $rfid
	 * @return \Illuminate\Http\Response
	 */
	public function showTree(Rfid $rfid)
	{
		$tree = $rfid->tree;

		if (!$tree) {
			return $this->send_error('Nenhuma árvore associada a este RFID');
		}

		return $this->send_response($tree, 'Árvore associada ao RFID');
	}

	/**
	 * Associa um RFID a uma árvore
	 *
	 * @bodyParam tree_id int required ID da árvore a ser associada. Example: 1
	 * @bodyParam code string required Código do RFID a ser associado. Example: 12345ABC
	 * @bodyParam sp_id int required ID do ponto de amostragem associado (sampling_point_id). Example: 1
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @return \Illuminate\Http\Response
	 */
	public function assignRfidToTree(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'tree_id' => 'required|exists:trees,id',
			'rfid_id' => 'required|exists:rfids,id',
			'sp_id' => 'required|exists:sampling_points,id',
		]);

		if ($validator->fails()) {
			return $this->send_error('Erro de validação', $validator->errors());
		}

		$tree = Tree::find($request->tree_id);
		$rfid = Rfid::find($request->rfid_id);
		$samplingPoint = SamplingPoint::find($request->sp_id);

		if ($rfid->tree) {
			return $this->send_error('Este RFID já está associado a outra árvore.');
		}

		if (!$samplingPoint || !$samplingPoint->property_id) {
			return $this->send_error('Ponto amostral inválido ou sem propriedade associada.');
		}

		$rfid->property_id = $samplingPoint->property_id;
		$rfid->save();

		$tree->rfid_id = $request->rfid_id;
		$tree->save();

		return $this->send_response($tree, 'RFID associado à árvore com sucesso');
	}

	/**
	 * Remove a associação de um RFID de uma árvore
	 *
	 * @bodyParam tree_id int required ID da árvore a ser dissociada. Example: 1
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @return \Illuminate\Http\Response
	 */
	public function removeRfidFromTree(Request $request)
	{
		$validator = Validator::make($request->all(), [
			'tree_id' => 'required|exists:trees,id',
		]);

		if ($validator->fails()) {
			return $this->send_error('Erro de validação', $validator->errors());
		}

		$tree = Tree::find($request->tree_id);

		if (!$tree->rfid_id) {
			return $this->send_error('Esta árvore não tem nenhum RFID associado.');
		}

		$tree->rfid_id = null;
		$tree->save();

		return $this->send_response($tree, 'RFID removido da árvore com sucesso');
	}

	/**
	 * Retorna os RFIDs que não estão associados a nenhuma árvore.
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function unassigned()
	{
		$user = Auth::user();

		if ($user->isAdmin()) {
			$unassignedRfids = Rfid::whereDoesntHave('tree')->orderBy('id', 'asc')->get();
		} else {
			$unassignedRfids = Rfid::where('user_id', $user->id)
				->whereDoesntHave('tree')
				->orderBy('id', 'asc')
				->get();
		}

		if ($unassignedRfids->isEmpty()) {
			return $this->send_response([]);
		}


		return $this->send_response($unassignedRfids);
	}
}
