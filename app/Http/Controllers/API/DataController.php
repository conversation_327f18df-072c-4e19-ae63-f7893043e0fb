<?php

namespace App\Http\Controllers\API;

use PDF;
use Maatwebsite\Excel\Facades\Excel;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

use App\Exports\ExcelExport;
use App\Imports\ExcelImport;
use App\Exports\BlockExport;
use App\Exports\PracticeExport;
use App\Exports\ExcelPropertyExport;
use App\Exports\ModelExport;

use App\Jobs\ImportSpreadSheet;

use App\User;
use App\Models\Stratum;
use App\Models\Property;
use App\Models\TreeVisit;
use App\Models\SamplingPoint;
use Illuminate\Support\Facades\Auth;
use App\Models\Collection;
use App\Http\Resources\TreeVisitResource;

set_time_limit(0);

class DataController extends BaseController
{
	private $propertyId;
    private $startDate;
    private $endDate;
	private $areaHomogeneaId;

	public function getCollectionData(Request $request)
    {


        try {
            $request->validate([
                'propertyId' => 'required|integer|exists:properties,id',
                'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
                'startDate' => 'required|string',
                'endDate' => 'required|string',
            ]);
			Log::info('Request Data', [
				'propertyId' => $request->input('propertyId'),
				'areaHomogeneaId' => $request->input('areaHomogeneaId'),
				'startDate' => $request->input('startDate'),
				'endDate' => $request->input('endDate'),
			]);

            $this->propertyId = $request->input('propertyId');
            $this->areaHomogeneaId = $request->input('areaHomogeneaId');
            $startDateInput = $request->input('startDate');
            $endDateInput = $request->input('endDate');

            // Conversão de datas para o formato correto
            $months = [
                'Janeiro' => 'January',
				'Fevereiro' => 'February',
				'Março' => 'March',
				'Abril' => 'April',
                'Maio' => 'May',
				'Junho' => 'June',
				'Julho' => 'July',
				'Agosto' => 'August',
                'Setembro' => 'September',
				'Outubro' => 'October',
				'Novembro' => 'November',
				'Dezembro' => 'December'
            ];

            // Converter o mês de startDate
			list($startMonth, $startYear) = explode('/', $startDateInput);
			$startMonthEnglish = $months[$startMonth] ?? null;
			if (!$startMonthEnglish) {
				throw new \Exception("Mês de início inválido: $startMonth");
			}
			$this->startDate = Carbon::createFromFormat('F/Y', $startMonthEnglish . '/' . $startYear)->startOfMonth();

			// Converter o mês de endDate
			list($endMonth, $endYear) = explode('/', $endDateInput);
			$endMonthEnglish = $months[$endMonth] ?? null;
			if (!$endMonthEnglish) {
				throw new \Exception("Mês de fim inválido: $endMonth");
			}
			$this->endDate = Carbon::createFromFormat('F/Y', $endMonthEnglish . '/' . $endYear)->endOfMonth();

			Log::info('Datas convertidas:', ['startDate' => $this->startDate->format('Y-m-d'), 'endDate' => $this->endDate->format('Y-m-d')]);

            // Filtrar Sampling Points com base no propertyId
			$sampling_points = SamplingPoint::whereHas('stratum', function ($query) {
				$query->where('property_id', $this->propertyId);
			})->get();

			// Extrair os IDs dos pontos amostrais
			$samplingPointIds = $sampling_points->pluck('id');

			// Fazer uma única query para buscar todas as visitas
			$all_tree_visits = TreeVisit::join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->whereIn('trees.sampling_point_id', $samplingPointIds)
				->where('homogeneous_areas.id', '=', $this->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$this->startDate, $this->endDate])
				->select(
					'tree_visits.*',
					'trees.id as tree_id',
					'sampling_points.id as sampling_point_id',
					'strata.id as stratum_id',
					'homogeneous_areas.id as homogeneous_area_id'
				)
				->get();

			// Logar quantidade
			Log::info('Total tree visits processed:', [
				'total_visits' => $all_tree_visits->count(),
			]);

			// Converter os dados
			$data = TreeVisitResource::collection($all_tree_visits);


        	return response()->json($data);

        } catch (\Exception $e) {
            Log::error('Erro ao buscar dados de coleta:', ['error_message' => $e->getMessage()]);
            return response()->json(['error' => 'Erro ao obter os dados'], 500);
        }

    }

	public function getCollectionDataMultiArea(Request $request)
	{
		try {
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaIds' => 'required|array|min:1',
				'areaHomogeneaIds.*' => 'integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			$this->propertyId = $request->input('propertyId');
			$areaHomogeneaIds = $request->input('areaHomogeneaIds');
			$startDateInput = $request->input('startDate');
			$endDateInput = $request->input('endDate');

			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June', 'Julho' => 'July',
				'Agosto' => 'August', 'Setembro' => 'September', 'Outubro' => 'October',
				'Novembro' => 'November', 'Dezembro' => 'December'
			];

			[$startMonth, $startYear] = explode('/', $startDateInput);
			[$endMonth, $endYear] = explode('/', $endDateInput);
			$startMonthEnglish = $months[$startMonth] ?? null;
			$endMonthEnglish = $months[$endMonth] ?? null;

			if (!$startMonthEnglish || !$endMonthEnglish) {
				throw new \Exception("Mês inválido.");
			}

			$startDate = Carbon::createFromFormat('F/Y', "$startMonthEnglish/$startYear")->startOfMonth();
			$endDate = Carbon::createFromFormat('F/Y', "$endMonthEnglish/$endYear")->endOfMonth();

			$samplingPoints = SamplingPoint::whereHas('stratum', function ($query) {
				$query->where('property_id', $this->propertyId);
			})->get();

			$samplingPointIds = $samplingPoints->pluck('id');

			$treeVisits = TreeVisit::join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->whereIn('trees.sampling_point_id', $samplingPointIds)
				->whereIn('homogeneous_areas.id', $areaHomogeneaIds)
				->whereBetween('tree_visits.date', [$startDate, $endDate])
				->select('tree_visits.*')
				->get();

			// Inicializa variáveis
			$totals = [
				'totalHarvested' => 0,
				'totalLoss' => 0,
				'totalPiece' => 0,
				'totalRat' => 0,
				'totalRotten' => 0,
				'totalWitchBroom' => 0
			];

			$monthlyHarvested = array_fill(0, 12, 0);

			foreach ($treeVisits as $visit) {
				$harvested = collect([
					$visit->medium3['harvested'] ?? 0,
					$visit->adult['harvested'] ?? 0,
					$visit->adult2['harvested'] ?? 0,
					$visit->mature['harvested'] ?? 0,
					$visit->mature2['harvested'] ?? 0,
					$visit->mature3['harvested'] ?? 0,
					$visit->mature4['harvested'] ?? 0,
				])->sum();

				$loss = collect([
					$visit->small['loss'] ?? 0, $visit->medium['loss'] ?? 0, $visit->medium2['loss'] ?? 0,
					$visit->medium3['loss'] ?? 0, $visit->adult['loss'] ?? 0, $visit->adult2['loss'] ?? 0,
					$visit->mature['loss'] ?? 0, $visit->mature2['loss'] ?? 0, $visit->mature3['loss'] ?? 0,
					$visit->mature4['loss'] ?? 0,
				])->sum();

				$piece = collect([
					$visit->small['piece'] ?? 0, $visit->medium['piece'] ?? 0,
					$visit->medium2['piece'] ?? 0, $visit->medium3['piece'] ?? 0,
				])->sum();

				$rat = collect([
					$visit->medium2['rat'] ?? 0, $visit->medium3['rat'] ?? 0,
					$visit->adult['rat'] ?? 0, $visit->adult2['rat'] ?? 0,
					$visit->mature['rat'] ?? 0, $visit->mature2['rat'] ?? 0,
					$visit->mature3['rat'] ?? 0, $visit->mature4['rat'] ?? 0,
				])->sum();

				$rotten = collect([
					$visit->medium2['rotten'] ?? 0, $visit->medium3['rotten'] ?? 0,
					$visit->adult['rotten'] ?? 0, $visit->adult2['rotten'] ?? 0,
					$visit->mature['rotten'] ?? 0, $visit->mature2['rotten'] ?? 0,
					$visit->mature3['rotten'] ?? 0, $visit->mature4['rotten'] ?? 0,
				])->sum();

				$witchBroom = collect([
					$visit->small['witchs_broom'] ?? 0, $visit->medium2['witchs_broom'] ?? 0,
					$visit->medium3['witchs_broom'] ?? 0, $visit->adult['witchs_broom'] ?? 0,
					$visit->adult2['witchs_broom'] ?? 0, $visit->mature['witchs_broom'] ?? 0,
					$visit->mature2['witchs_broom'] ?? 0, $visit->mature3['witchs_broom'] ?? 0,
					$visit->mature4['witchs_broom'] ?? 0,
				])->sum();

				// Soma os totais
				$totals['totalHarvested'] += $harvested;
				$totals['totalLoss'] += $loss;
				$totals['totalPiece'] += $piece;
				$totals['totalRat'] += $rat;
				$totals['totalRotten'] += $rotten;
				$totals['totalWitchBroom'] += $witchBroom;

				// Acumula por mês
				$date = new \DateTime($visit->date);
				$monthIndex = (int) $date->format('n') - 1;
				$monthlyHarvested[$monthIndex] += $harvested;
			}

			$lossEstimate = $totals['totalPiece'] + $totals['totalRat'] + $totals['totalRotten'] + $totals['totalWitchBroom'] + $totals['totalLoss'];
			$totalCocoa = $lossEstimate + $totals['totalHarvested'];

			// Mês agrícola: reorganizar começando por outubro
			$reordered = array_merge(array_slice($monthlyHarvested, 9), array_slice($monthlyHarvested, 0, 9));
			$monthKeys = ['out', 'nov', 'dez', 'jan', 'fev', 'mar', 'abr', 'mai', 'jun', 'jul', 'ago', 'set'];
			$totalHarvestedByMonth = array_combine($monthKeys, $reordered);

			return response()->json([
				'totalCocoaData' => array_merge($totals, [
					'lossEstimate' => $lossEstimate,
					'totalCocoa' => $totalCocoa,
				]),
				'totalHarvested' => $totalHarvestedByMonth
			]);

		} catch (\Exception $e) {
			Log::error('Erro ao buscar dados de múltiplas áreas:', ['error_message' => $e->getMessage()]);
			return response()->json(['error' => 'Erro ao obter os dados'], 500);
		}
	}

	public function getFruitDistribution(Request $request)
{
	try {
		$request->validate([
			'propertyId' => 'required|integer|exists:properties,id',
			'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
			'startDate' => 'required|string',
			'endDate' => 'required|string',
		]);

		$propertyId = $request->input('propertyId');
		$areaHomogeneaId = $request->input('areaHomogeneaId');
		$startDateInput = $request->input('startDate');
		$endDateInput = $request->input('endDate');

		$months = [
			'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
			'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
			'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
			'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
		];

		list($startMonth, $startYear) = explode('/', $startDateInput);
		$startMonthEnglish = $months[$startMonth] ?? throw new \Exception("Mês inválido: $startMonth");
		$startDate = Carbon::createFromFormat('F/Y', $startMonthEnglish . '/' . $startYear)->startOfMonth();

		list($endMonth, $endYear) = explode('/', $endDateInput);
		$endMonthEnglish = $months[$endMonth] ?? throw new \Exception("Mês inválido: $endMonth");
		$endDate = Carbon::createFromFormat('F/Y', $endMonthEnglish . '/' . $endYear)->endOfMonth();

		$samplingPointIds = SamplingPoint::whereHas('stratum', function ($query) use ($propertyId) {
			$query->where('property_id', $propertyId);
		})->pluck('id');

		$treeVisits = TreeVisit::join('trees', 'trees.id', '=', 'tree_visits.tree_id')
			->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
			->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
			->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
			->whereIn('trees.sampling_point_id', $samplingPointIds)
			->where('homogeneous_areas.id', $areaHomogeneaId)
			->whereBetween('tree_visits.date', [$startDate, $endDate])
			->select('tree_visits.*')
			->get();

		$rat = $piece = $rotten = $wb = $loss = $harvested = $totalFrutos = 0;
		$visitDates = [];

		foreach ($treeVisits as $visit) {
			Log::info('TreeVisit data sample:', ['data' => $visit->date]);
			$visitDates[] = $visit->date;

			$harvested += collect([
				$visit['medium3']['harvested'] ?? 0,
				$visit['adult']['harvested'] ?? 0,
				$visit['adult2']['harvested'] ?? 0,
				$visit['mature']['harvested'] ?? 0,
				$visit['mature2']['harvested'] ?? 0,
				$visit['mature3']['harvested'] ?? 0,
				$visit['mature4']['harvested'] ?? 0,
			])->sum();

			$loss += collect([
				$visit['small']['loss'] ?? 0,
				$visit['medium']['loss'] ?? 0,
				$visit['medium2']['loss'] ?? 0,
				$visit['medium3']['loss'] ?? 0,
				$visit['adult']['loss'] ?? 0,
				$visit['adult2']['loss'] ?? 0,
				$visit['mature']['loss'] ?? 0,
				$visit['mature2']['loss'] ?? 0,
				$visit['mature3']['loss'] ?? 0,
				$visit['mature4']['loss'] ?? 0,
			])->sum();

			$piece += collect([
				$visit['small']['piece'] ?? 0,
				$visit['medium']['piece'] ?? 0,
				$visit['medium2']['piece'] ?? 0,
				$visit['medium3']['piece'] ?? 0,
			])->sum();

			$rat += collect([
				$visit['medium2']['rat'] ?? 0,
				$visit['medium3']['rat'] ?? 0,
				$visit['adult']['rat'] ?? 0,
				$visit['adult2']['rat'] ?? 0,
				$visit['mature']['rat'] ?? 0,
				$visit['mature2']['rat'] ?? 0,
				$visit['mature3']['rat'] ?? 0,
				$visit['mature4']['rat'] ?? 0,
			])->sum();

			$rotten += collect([
				$visit['medium2']['rotten'] ?? 0,
				$visit['medium3']['rotten'] ?? 0,
				$visit['adult']['rotten'] ?? 0,
				$visit['adult2']['rotten'] ?? 0,
				$visit['mature']['rotten'] ?? 0,
				$visit['mature2']['rotten'] ?? 0,
				$visit['mature3']['rotten'] ?? 0,
				$visit['mature4']['rotten'] ?? 0,
			])->sum();

			$wb += collect([
				$visit['small']['witchs_broom'] ?? 0,
				$visit['medium2']['witchs_broom'] ?? 0,
				$visit['medium3']['witchs_broom'] ?? 0,
				$visit['adult']['witchs_broom'] ?? 0,
				$visit['adult2']['witchs_broom'] ?? 0,
				$visit['mature']['witchs_broom'] ?? 0,
				$visit['mature2']['witchs_broom'] ?? 0,
				$visit['mature3']['witchs_broom'] ?? 0,
				$visit['mature4']['witchs_broom'] ?? 0,
			])->sum();

			$totalFrutos += collect([
				$visit['bobbin']['total'] ?? 0,
				$visit['small']['total'] ?? 0,
				$visit['medium']['total'] ?? 0,
				$visit['medium2']['total'] ?? 0,
				$visit['medium3']['total'] ?? 0,
				$visit['adult']['total'] ?? 0,
				$visit['adult2']['total'] ?? 0,
				$visit['mature']['total'] ?? 0,
				$visit['mature2']['total'] ?? 0,
				$visit['mature3']['total'] ?? 0,
				$visit['mature4']['total'] ?? 0,
			])->sum();
		}
		$visitDates = array_values(array_unique($visitDates));
		Log::info('Distribuição de frutos:', [
			'harvested' => $harvested,
			'loss' => $loss,
			'rat' => $rat,
			'piece' => $piece,
			'rotten' => $rotten,
			'witchs_broom' => $wb,
			'totalFruits' => $totalFrutos,
			'visitDatesCount' => count($visitDates),
			'dates' => $visitDates
		]);
		return response()->json([
			'harvested' => $harvested,
			'loss' => $loss,
			'rat' => $rat,
			'piece' => $piece,
			'rotten' => $rotten,
			'witchs_broom' => $wb,
			'totalFruits' => $totalFrutos,
			'visitDates' => $visitDates,
		]);
	} catch (\Exception $e) {
		Log::error('Erro ao obter distribuição de frutos: ' . $e->getMessage());
		return response()->json(['error' => 'Erro ao processar os dados.'], 500);
	}
}

	public function getCollectionDataHarvested(Request $request)
    {


        try {
            $request->validate([
                'propertyId' => 'required|integer|exists:properties,id',
                'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
                'startDate' => 'required|string',
                'endDate' => 'required|string',
            ]);
			Log::info('Request Data', [
				'propertyId' => $request->input('propertyId'),
				'areaHomogeneaId' => $request->input('areaHomogeneaId'),
				'startDate' => $request->input('startDate'),
				'endDate' => $request->input('endDate'),
			]);

            $this->propertyId = $request->input('propertyId');
            $this->areaHomogeneaId = $request->input('areaHomogeneaId');
            $startDateInput = $request->input('startDate');
            $endDateInput = $request->input('endDate');

            // Conversão de datas para o formato correto
            $months = [
                'Janeiro' => 'January',
				'Fevereiro' => 'February',
				'Março' => 'March',
				'Abril' => 'April',
                'Maio' => 'May',
				'Junho' => 'June',
				'Julho' => 'July',
				'Agosto' => 'August',
                'Setembro' => 'September',
				'Outubro' => 'October',
				'Novembro' => 'November',
				'Dezembro' => 'December'
            ];

            // Converter o mês de startDate
			list($startMonth, $startYear) = explode('/', $startDateInput);
			$startMonthEnglish = $months[$startMonth] ?? null;
			if (!$startMonthEnglish) {
				throw new \Exception("Mês de início inválido: $startMonth");
			}
			$this->startDate = Carbon::createFromFormat('F/Y', $startMonthEnglish . '/' . $startYear)->startOfMonth();

			// Converter o mês de endDate
			list($endMonth, $endYear) = explode('/', $endDateInput);
			$endMonthEnglish = $months[$endMonth] ?? null;
			if (!$endMonthEnglish) {
				throw new \Exception("Mês de fim inválido: $endMonth");
			}
			$this->endDate = Carbon::createFromFormat('F/Y', $endMonthEnglish . '/' . $endYear)->endOfMonth();

			Log::info('Datas convertidas:', ['startDate' => $this->startDate->format('Y-m-d'), 'endDate' => $this->endDate->format('Y-m-d')]);

            // Filtrar Sampling Points com base no propertyId
			$sampling_points = SamplingPoint::whereHas('stratum', function ($query) {
				$query->where('property_id', $this->propertyId);
			})->get();

			// Extrair os IDs dos pontos amostrais
			$samplingPointIds = $sampling_points->pluck('id');

			// Fazer uma única query para buscar todas as visitas
			$all_tree_visits = TreeVisit::join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->whereIn('trees.sampling_point_id', $samplingPointIds)
				->where('homogeneous_areas.id', '=', $this->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$this->startDate, $this->endDate])
				->select(
					'tree_visits.*',
					'trees.id as tree_id',
					'sampling_points.id as sampling_point_id',
					'strata.id as stratum_id',
					'homogeneous_areas.id as homogeneous_area_id'
				)
				->get();

			// Logar quantidade
			Log::info('Total tree visits processed:', [
				'total_visits' => $all_tree_visits->count(),
			]);

			// Calcular o total de frutos colhidos por data
			$totalPerDate = [];

			foreach ($all_tree_visits as $visit) {
				$date = Carbon::parse($visit->date)->format('Y-m-d');

				$totalHarvested =
					($visit->medium3['harvested'] ?? 0) +
					($visit->adult['harvested'] ?? 0) +
					($visit->adult2['harvested'] ?? 0) +
					($visit->mature['harvested'] ?? 0) +
					($visit->mature2['harvested'] ?? 0) +
					($visit->mature3['harvested'] ?? 0) +
					($visit->mature4['harvested'] ?? 0);

				if (!isset($totalPerDate[$date])) {
					$totalPerDate[$date] = 0;
				}
				$totalPerDate[$date] += $totalHarvested;
			}

			// Retornar apenas os dados resumidos
			return response()->json([
				'areaId' => $this->areaHomogeneaId,
				'totals' => $totalPerDate
			]);


        } catch (\Exception $e) {
            Log::error('Erro ao buscar dados de coleta:', ['error_message' => $e->getMessage()]);
            return response()->json(['error' => 'Erro ao obter os dados'], 500);
        }

    }

	public function getCollectionDataLoss(Request $request)
    {
        try {
            $request->validate([
                'propertyId' => 'required|integer|exists:properties,id',
                'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
                'startDate' => 'required|string',
                'endDate' => 'required|string',
            ]);
			Log::info('Request Data', [
				'propertyId' => $request->input('propertyId'),
				'areaHomogeneaId' => $request->input('areaHomogeneaId'),
				'startDate' => $request->input('startDate'),
				'endDate' => $request->input('endDate'),
			]);

            $this->propertyId = $request->input('propertyId');
            $this->areaHomogeneaId = $request->input('areaHomogeneaId');
            $startDateInput = $request->input('startDate');
            $endDateInput = $request->input('endDate');

            // Conversão de datas para o formato correto
            $months = [
                'Janeiro' => 'January',
				'Fevereiro' => 'February',
				'Março' => 'March',
				'Abril' => 'April',
                'Maio' => 'May',
				'Junho' => 'June',
				'Julho' => 'July',
				'Agosto' => 'August',
                'Setembro' => 'September',
				'Outubro' => 'October',
				'Novembro' => 'November',
				'Dezembro' => 'December'
            ];

            // Converter o mês de startDate
			list($startMonth, $startYear) = explode('/', $startDateInput);
			$startMonthEnglish = $months[$startMonth] ?? null;
			if (!$startMonthEnglish) {
				throw new \Exception("Mês de início inválido: $startMonth");
			}
			$this->startDate = Carbon::createFromFormat('F/Y', $startMonthEnglish . '/' . $startYear)->startOfMonth();

			// Converter o mês de endDate
			list($endMonth, $endYear) = explode('/', $endDateInput);
			$endMonthEnglish = $months[$endMonth] ?? null;
			if (!$endMonthEnglish) {
				throw new \Exception("Mês de fim inválido: $endMonth");
			}
			$this->endDate = Carbon::createFromFormat('F/Y', $endMonthEnglish . '/' . $endYear)->endOfMonth();

			Log::info('Datas convertidas:', ['startDate' => $this->startDate->format('Y-m-d'), 'endDate' => $this->endDate->format('Y-m-d')]);

            // Filtrar Sampling Points com base no propertyId
			$sampling_points = SamplingPoint::whereHas('stratum', function ($query) {
				$query->where('property_id', $this->propertyId);
			})->get();

			// Extrair os IDs dos pontos amostrais
			$samplingPointIds = $sampling_points->pluck('id');

			// Fazer uma única query para buscar todas as visitas
			$all_tree_visits = TreeVisit::join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->whereIn('trees.sampling_point_id', $samplingPointIds)
				->where('homogeneous_areas.id', '=', $this->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$this->startDate, $this->endDate])
				->select(
					'tree_visits.*',
					'trees.id as tree_id',
					'sampling_points.id as sampling_point_id',
					'strata.id as stratum_id',
					'homogeneous_areas.id as homogeneous_area_id'
				)
				->get();

			// Calcular o total de frutos perdidos por data
			$totalPerDate = [];

			foreach ($all_tree_visits as $visit) {
				$date = Carbon::parse($visit->date)->format('Y-m-d');

				$totalLoss =
					($visit->small['loss'] ?? 0) +
					($visit->medium['loss'] ?? 0) +
					($visit->medium2['loss'] ?? 0) +
					($visit->medium3['loss'] ?? 0) +
					($visit->adult['loss'] ?? 0) +
					($visit->adult2['loss'] ?? 0) +
					($visit->mature['loss'] ?? 0) +
					($visit->mature2['loss'] ?? 0) +
					($visit->mature3['loss'] ?? 0) +
					($visit->mature4['loss'] ?? 0);

				if (!isset($totalPerDate[$date])) {
					$totalPerDate[$date] = 0;
				}
				$totalPerDate[$date] += $totalLoss;
			}

			// Retornar somente os totais de perdas por data
			return response()->json([
				'areaId' => $this->areaHomogeneaId,
				'totals' => $totalPerDate
			]);
        } catch (\Exception $e) {
            Log::error('Erro ao buscar dados de coleta:', ['error_message' => $e->getMessage()]);
            return response()->json(['error' => 'Erro ao obter os dados'], 500);
        }

    }

	public function getCollectionDataWB(Request $request)
    {


        try {
            $request->validate([
                'propertyId' => 'required|integer|exists:properties,id',
                'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
                'startDate' => 'required|string',
                'endDate' => 'required|string',
            ]);
			Log::info('Request Data', [
				'propertyId' => $request->input('propertyId'),
				'areaHomogeneaId' => $request->input('areaHomogeneaId'),
				'startDate' => $request->input('startDate'),
				'endDate' => $request->input('endDate'),
			]);

            $this->propertyId = $request->input('propertyId');
            $this->areaHomogeneaId = $request->input('areaHomogeneaId');
            $startDateInput = $request->input('startDate');
            $endDateInput = $request->input('endDate');

            // Conversão de datas para o formato correto
            $months = [
                'Janeiro' => 'January',
				'Fevereiro' => 'February',
				'Março' => 'March',
				'Abril' => 'April',
                'Maio' => 'May',
				'Junho' => 'June',
				'Julho' => 'July',
				'Agosto' => 'August',
                'Setembro' => 'September',
				'Outubro' => 'October',
				'Novembro' => 'November',
				'Dezembro' => 'December'
            ];

            // Converter o mês de startDate
			list($startMonth, $startYear) = explode('/', $startDateInput);
			$startMonthEnglish = $months[$startMonth] ?? null;
			if (!$startMonthEnglish) {
				throw new \Exception("Mês de início inválido: $startMonth");
			}
			$this->startDate = Carbon::createFromFormat('F/Y', $startMonthEnglish . '/' . $startYear)->startOfMonth();

			// Converter o mês de endDate
			list($endMonth, $endYear) = explode('/', $endDateInput);
			$endMonthEnglish = $months[$endMonth] ?? null;
			if (!$endMonthEnglish) {
				throw new \Exception("Mês de fim inválido: $endMonth");
			}
			$this->endDate = Carbon::createFromFormat('F/Y', $endMonthEnglish . '/' . $endYear)->endOfMonth();

			Log::info('Datas convertidas:', ['startDate' => $this->startDate->format('Y-m-d'), 'endDate' => $this->endDate->format('Y-m-d')]);

            // Filtrar Sampling Points com base no propertyId
			$sampling_points = SamplingPoint::whereHas('stratum', function ($query) {
				$query->where('property_id', $this->propertyId);
			})->get();

			// Extrair os IDs dos pontos amostrais
			$samplingPointIds = $sampling_points->pluck('id');

			// Fazer uma única query para buscar todas as visitas
			$all_tree_visits = TreeVisit::join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->whereIn('trees.sampling_point_id', $samplingPointIds)
				->where('homogeneous_areas.id', '=', $this->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$this->startDate, $this->endDate])
				->select(
					'tree_visits.*',
					'trees.id as tree_id',
					'sampling_points.id as sampling_point_id',
					'strata.id as stratum_id',
					'homogeneous_areas.id as homogeneous_area_id'
				)
				->get();

			// Calcular o total de frutos com vassoura-de-bruxa por data
			$totalPerDate = [];

			foreach ($all_tree_visits as $visit) {
				$date = Carbon::parse($visit->date)->format('Y-m-d');

				$totalWitchBroom =
					($visit->small['witchs_broom'] ?? 0) +
					($visit->medium2['witchs_broom'] ?? 0) +
					($visit->medium3['witchs_broom'] ?? 0) +
					($visit->adult['witchs_broom'] ?? 0) +
					($visit->adult2['witchs_broom'] ?? 0) +
					($visit->mature['witchs_broom'] ?? 0) +
					($visit->mature2['witchs_broom'] ?? 0) +
					($visit->mature3['witchs_broom'] ?? 0) +
					($visit->mature4['witchs_broom'] ?? 0);

				if (!isset($totalPerDate[$date])) {
					$totalPerDate[$date] = 0;
				}

				$totalPerDate[$date] += $totalWitchBroom;
			}

			return response()->json([
				'areaId' => $this->areaHomogeneaId,
				'totals' => $totalPerDate
			]);

        } catch (\Exception $e) {
            Log::error('Erro ao buscar dados de coleta:', ['error_message' => $e->getMessage()]);
            return response()->json(['error' => 'Erro ao obter os dados'], 500);
        }

    }

	public function getCollectionDataRotten(Request $request)
    {


        try {
            $request->validate([
                'propertyId' => 'required|integer|exists:properties,id',
                'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
                'startDate' => 'required|string',
                'endDate' => 'required|string',
            ]);
			Log::info('Request Data', [
				'propertyId' => $request->input('propertyId'),
				'areaHomogeneaId' => $request->input('areaHomogeneaId'),
				'startDate' => $request->input('startDate'),
				'endDate' => $request->input('endDate'),
			]);

            $this->propertyId = $request->input('propertyId');
            $this->areaHomogeneaId = $request->input('areaHomogeneaId');
            $startDateInput = $request->input('startDate');
            $endDateInput = $request->input('endDate');

            // Conversão de datas para o formato correto
            $months = [
                'Janeiro' => 'January',
				'Fevereiro' => 'February',
				'Março' => 'March',
				'Abril' => 'April',
                'Maio' => 'May',
				'Junho' => 'June',
				'Julho' => 'July',
				'Agosto' => 'August',
                'Setembro' => 'September',
				'Outubro' => 'October',
				'Novembro' => 'November',
				'Dezembro' => 'December'
            ];

            // Converter o mês de startDate
			list($startMonth, $startYear) = explode('/', $startDateInput);
			$startMonthEnglish = $months[$startMonth] ?? null;
			if (!$startMonthEnglish) {
				throw new \Exception("Mês de início inválido: $startMonth");
			}
			$this->startDate = Carbon::createFromFormat('F/Y', $startMonthEnglish . '/' . $startYear)->startOfMonth();

			// Converter o mês de endDate
			list($endMonth, $endYear) = explode('/', $endDateInput);
			$endMonthEnglish = $months[$endMonth] ?? null;
			if (!$endMonthEnglish) {
				throw new \Exception("Mês de fim inválido: $endMonth");
			}
			$this->endDate = Carbon::createFromFormat('F/Y', $endMonthEnglish . '/' . $endYear)->endOfMonth();

			Log::info('Datas convertidas:', ['startDate' => $this->startDate->format('Y-m-d'), 'endDate' => $this->endDate->format('Y-m-d')]);

            // Filtrar Sampling Points com base no propertyId
			$sampling_points = SamplingPoint::whereHas('stratum', function ($query) {
				$query->where('property_id', $this->propertyId);
			})->get();

			// Extrair os IDs dos pontos amostrais
			$samplingPointIds = $sampling_points->pluck('id');

			// Fazer uma única query para buscar todas as visitas
			$all_tree_visits = TreeVisit::join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->whereIn('trees.sampling_point_id', $samplingPointIds)
				->where('homogeneous_areas.id', '=', $this->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$this->startDate, $this->endDate])
				->select(
					'tree_visits.*',
					'trees.id as tree_id',
					'sampling_points.id as sampling_point_id',
					'strata.id as stratum_id',
					'homogeneous_areas.id as homogeneous_area_id'
				)
				->get();

			// Calcular o total de frutos podres por data
			$totalPerDate = [];

			foreach ($all_tree_visits as $visit) {
				$date = Carbon::parse($visit->date)->format('Y-m-d');

				$totalRotten =
					($visit->medium2['rotten'] ?? 0) +
					($visit->medium3['rotten'] ?? 0) +
					($visit->adult['rotten'] ?? 0) +
					($visit->adult2['rotten'] ?? 0) +
					($visit->mature['rotten'] ?? 0) +
					($visit->mature2['rotten'] ?? 0) +
					($visit->mature3['rotten'] ?? 0) +
					($visit->mature4['rotten'] ?? 0);

				if (!isset($totalPerDate[$date])) {
					$totalPerDate[$date] = 0;
				}

				$totalPerDate[$date] += $totalRotten;
			}

			return response()->json([
				'areaId' => $this->areaHomogeneaId,
				'totals' => $totalPerDate
			]);


        } catch (\Exception $e) {
            Log::error('Erro ao buscar dados de coleta:', ['error_message' => $e->getMessage()]);
            return response()->json(['error' => 'Erro ao obter os dados'], 500);
        }

    }

	public function getCollectionDataPiece(Request $request)
    {


        try {
            $request->validate([
                'propertyId' => 'required|integer|exists:properties,id',
                'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
                'startDate' => 'required|string',
                'endDate' => 'required|string',
            ]);
			Log::info('Request Data', [
				'propertyId' => $request->input('propertyId'),
				'areaHomogeneaId' => $request->input('areaHomogeneaId'),
				'startDate' => $request->input('startDate'),
				'endDate' => $request->input('endDate'),
			]);

            $this->propertyId = $request->input('propertyId');
            $this->areaHomogeneaId = $request->input('areaHomogeneaId');
            $startDateInput = $request->input('startDate');
            $endDateInput = $request->input('endDate');

            // Conversão de datas para o formato correto
            $months = [
                'Janeiro' => 'January',
				'Fevereiro' => 'February',
				'Março' => 'March',
				'Abril' => 'April',
                'Maio' => 'May',
				'Junho' => 'June',
				'Julho' => 'July',
				'Agosto' => 'August',
                'Setembro' => 'September',
				'Outubro' => 'October',
				'Novembro' => 'November',
				'Dezembro' => 'December'
            ];

            // Converter o mês de startDate
			list($startMonth, $startYear) = explode('/', $startDateInput);
			$startMonthEnglish = $months[$startMonth] ?? null;
			if (!$startMonthEnglish) {
				throw new \Exception("Mês de início inválido: $startMonth");
			}
			$this->startDate = Carbon::createFromFormat('F/Y', $startMonthEnglish . '/' . $startYear)->startOfMonth();

			// Converter o mês de endDate
			list($endMonth, $endYear) = explode('/', $endDateInput);
			$endMonthEnglish = $months[$endMonth] ?? null;
			if (!$endMonthEnglish) {
				throw new \Exception("Mês de fim inválido: $endMonth");
			}
			$this->endDate = Carbon::createFromFormat('F/Y', $endMonthEnglish . '/' . $endYear)->endOfMonth();

			Log::info('Datas convertidas:', ['startDate' => $this->startDate->format('Y-m-d'), 'endDate' => $this->endDate->format('Y-m-d')]);

            // Filtrar Sampling Points com base no propertyId
			$sampling_points = SamplingPoint::whereHas('stratum', function ($query) {
				$query->where('property_id', $this->propertyId);
			})->get();

			// Extrair os IDs dos pontos amostrais
			$samplingPointIds = $sampling_points->pluck('id');

			// Fazer uma única query para buscar todas as visitas
			$all_tree_visits = TreeVisit::join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->whereIn('trees.sampling_point_id', $samplingPointIds)
				->where('homogeneous_areas.id', '=', $this->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$this->startDate, $this->endDate])
				->select(
					'tree_visits.*',
					'trees.id as tree_id',
					'sampling_points.id as sampling_point_id',
					'strata.id as stratum_id',
					'homogeneous_areas.id as homogeneous_area_id'
				)
				->get();

			// Calcular o total de frutos pecos por data
			$totalPerDate = [];

			foreach ($all_tree_visits as $visit) {
				$date = Carbon::parse($visit->date)->format('Y-m-d');

				$totalPiece =
					($visit->small['piece'] ?? 0) +
					($visit->medium['piece'] ?? 0) +
					($visit->medium2['piece'] ?? 0) +
					($visit->medium3['piece'] ?? 0);

				if (!isset($totalPerDate[$date])) {
					$totalPerDate[$date] = 0;
				}

				$totalPerDate[$date] += $totalPiece;
			}

			return response()->json([
				'areaId' => $this->areaHomogeneaId,
				'totals' => $totalPerDate
			]);

        } catch (\Exception $e) {
            Log::error('Erro ao buscar dados de coleta:', ['error_message' => $e->getMessage()]);
            return response()->json(['error' => 'Erro ao obter os dados'], 500);
        }

    }

	public function getCollectionDataRat(Request $request)
    {


        try {
            $request->validate([
                'propertyId' => 'required|integer|exists:properties,id',
                'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
                'startDate' => 'required|string',
                'endDate' => 'required|string',
            ]);
			Log::info('Request Data', [
				'propertyId' => $request->input('propertyId'),
				'areaHomogeneaId' => $request->input('areaHomogeneaId'),
				'startDate' => $request->input('startDate'),
				'endDate' => $request->input('endDate'),
			]);

            $this->propertyId = $request->input('propertyId');
            $this->areaHomogeneaId = $request->input('areaHomogeneaId');
            $startDateInput = $request->input('startDate');
            $endDateInput = $request->input('endDate');

            // Conversão de datas para o formato correto
            $months = [
                'Janeiro' => 'January',
				'Fevereiro' => 'February',
				'Março' => 'March',
				'Abril' => 'April',
                'Maio' => 'May',
				'Junho' => 'June',
				'Julho' => 'July',
				'Agosto' => 'August',
                'Setembro' => 'September',
				'Outubro' => 'October',
				'Novembro' => 'November',
				'Dezembro' => 'December'
            ];

            // Converter o mês de startDate
			list($startMonth, $startYear) = explode('/', $startDateInput);
			$startMonthEnglish = $months[$startMonth] ?? null;
			if (!$startMonthEnglish) {
				throw new \Exception("Mês de início inválido: $startMonth");
			}
			$this->startDate = Carbon::createFromFormat('F/Y', $startMonthEnglish . '/' . $startYear)->startOfMonth();

			// Converter o mês de endDate
			list($endMonth, $endYear) = explode('/', $endDateInput);
			$endMonthEnglish = $months[$endMonth] ?? null;
			if (!$endMonthEnglish) {
				throw new \Exception("Mês de fim inválido: $endMonth");
			}
			$this->endDate = Carbon::createFromFormat('F/Y', $endMonthEnglish . '/' . $endYear)->endOfMonth();

			Log::info('Datas convertidas:', ['startDate' => $this->startDate->format('Y-m-d'), 'endDate' => $this->endDate->format('Y-m-d')]);

            // Filtrar Sampling Points com base no propertyId
			$sampling_points = SamplingPoint::whereHas('stratum', function ($query) {
				$query->where('property_id', $this->propertyId);
			})->get();

			// Extrair os IDs dos pontos amostrais
			$samplingPointIds = $sampling_points->pluck('id');

			// Fazer uma única query para buscar todas as visitas
			$all_tree_visits = TreeVisit::join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->whereIn('trees.sampling_point_id', $samplingPointIds)
				->where('homogeneous_areas.id', '=', $this->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$this->startDate, $this->endDate])
				->select(
					'tree_visits.*',
					'trees.id as tree_id',
					'sampling_points.id as sampling_point_id',
					'strata.id as stratum_id',
					'homogeneous_areas.id as homogeneous_area_id'
				)
				->get();

			// Calcular o total de frutos comidos por ratos por data
			$totalPerDate = [];

			foreach ($all_tree_visits as $visit) {
				$date = Carbon::parse($visit->date)->format('Y-m-d');

				$totalRat =
					($visit->medium2['rat'] ?? 0) +
					($visit->medium3['rat'] ?? 0) +
					($visit->adult['rat'] ?? 0) +
					($visit->adult2['rat'] ?? 0) +
					($visit->mature['rat'] ?? 0) +
					($visit->mature2['rat'] ?? 0) +
					($visit->mature3['rat'] ?? 0) +
					($visit->mature4['rat'] ?? 0);

				if (!isset($totalPerDate[$date])) {
					$totalPerDate[$date] = 0;
				}

				$totalPerDate[$date] += $totalRat;
			}

			return response()->json([
				'areaId' => $this->areaHomogeneaId,
				'totals' => $totalPerDate
			]);

        } catch (\Exception $e) {
            Log::error('Erro ao buscar dados de coleta:', ['error_message' => $e->getMessage()]);
            return response()->json(['error' => 'Erro ao obter os dados'], 500);
        }

    }

	public function getCollectionDataHarvestedHA(Request $request)
    {


        try {
            $request->validate([
                'propertyId' => 'required|integer|exists:properties,id',
                'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
                'startDate' => 'required|string',
                'endDate' => 'required|string',
            ]);
			Log::info('Request Data', [
				'propertyId' => $request->input('propertyId'),
				'areaHomogeneaId' => $request->input('areaHomogeneaId'),
				'startDate' => $request->input('startDate'),
				'endDate' => $request->input('endDate'),
			]);

            $this->propertyId = $request->input('propertyId');
            $this->areaHomogeneaId = $request->input('areaHomogeneaId');
            $startDateInput = $request->input('startDate');
            $endDateInput = $request->input('endDate');

            // Conversão de datas para o formato correto
            $months = [
                'Janeiro' => 'January',
				'Fevereiro' => 'February',
				'Março' => 'March',
				'Abril' => 'April',
                'Maio' => 'May',
				'Junho' => 'June',
				'Julho' => 'July',
				'Agosto' => 'August',
                'Setembro' => 'September',
				'Outubro' => 'October',
				'Novembro' => 'November',
				'Dezembro' => 'December'
            ];

            // Converter o mês de startDate
			list($startMonth, $startYear) = explode('/', $startDateInput);
			$startMonthEnglish = $months[$startMonth] ?? null;
			if (!$startMonthEnglish) {
				throw new \Exception("Mês de início inválido: $startMonth");
			}
			$this->startDate = Carbon::createFromFormat('F/Y', $startMonthEnglish . '/' . $startYear)->startOfMonth();

			// Converter o mês de endDate
			list($endMonth, $endYear) = explode('/', $endDateInput);
			$endMonthEnglish = $months[$endMonth] ?? null;
			if (!$endMonthEnglish) {
				throw new \Exception("Mês de fim inválido: $endMonth");
			}
			$this->endDate = Carbon::createFromFormat('F/Y', $endMonthEnglish . '/' . $endYear)->endOfMonth();

			Log::info('Datas convertidas:', ['startDate' => $this->startDate->format('Y-m-d'), 'endDate' => $this->endDate->format('Y-m-d')]);

            // Filtrar Sampling Points com base no propertyId
			$sampling_points = SamplingPoint::whereHas('stratum', function ($query) {
				$query->where('property_id', $this->propertyId);
			})->get();

			// Extrair os IDs dos pontos amostrais
			$samplingPointIds = $sampling_points->pluck('id');

			// Fazer uma única query para buscar todas as visitas
			$all_tree_visits = TreeVisit::join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->whereIn('trees.sampling_point_id', $samplingPointIds)
				->where('homogeneous_areas.id', '=', $this->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$this->startDate, $this->endDate])
				->select(
					'tree_visits.*',
					'trees.id as tree_id',
					'sampling_points.id as sampling_point_id',
					'strata.id as stratum_id',
					'homogeneous_areas.id as homogeneous_area_id'
				)
				->get();

			$totalPerDayMonth = [];

			foreach ($all_tree_visits as $visit) {
				$date = Carbon::parse($visit->date)->format('d/m');

				$totalHarvested =
					($visit->medium3['harvested'] ?? 0) +
					($visit->adult['harvested'] ?? 0) +
					($visit->adult2['harvested'] ?? 0) +
					($visit->mature['harvested'] ?? 0) +
					($visit->mature2['harvested'] ?? 0) +
					($visit->mature3['harvested'] ?? 0) +
					($visit->mature4['harvested'] ?? 0);

				if (!isset($totalPerDayMonth[$date])) {
					$totalPerDayMonth[$date] = 0;
				}

				$totalPerDayMonth[$date] += $totalHarvested;
			}

			// Ordenar as datas em ordem ajustada (outubro em diante primeiro)
			uksort($totalPerDayMonth, function ($a, $b) {
				[$dayA, $monthA] = explode('/', $a);
				[$dayB, $monthB] = explode('/', $b);

				$monthA = (int)$monthA;
				$monthB = (int)$monthB;

				$adjustedA = $monthA >= 10 ? $monthA - 12 : $monthA;
				$adjustedB = $monthB >= 10 ? $monthB - 12 : $monthB;

				if ($adjustedA === $adjustedB) {
					return (int)$dayA - (int)$dayB;
				}

				return $adjustedA - $adjustedB;
			});

			// Retornar apenas os totais ordenados (sem a chave de data, como era feito no frontend)
			return response()->json(array_values($totalPerDayMonth));


        } catch (\Exception $e) {
            Log::error('Erro ao buscar dados de coleta:', ['error_message' => $e->getMessage()]);
            return response()->json(['error' => 'Erro ao obter os dados'], 500);
        }

    }

		public function getCollectionDataLossHA(Request $request)
		{


			try {
				$request->validate([
					'propertyId' => 'required|integer|exists:properties,id',
					'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
					'startDate' => 'required|string',
					'endDate' => 'required|string',
				]);
				Log::info('Request Data', [
					'propertyId' => $request->input('propertyId'),
					'areaHomogeneaId' => $request->input('areaHomogeneaId'),
					'startDate' => $request->input('startDate'),
					'endDate' => $request->input('endDate'),
				]);

				$this->propertyId = $request->input('propertyId');
				$this->areaHomogeneaId = $request->input('areaHomogeneaId');
				$startDateInput = $request->input('startDate');
				$endDateInput = $request->input('endDate');

				// Conversão de datas para o formato correto
				$months = [
					'Janeiro' => 'January',
					'Fevereiro' => 'February',
					'Março' => 'March',
					'Abril' => 'April',
					'Maio' => 'May',
					'Junho' => 'June',
					'Julho' => 'July',
					'Agosto' => 'August',
					'Setembro' => 'September',
					'Outubro' => 'October',
					'Novembro' => 'November',
					'Dezembro' => 'December'
				];

				// Converter o mês de startDate
				list($startMonth, $startYear) = explode('/', $startDateInput);
				$startMonthEnglish = $months[$startMonth] ?? null;
				if (!$startMonthEnglish) {
					throw new \Exception("Mês de início inválido: $startMonth");
				}
				$this->startDate = Carbon::createFromFormat('F/Y', $startMonthEnglish . '/' . $startYear)->startOfMonth();

				// Converter o mês de endDate
				list($endMonth, $endYear) = explode('/', $endDateInput);
				$endMonthEnglish = $months[$endMonth] ?? null;
				if (!$endMonthEnglish) {
					throw new \Exception("Mês de fim inválido: $endMonth");
				}
				$this->endDate = Carbon::createFromFormat('F/Y', $endMonthEnglish . '/' . $endYear)->endOfMonth();

				Log::info('Datas convertidas:', ['startDate' => $this->startDate->format('Y-m-d'), 'endDate' => $this->endDate->format('Y-m-d')]);

				// Filtrar Sampling Points com base no propertyId
				$sampling_points = SamplingPoint::whereHas('stratum', function ($query) {
					$query->where('property_id', $this->propertyId);
				})->get();

				// Extrair os IDs dos pontos amostrais
				$samplingPointIds = $sampling_points->pluck('id');

				// Fazer uma única query para buscar todas as visitas
				$all_tree_visits = TreeVisit::join('trees', 'trees.id', '=', 'tree_visits.tree_id')
					->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
					->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
					->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
					->whereIn('trees.sampling_point_id', $samplingPointIds)
					->where('homogeneous_areas.id', '=', $this->areaHomogeneaId)
					->whereBetween('tree_visits.date', [$this->startDate, $this->endDate])
					->select(
						'tree_visits.*',
						'trees.id as tree_id',
						'sampling_points.id as sampling_point_id',
						'strata.id as stratum_id',
						'homogeneous_areas.id as homogeneous_area_id'
					)
					->get();

				$totalLossPerDay = [];

				foreach ($all_tree_visits as $visit) {
					$date = Carbon::parse($visit->date)->format('d/m');

					$totalLoss =
						($visit->small['loss'] ?? 0) +
						($visit->medium['loss'] ?? 0) +
						($visit->medium2['loss'] ?? 0) +
						($visit->medium3['loss'] ?? 0) +
						($visit->adult['loss'] ?? 0) +
						($visit->adult2['loss'] ?? 0) +
						($visit->mature['loss'] ?? 0) +
						($visit->mature2['loss'] ?? 0) +
						($visit->mature3['loss'] ?? 0) +
						($visit->mature4['loss'] ?? 0);

					if (!isset($totalLossPerDay[$date])) {
						$totalLossPerDay[$date] = 0;
					}

					$totalLossPerDay[$date] += $totalLoss;
				}

				// Ordenar as datas no padrão da safra (outubro → setembro)
				uksort($totalLossPerDay, function ($a, $b) {
					[$dayA, $monthA] = explode('/', $a);
					[$dayB, $monthB] = explode('/', $b);

					$adjustedA = ((int)$monthA >= 10) ? ((int)$monthA - 12) : (int)$monthA;
					$adjustedB = ((int)$monthB >= 10) ? ((int)$monthB - 12) : (int)$monthB;

					return $adjustedA === $adjustedB ? (int)$dayA - (int)$dayB : $adjustedA - $adjustedB;
				});

				// Retorna só os totais ordenados (sem datas)
				return response()->json(array_values($totalLossPerDay));


			} catch (\Exception $e) {
				Log::error('Erro ao buscar dados de coleta:', ['error_message' => $e->getMessage()]);
				return response()->json(['error' => 'Erro ao obter os dados'], 500);
			}

		}

		public function getCollectionDataWBHA(Request $request)
		{


			try {
				$request->validate([
					'propertyId' => 'required|integer|exists:properties,id',
					'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
					'startDate' => 'required|string',
					'endDate' => 'required|string',
				]);
				Log::info('Request Data', [
					'propertyId' => $request->input('propertyId'),
					'areaHomogeneaId' => $request->input('areaHomogeneaId'),
					'startDate' => $request->input('startDate'),
					'endDate' => $request->input('endDate'),
				]);

				$this->propertyId = $request->input('propertyId');
				$this->areaHomogeneaId = $request->input('areaHomogeneaId');
				$startDateInput = $request->input('startDate');
				$endDateInput = $request->input('endDate');

				// Conversão de datas para o formato correto
				$months = [
					'Janeiro' => 'January',
					'Fevereiro' => 'February',
					'Março' => 'March',
					'Abril' => 'April',
					'Maio' => 'May',
					'Junho' => 'June',
					'Julho' => 'July',
					'Agosto' => 'August',
					'Setembro' => 'September',
					'Outubro' => 'October',
					'Novembro' => 'November',
					'Dezembro' => 'December'
				];

				// Converter o mês de startDate
				list($startMonth, $startYear) = explode('/', $startDateInput);
				$startMonthEnglish = $months[$startMonth] ?? null;
				if (!$startMonthEnglish) {
					throw new \Exception("Mês de início inválido: $startMonth");
				}
				$this->startDate = Carbon::createFromFormat('F/Y', $startMonthEnglish . '/' . $startYear)->startOfMonth();

				// Converter o mês de endDate
				list($endMonth, $endYear) = explode('/', $endDateInput);
				$endMonthEnglish = $months[$endMonth] ?? null;
				if (!$endMonthEnglish) {
					throw new \Exception("Mês de fim inválido: $endMonth");
				}
				$this->endDate = Carbon::createFromFormat('F/Y', $endMonthEnglish . '/' . $endYear)->endOfMonth();

				Log::info('Datas convertidas:', ['startDate' => $this->startDate->format('Y-m-d'), 'endDate' => $this->endDate->format('Y-m-d')]);

				// Filtrar Sampling Points com base no propertyId
				$sampling_points = SamplingPoint::whereHas('stratum', function ($query) {
					$query->where('property_id', $this->propertyId);
				})->get();

				// Extrair os IDs dos pontos amostrais
				$samplingPointIds = $sampling_points->pluck('id');

				// Fazer uma única query para buscar todas as visitas
				$all_tree_visits = TreeVisit::join('trees', 'trees.id', '=', 'tree_visits.tree_id')
					->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
					->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
					->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
					->whereIn('trees.sampling_point_id', $samplingPointIds)
					->where('homogeneous_areas.id', '=', $this->areaHomogeneaId)
					->whereBetween('tree_visits.date', [$this->startDate, $this->endDate])
					->select(
						'tree_visits.*',
						'trees.id as tree_id',
						'sampling_points.id as sampling_point_id',
						'strata.id as stratum_id',
						'homogeneous_areas.id as homogeneous_area_id'
					)
					->get();

				$totalWBPerDay = [];

				foreach ($all_tree_visits as $visit) {
					$date = Carbon::parse($visit->date)->format('d/m');

					$totalWB =
						($visit->small['witchs_broom'] ?? 0) +
						($visit->medium2['witchs_broom'] ?? 0) +
						($visit->medium3['witchs_broom'] ?? 0) +
						($visit->adult['witchs_broom'] ?? 0) +
						($visit->adult2['witchs_broom'] ?? 0) +
						($visit->mature['witchs_broom'] ?? 0) +
						($visit->mature2['witchs_broom'] ?? 0) +
						($visit->mature3['witchs_broom'] ?? 0) +
						($visit->mature4['witchs_broom'] ?? 0);

					if (!isset($totalWBPerDay[$date])) {
						$totalWBPerDay[$date] = 0;
					}

					$totalWBPerDay[$date] += $totalWB;
				}

				// Ordenar no estilo safra (outubro–setembro)
				uksort($totalWBPerDay, function ($a, $b) {
					[$dayA, $monthA] = explode('/', $a);
					[$dayB, $monthB] = explode('/', $b);

					$adjustedA = ((int)$monthA >= 10) ? ((int)$monthA - 12) : (int)$monthA;
					$adjustedB = ((int)$monthB >= 10) ? ((int)$monthB - 12) : (int)$monthB;

					return $adjustedA === $adjustedB ? (int)$dayA - (int)$dayB : $adjustedA - $adjustedB;
				});

				return response()->json(array_values($totalWBPerDay));


			} catch (\Exception $e) {
				Log::error('Erro ao buscar dados de coleta:', ['error_message' => $e->getMessage()]);
				return response()->json(['error' => 'Erro ao obter os dados'], 500);
			}

		}

		public function getCollectionDataPieceHA(Request $request)
		{


			try {
				$request->validate([
					'propertyId' => 'required|integer|exists:properties,id',
					'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
					'startDate' => 'required|string',
					'endDate' => 'required|string',
				]);
				Log::info('Request Data', [
					'propertyId' => $request->input('propertyId'),
					'areaHomogeneaId' => $request->input('areaHomogeneaId'),
					'startDate' => $request->input('startDate'),
					'endDate' => $request->input('endDate'),
				]);

				$this->propertyId = $request->input('propertyId');
				$this->areaHomogeneaId = $request->input('areaHomogeneaId');
				$startDateInput = $request->input('startDate');
				$endDateInput = $request->input('endDate');

				// Conversão de datas para o formato correto
				$months = [
					'Janeiro' => 'January',
					'Fevereiro' => 'February',
					'Março' => 'March',
					'Abril' => 'April',
					'Maio' => 'May',
					'Junho' => 'June',
					'Julho' => 'July',
					'Agosto' => 'August',
					'Setembro' => 'September',
					'Outubro' => 'October',
					'Novembro' => 'November',
					'Dezembro' => 'December'
				];

				// Converter o mês de startDate
				list($startMonth, $startYear) = explode('/', $startDateInput);
				$startMonthEnglish = $months[$startMonth] ?? null;
				if (!$startMonthEnglish) {
					throw new \Exception("Mês de início inválido: $startMonth");
				}
				$this->startDate = Carbon::createFromFormat('F/Y', $startMonthEnglish . '/' . $startYear)->startOfMonth();

				// Converter o mês de endDate
				list($endMonth, $endYear) = explode('/', $endDateInput);
				$endMonthEnglish = $months[$endMonth] ?? null;
				if (!$endMonthEnglish) {
					throw new \Exception("Mês de fim inválido: $endMonth");
				}
				$this->endDate = Carbon::createFromFormat('F/Y', $endMonthEnglish . '/' . $endYear)->endOfMonth();

				Log::info('Datas convertidas:', ['startDate' => $this->startDate->format('Y-m-d'), 'endDate' => $this->endDate->format('Y-m-d')]);

				// Filtrar Sampling Points com base no propertyId
				$sampling_points = SamplingPoint::whereHas('stratum', function ($query) {
					$query->where('property_id', $this->propertyId);
				})->get();

				// Extrair os IDs dos pontos amostrais
				$samplingPointIds = $sampling_points->pluck('id');

				// Fazer uma única query para buscar todas as visitas
				$all_tree_visits = TreeVisit::join('trees', 'trees.id', '=', 'tree_visits.tree_id')
					->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
					->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
					->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
					->whereIn('trees.sampling_point_id', $samplingPointIds)
					->where('homogeneous_areas.id', '=', $this->areaHomogeneaId)
					->whereBetween('tree_visits.date', [$this->startDate, $this->endDate])
					->select(
						'tree_visits.*',
						'trees.id as tree_id',
						'sampling_points.id as sampling_point_id',
						'strata.id as stratum_id',
						'homogeneous_areas.id as homogeneous_area_id'
					)
					->get();

				$totalPiecePerDay = [];

				foreach ($all_tree_visits as $visit) {
					$date = Carbon::parse($visit->date)->format('d/m');

					$totalPiece =
						($visit->small['piece'] ?? 0) +
						($visit->medium['piece'] ?? 0) +
						($visit->medium2['piece'] ?? 0) +
						($visit->medium3['piece'] ?? 0);

					if (!isset($totalPiecePerDay[$date])) {
						$totalPiecePerDay[$date] = 0;
					}

					$totalPiecePerDay[$date] += $totalPiece;
				}

				// Ordenar no estilo safra (outubro a setembro)
				uksort($totalPiecePerDay, function ($a, $b) {
					[$dayA, $monthA] = explode('/', $a);
					[$dayB, $monthB] = explode('/', $b);

					$adjustedA = ((int)$monthA >= 10) ? ((int)$monthA - 12) : (int)$monthA;
					$adjustedB = ((int)$monthB >= 10) ? ((int)$monthB - 12) : (int)$monthB;

					return $adjustedA === $adjustedB ? (int)$dayA - (int)$dayB : $adjustedA - $adjustedB;
				});

				// Retornar apenas os totais ordenados
				return response()->json(array_values($totalPiecePerDay));


			} catch (\Exception $e) {
				Log::error('Erro ao buscar dados de coleta:', ['error_message' => $e->getMessage()]);
				return response()->json(['error' => 'Erro ao obter os dados'], 500);
			}

		}

		public function getCollectionDataRatHA(Request $request)
		{


			try {
				$request->validate([
					'propertyId' => 'required|integer|exists:properties,id',
					'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
					'startDate' => 'required|string',
					'endDate' => 'required|string',
				]);
				Log::info('Request Data', [
					'propertyId' => $request->input('propertyId'),
					'areaHomogeneaId' => $request->input('areaHomogeneaId'),
					'startDate' => $request->input('startDate'),
					'endDate' => $request->input('endDate'),
				]);

				$this->propertyId = $request->input('propertyId');
				$this->areaHomogeneaId = $request->input('areaHomogeneaId');
				$startDateInput = $request->input('startDate');
				$endDateInput = $request->input('endDate');

				// Conversão de datas para o formato correto
				$months = [
					'Janeiro' => 'January',
					'Fevereiro' => 'February',
					'Março' => 'March',
					'Abril' => 'April',
					'Maio' => 'May',
					'Junho' => 'June',
					'Julho' => 'July',
					'Agosto' => 'August',
					'Setembro' => 'September',
					'Outubro' => 'October',
					'Novembro' => 'November',
					'Dezembro' => 'December'
				];

				// Converter o mês de startDate
				list($startMonth, $startYear) = explode('/', $startDateInput);
				$startMonthEnglish = $months[$startMonth] ?? null;
				if (!$startMonthEnglish) {
					throw new \Exception("Mês de início inválido: $startMonth");
				}
				$this->startDate = Carbon::createFromFormat('F/Y', $startMonthEnglish . '/' . $startYear)->startOfMonth();

				// Converter o mês de endDate
				list($endMonth, $endYear) = explode('/', $endDateInput);
				$endMonthEnglish = $months[$endMonth] ?? null;
				if (!$endMonthEnglish) {
					throw new \Exception("Mês de fim inválido: $endMonth");
				}
				$this->endDate = Carbon::createFromFormat('F/Y', $endMonthEnglish . '/' . $endYear)->endOfMonth();

				Log::info('Datas convertidas:', ['startDate' => $this->startDate->format('Y-m-d'), 'endDate' => $this->endDate->format('Y-m-d')]);

				// Filtrar Sampling Points com base no propertyId
				$sampling_points = SamplingPoint::whereHas('stratum', function ($query) {
					$query->where('property_id', $this->propertyId);
				})->get();

				// Extrair os IDs dos pontos amostrais
				$samplingPointIds = $sampling_points->pluck('id');

				// Fazer uma única query para buscar todas as visitas
				$all_tree_visits = TreeVisit::join('trees', 'trees.id', '=', 'tree_visits.tree_id')
					->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
					->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
					->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
					->whereIn('trees.sampling_point_id', $samplingPointIds)
					->where('homogeneous_areas.id', '=', $this->areaHomogeneaId)
					->whereBetween('tree_visits.date', [$this->startDate, $this->endDate])
					->select(
						'tree_visits.*',
						'trees.id as tree_id',
						'sampling_points.id as sampling_point_id',
						'strata.id as stratum_id',
						'homogeneous_areas.id as homogeneous_area_id'
					)
					->get();

				$totalRatPerDay = [];

				foreach ($all_tree_visits as $visit) {
					$date = Carbon::parse($visit->date)->format('d/m');

					$totalRat =
						($visit->medium2['rat'] ?? 0) +
						($visit->medium3['rat'] ?? 0) +
						($visit->adult['rat'] ?? 0) +
						($visit->adult2['rat'] ?? 0) +
						($visit->mature['rat'] ?? 0) +
						($visit->mature2['rat'] ?? 0) +
						($visit->mature3['rat'] ?? 0) +
						($visit->mature4['rat'] ?? 0);

					if (!isset($totalRatPerDay[$date])) {
						$totalRatPerDay[$date] = 0;
					}

					$totalRatPerDay[$date] += $totalRat;
				}

				// Ordenar para manter a ordem das coletas com base na lógica safra (outubro primeiro)
				uksort($totalRatPerDay, function ($a, $b) {
					[$dayA, $monthA] = explode('/', $a);
					[$dayB, $monthB] = explode('/', $b);

					$adjustedMonthA = ((int)$monthA >= 10) ? ((int)$monthA - 12) : (int)$monthA;
					$adjustedMonthB = ((int)$monthB >= 10) ? ((int)$monthB - 12) : (int)$monthB;

					return $adjustedMonthA === $adjustedMonthB ? (int)$dayA - (int)$dayB : $adjustedMonthA - $adjustedMonthB;
				});

				// Retornar somente os totais em ordem
				return response()->json(array_values($totalRatPerDay));


			} catch (\Exception $e) {
				Log::error('Erro ao buscar dados de coleta:', ['error_message' => $e->getMessage()]);
				return response()->json(['error' => 'Erro ao obter os dados'], 500);
			}

		}

		public function getCollectionDataRottenHA(Request $request)
		{


			try {
				$request->validate([
					'propertyId' => 'required|integer|exists:properties,id',
					'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
					'startDate' => 'required|string',
					'endDate' => 'required|string',
				]);
				Log::info('Request Data', [
					'propertyId' => $request->input('propertyId'),
					'areaHomogeneaId' => $request->input('areaHomogeneaId'),
					'startDate' => $request->input('startDate'),
					'endDate' => $request->input('endDate'),
				]);

				$this->propertyId = $request->input('propertyId');
				$this->areaHomogeneaId = $request->input('areaHomogeneaId');
				$startDateInput = $request->input('startDate');
				$endDateInput = $request->input('endDate');

				// Conversão de datas para o formato correto
				$months = [
					'Janeiro' => 'January',
					'Fevereiro' => 'February',
					'Março' => 'March',
					'Abril' => 'April',
					'Maio' => 'May',
					'Junho' => 'June',
					'Julho' => 'July',
					'Agosto' => 'August',
					'Setembro' => 'September',
					'Outubro' => 'October',
					'Novembro' => 'November',
					'Dezembro' => 'December'
				];

				// Converter o mês de startDate
				list($startMonth, $startYear) = explode('/', $startDateInput);
				$startMonthEnglish = $months[$startMonth] ?? null;
				if (!$startMonthEnglish) {
					throw new \Exception("Mês de início inválido: $startMonth");
				}
				$this->startDate = Carbon::createFromFormat('F/Y', $startMonthEnglish . '/' . $startYear)->startOfMonth();

				// Converter o mês de endDate
				list($endMonth, $endYear) = explode('/', $endDateInput);
				$endMonthEnglish = $months[$endMonth] ?? null;
				if (!$endMonthEnglish) {
					throw new \Exception("Mês de fim inválido: $endMonth");
				}
				$this->endDate = Carbon::createFromFormat('F/Y', $endMonthEnglish . '/' . $endYear)->endOfMonth();

				Log::info('Datas convertidas:', ['startDate' => $this->startDate->format('Y-m-d'), 'endDate' => $this->endDate->format('Y-m-d')]);

				// Filtrar Sampling Points com base no propertyId
				$sampling_points = SamplingPoint::whereHas('stratum', function ($query) {
					$query->where('property_id', $this->propertyId);
				})->get();

				// Extrair os IDs dos pontos amostrais
				$samplingPointIds = $sampling_points->pluck('id');

				// Fazer uma única query para buscar todas as visitas
				$all_tree_visits = TreeVisit::join('trees', 'trees.id', '=', 'tree_visits.tree_id')
					->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
					->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
					->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
					->whereIn('trees.sampling_point_id', $samplingPointIds)
					->where('homogeneous_areas.id', '=', $this->areaHomogeneaId)
					->whereBetween('tree_visits.date', [$this->startDate, $this->endDate])
					->select(
						'tree_visits.*',
						'trees.id as tree_id',
						'sampling_points.id as sampling_point_id',
						'strata.id as stratum_id',
						'homogeneous_areas.id as homogeneous_area_id'
					)
					->get();

				$totalRottenPerDay = [];

				foreach ($all_tree_visits as $visit) {
					$date = Carbon::parse($visit->date)->format('d/m');

					$totalRotten =
						($visit->medium2['rotten'] ?? 0) +
						($visit->medium3['rotten'] ?? 0) +
						($visit->adult['rotten'] ?? 0) +
						($visit->adult2['rotten'] ?? 0) +
						($visit->mature['rotten'] ?? 0) +
						($visit->mature2['rotten'] ?? 0) +
						($visit->mature3['rotten'] ?? 0) +
						($visit->mature4['rotten'] ?? 0);

					if (!isset($totalRottenPerDay[$date])) {
						$totalRottenPerDay[$date] = 0;
					}

					$totalRottenPerDay[$date] += $totalRotten;
				}

				// Ordenar para que outubro a dezembro venham antes
				uksort($totalRottenPerDay, function ($a, $b) {
					[$dayA, $monthA] = explode('/', $a);
					[$dayB, $monthB] = explode('/', $b);

					$adjustedMonthA = ((int)$monthA >= 10) ? ((int)$monthA - 12) : (int)$monthA;
					$adjustedMonthB = ((int)$monthB >= 10) ? ((int)$monthB - 12) : (int)$monthB;

					return $adjustedMonthA === $adjustedMonthB ? (int)$dayA - (int)$dayB : $adjustedMonthA - $adjustedMonthB;
				});

				return response()->json(array_values($totalRottenPerDay));

			} catch (\Exception $e) {
				Log::error('Erro ao buscar dados de coleta:', ['error_message' => $e->getMessage()]);
				return response()->json(['error' => 'Erro ao obter os dados'], 500);
			}

		}
}
