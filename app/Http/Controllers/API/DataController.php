<?php

namespace App\Http\Controllers\API;

use PDF;
use Maatwebsite\Excel\Facades\Excel;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;

use App\Exports\ExcelExport;
use App\Imports\ExcelImport;
use App\Exports\BlockExport;
use App\Exports\PracticeExport;
use App\Exports\ExcelPropertyExport;
use App\Exports\ModelExport;

use App\Jobs\ImportSpreadSheet;

use App\User;
use App\Models\Stratum;
use App\Models\Property;
use App\Models\TreeVisit;
use App\Models\SamplingPoint;
use Illuminate\Support\Facades\Auth;
use App\Models\Collection;
use App\Http\Resources\TreeVisitResource;

set_time_limit(0);

class DataController extends BaseController
{
	private $propertyId;
    private $startDate;
    private $endDate;
	private $areaHomogeneaId;

	public function getCollectionData(Request $request)
    {


        try {
            $request->validate([
                'propertyId' => 'required|integer|exists:properties,id',
                'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
                'startDate' => 'required|string',
                'endDate' => 'required|string',
            ]);
			Log::info('Request Data', [
				'propertyId' => $request->input('propertyId'),
				'areaHomogeneaId' => $request->input('areaHomogeneaId'),
				'startDate' => $request->input('startDate'),
				'endDate' => $request->input('endDate'),
			]);

            $this->propertyId = $request->input('propertyId');
            $this->areaHomogeneaId = $request->input('areaHomogeneaId');
            $startDateInput = $request->input('startDate');
            $endDateInput = $request->input('endDate');

            // Conversão de datas para o formato correto
            $months = [
                'Janeiro' => 'January',
				'Fevereiro' => 'February',
				'Março' => 'March',
				'Abril' => 'April',
                'Maio' => 'May',
				'Junho' => 'June',
				'Julho' => 'July',
				'Agosto' => 'August',
                'Setembro' => 'September',
				'Outubro' => 'October',
				'Novembro' => 'November',
				'Dezembro' => 'December'
            ];

            // Converter o mês de startDate
			list($startMonth, $startYear) = explode('/', $startDateInput);
			$startMonthEnglish = $months[$startMonth] ?? null;
			if (!$startMonthEnglish) {
				throw new \Exception("Mês de início inválido: $startMonth");
			}
			$this->startDate = Carbon::createFromFormat('F/Y', $startMonthEnglish . '/' . $startYear)->startOfMonth();

			// Converter o mês de endDate
			list($endMonth, $endYear) = explode('/', $endDateInput);
			$endMonthEnglish = $months[$endMonth] ?? null;
			if (!$endMonthEnglish) {
				throw new \Exception("Mês de fim inválido: $endMonth");
			}
			$this->endDate = Carbon::createFromFormat('F/Y', $endMonthEnglish . '/' . $endYear)->endOfMonth();

			Log::info('Datas convertidas:', ['startDate' => $this->startDate->format('Y-m-d'), 'endDate' => $this->endDate->format('Y-m-d')]);

            // Filtrar Sampling Points com base no propertyId
			$sampling_points = SamplingPoint::whereHas('stratum', function ($query) {
				$query->where('property_id', $this->propertyId);
			})->get();

			// Extrair os IDs dos pontos amostrais
			$samplingPointIds = $sampling_points->pluck('id');

			// Fazer uma única query para buscar todas as visitas
			$all_tree_visits = TreeVisit::join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->whereIn('trees.sampling_point_id', $samplingPointIds)
				->where('homogeneous_areas.id', '=', $this->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$this->startDate, $this->endDate])
				->select(
					'tree_visits.*',
					'trees.id as tree_id',
					'sampling_points.id as sampling_point_id',
					'strata.id as stratum_id',
					'homogeneous_areas.id as homogeneous_area_id'
				)
				->get();

			// Logar quantidade
			Log::info('Total tree visits processed:', [
				'total_visits' => $all_tree_visits->count(),
			]);

			// Converter os dados
			$data = TreeVisitResource::collection($all_tree_visits);


        	return response()->json($data);

        } catch (\Exception $e) {
            Log::error('Erro ao buscar dados de coleta:', ['error_message' => $e->getMessage()]);
            return response()->json(['error' => 'Erro ao obter os dados'], 500);
        }

    }

	public function getCollectionDataMultiArea(Request $request)
	{
		try {
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaIds' => 'required|array|min:1',
				'areaHomogeneaIds.*' => 'integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			$monthsMap = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June', 'Julho' => 'July',
				'Agosto' => 'August', 'Setembro' => 'September', 'Outubro' => 'October',
				'Novembro' => 'November', 'Dezembro' => 'December'
			];

			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);

			$start = Carbon::createFromFormat('F/Y', "{$monthsMap[$startMonth]}/$startYear")->startOfMonth();
			$end = Carbon::createFromFormat('F/Y', "{$monthsMap[$endMonth]}/$endYear")->endOfMonth();

			$areaIds = $request->areaHomogeneaIds;

			// Aqui é onde concentramos tudo no banco
			$data = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->leftJoin('smalls', 'tree_visits.small_id', '=', 'smalls.id')
				->leftJoin('mediums', 'tree_visits.medium_id', '=', 'mediums.id')
				->leftJoin('mediums2', 'tree_visits.medium2_id', '=', 'mediums2.id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->leftJoin('adults', 'tree_visits.adult_id', '=', 'adults.id')
				->leftJoin('adults2', 'tree_visits.adult2_id', '=', 'adults2.id')
				->leftJoin('matures', 'tree_visits.mature_id', '=', 'matures.id')
				->leftJoin('matures2', 'tree_visits.mature2_id', '=', 'matures2.id')
				->leftJoin('matures3', 'tree_visits.mature3_id', '=', 'matures3.id')
				->leftJoin('matures4', 'tree_visits.mature4_id', '=', 'matures4.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->whereIn('homogeneous_areas.id', $areaIds)
				->whereBetween('tree_visits.date', [$start, $end])
				->selectRaw("
					MONTH(tree_visits.date) as month,
					YEAR(tree_visits.date) as year,
					COALESCE(mediums3.harvested, 0) +
					COALESCE(adults.harvested, 0) +
					COALESCE(adults2.harvested, 0) +
					COALESCE(matures.harvested, 0) +
					COALESCE(matures2.harvested, 0) +
					COALESCE(matures3.harvested, 0) +
					COALESCE(matures4.harvested, 0) as harvested,

					COALESCE(smalls.loss, 0) +
					COALESCE(mediums.loss, 0) +
					COALESCE(mediums2.loss, 0) +
					COALESCE(mediums3.loss, 0) +
					COALESCE(adults.loss, 0) +
					COALESCE(adults2.loss, 0) +
					COALESCE(matures.loss, 0) +
					COALESCE(matures2.loss, 0) +
					COALESCE(matures3.loss, 0) +
					COALESCE(matures4.loss, 0) as loss,

					COALESCE(smalls.piece, 0) + COALESCE(mediums.piece, 0) +
					COALESCE(mediums2.piece, 0) + COALESCE(mediums3.piece, 0) as piece,

					COALESCE(mediums2.rat, 0) + COALESCE(mediums3.rat, 0) +
					COALESCE(adults.rat, 0) + COALESCE(adults2.rat, 0) +
					COALESCE(matures.rat, 0) + COALESCE(matures2.rat, 0) +
					COALESCE(matures3.rat, 0) + COALESCE(matures4.rat, 0) as rat,

					COALESCE(mediums2.rotten, 0) + COALESCE(mediums3.rotten, 0) +
					COALESCE(adults.rotten, 0) + COALESCE(adults2.rotten, 0) +
					COALESCE(matures.rotten, 0) + COALESCE(matures2.rotten, 0) +
					COALESCE(matures3.rotten, 0) + COALESCE(matures4.rotten, 0) as rotten,

					COALESCE(smalls.witchs_broom, 0) + COALESCE(mediums2.witchs_broom, 0) +
					COALESCE(mediums3.witchs_broom, 0) + COALESCE(adults.witchs_broom, 0) +
					COALESCE(adults2.witchs_broom, 0) + COALESCE(matures.witchs_broom, 0) +
					COALESCE(matures2.witchs_broom, 0) + COALESCE(matures3.witchs_broom, 0) +
					COALESCE(matures4.witchs_broom, 0) as witch_broom
				")
				->get();

			// Agrupar resultados em PHP
			$totals = [
				'totalHarvested' => 0,
				'totalLoss' => 0,
				'totalPiece' => 0,
				'totalRat' => 0,
				'totalRotten' => 0,
				'totalWitchBroom' => 0
			];

			$monthly = array_fill(0, 12, 0);

			foreach ($data as $d) {
				$totals['totalHarvested'] += $d->harvested;
				$totals['totalLoss'] += $d->loss;
				$totals['totalPiece'] += $d->piece;
				$totals['totalRat'] += $d->rat;
				$totals['totalRotten'] += $d->rotten;
				$totals['totalWitchBroom'] += $d->witch_broom;

				$monthly[$d->month - 1] += $d->harvested;
			}

			$lossEstimate = $totals['totalPiece'] + $totals['totalRat'] + $totals['totalRotten'] + $totals['totalWitchBroom'] + $totals['totalLoss'];
			$totalCocoa = $lossEstimate + $totals['totalHarvested'];

			$monthKeys = ['out', 'nov', 'dez', 'jan', 'fev', 'mar', 'abr', 'mai', 'jun', 'jul', 'ago', 'set'];
			$reordered = array_merge(array_slice($monthly, 9), array_slice($monthly, 0, 9));
			$totalHarvestedByMonth = array_combine($monthKeys, $reordered);

			return response()->json([
				'totalCocoaData' => array_merge($totals, [
					'lossEstimate' => $lossEstimate,
					'totalCocoa' => $totalCocoa
				]),
				'totalHarvested' => $totalHarvestedByMonth
			]);

		} catch (\Exception $e) {
			Log::error('Erro ao buscar dados de múltiplas áreas:', ['error_message' => $e->getMessage()]);
			return response()->json(['error' => 'Erro ao obter os dados'], 500);
		}
	}

	public function getFruitDistribution(Request $request)
	{
		try {
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			$propertyId = $request->propertyId;
			$areaHomogeneaId = $request->areaHomogeneaId;

			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);

			$startDate = Carbon::createFromFormat('F/Y', ($months[$startMonth] ?? throw new \Exception("Mês inválido: $startMonth")) . '/' . $startYear)->startOfMonth();
			$endDate = Carbon::createFromFormat('F/Y', ($months[$endMonth] ?? throw new \Exception("Mês inválido: $endMonth")) . '/' . $endYear)->endOfMonth();

			$treeVisits = TreeVisit::with([
				'bobbin', 'small', 'medium', 'medium2', 'medium3',
				'adult', 'adult2', 'mature', 'mature2', 'mature3', 'mature4'
			])
			->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
			->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
			->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
			->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
			->where('homogeneous_areas.property_id', $propertyId)
			->where('homogeneous_areas.id', $areaHomogeneaId)
			->whereBetween('tree_visits.date', [$startDate, $endDate])
			->get();

			$totals = [
				'harvested' => 0,
				'loss' => 0,
				'rat' => 0,
				'piece' => 0,
				'rotten' => 0,
				'witchs_broom' => 0,
				'totalFruits' => 0,
				'visitDates' => [],
			];

			$stages = [
				'bobbin', 'small', 'medium', 'medium2', 'medium3',
				'adult', 'adult2', 'mature', 'mature2', 'mature3', 'mature4'
			];

			foreach ($treeVisits as $visit) {
				$totals['visitDates'][] = $visit->date;

				foreach ($stages as $stage) {
					$data = $visit->$stage;

					if ($data) {
						$totals['harvested']     += $data->harvested ?? 0;
						$totals['loss']          += $data->loss ?? 0;
						$totals['rat']           += $data->rat ?? 0;
						$totals['piece']         += $data->piece ?? 0;
						$totals['rotten']        += $data->rotten ?? 0;
						$totals['witchs_broom']  += $data->witchs_broom ?? 0;
						$totals['totalFruits']   += $data->total ?? 0;
					}
				}
			}

			$totals['visitDates'] = array_values(array_unique($totals['visitDates']));

			Log::info('Distribuição de frutos otimizada:', $totals);

			return response()->json($totals);
		} catch (\Exception $e) {
			Log::error('Erro ao obter distribuição de frutos: ' . $e->getMessage());
			return response()->json(['error' => 'Erro ao processar os dados.'], 500);
		}
	}

	public function getCollectionDataHarvested(Request $request)
	{
		try {
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);

			$start = Carbon::createFromFormat('F/Y', "{$months[$startMonth]}/$startYear")->startOfMonth();
			$end = Carbon::createFromFormat('F/Y', "{$months[$endMonth]}/$endYear")->endOfMonth();

			$totals = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->leftJoin('adults', 'tree_visits.adult_id', '=', 'adults.id')
				->leftJoin('adults2', 'tree_visits.adult2_id', '=', 'adults2.id')
				->leftJoin('matures', 'tree_visits.mature_id', '=', 'matures.id')
				->leftJoin('matures2', 'tree_visits.mature2_id', '=', 'matures2.id')
				->leftJoin('matures3', 'tree_visits.mature3_id', '=', 'matures3.id')
				->leftJoin('matures4', 'tree_visits.mature4_id', '=', 'matures4.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->where('homogeneous_areas.id', $request->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$start, $end])
				->selectRaw("
					DATE(tree_visits.date) as date,
					SUM(
						COALESCE(mediums3.harvested, 0) +
						COALESCE(adults.harvested, 0) +
						COALESCE(adults2.harvested, 0) +
						COALESCE(matures.harvested, 0) +
						COALESCE(matures2.harvested, 0) +
						COALESCE(matures3.harvested, 0) +
						COALESCE(matures4.harvested, 0)
					) as total
				")
				->groupByRaw('DATE(tree_visits.date)')
				->orderBy('date')
				->pluck('total', 'date'); // Retorna array associativo: ['2025-01-01' => 25, ...]

			return response()->json([
				'areaId' => $request->areaHomogeneaId,
				'totals' => $totals,
			]);

		} catch (\Exception $e) {
			Log::error('Erro ao buscar dados de coleta:', [
				'message' => $e->getMessage(),
				'trace' => $e->getTraceAsString(),
			]);

			return response()->json(['error' => 'Erro ao obter os dados.'], 500);
		}
	}


	public function getCollectionDataLoss(Request $request)
	{
		try {
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);

			$start = Carbon::createFromFormat('F/Y', "{$months[$startMonth]}/$startYear")->startOfMonth();
			$end = Carbon::createFromFormat('F/Y', "{$months[$endMonth]}/$endYear")->endOfMonth();

			$totals = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->leftJoin('smalls', 'tree_visits.small_id', '=', 'smalls.id')
				->leftJoin('mediums', 'tree_visits.medium_id', '=', 'mediums.id')
				->leftJoin('mediums2', 'tree_visits.medium2_id', '=', 'mediums2.id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->leftJoin('adults', 'tree_visits.adult_id', '=', 'adults.id')
				->leftJoin('adults2', 'tree_visits.adult2_id', '=', 'adults2.id')
				->leftJoin('matures', 'tree_visits.mature_id', '=', 'matures.id')
				->leftJoin('matures2', 'tree_visits.mature2_id', '=', 'matures2.id')
				->leftJoin('matures3', 'tree_visits.mature3_id', '=', 'matures3.id')
				->leftJoin('matures4', 'tree_visits.mature4_id', '=', 'matures4.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->where('homogeneous_areas.id', $request->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$start, $end])
				->selectRaw("
					DATE(tree_visits.date) as date,
					SUM(
						COALESCE(smalls.loss, 0) +
						COALESCE(mediums.loss, 0) +
						COALESCE(mediums2.loss, 0) +
						COALESCE(mediums3.loss, 0) +
						COALESCE(adults.loss, 0) +
						COALESCE(adults2.loss, 0) +
						COALESCE(matures.loss, 0) +
						COALESCE(matures2.loss, 0) +
						COALESCE(matures3.loss, 0) +
						COALESCE(matures4.loss, 0)
					) as total
				")
				->groupByRaw('DATE(tree_visits.date)')
				->orderBy('date')
				->pluck('total', 'date'); // Ex: ['2025-04-02' => 23.7]

			return response()->json([
				'areaId' => $request->areaHomogeneaId,
				'totals' => $totals
			]);

		} catch (\Exception $e) {
			Log::error('Erro ao buscar dados de perdas:', [
				'message' => $e->getMessage(),
				'trace' => $e->getTraceAsString(),
			]);

			return response()->json(['error' => 'Erro ao obter os dados.'], 500);
		}
	}

	public function getCollectionDataWB(Request $request)
	{
		try {
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);

			$start = Carbon::createFromFormat('F/Y', "{$months[$startMonth]}/$startYear")->startOfMonth();
			$end = Carbon::createFromFormat('F/Y', "{$months[$endMonth]}/$endYear")->endOfMonth();

			$totals = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->leftJoin('smalls', 'tree_visits.small_id', '=', 'smalls.id')
				->leftJoin('mediums2', 'tree_visits.medium2_id', '=', 'mediums2.id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->leftJoin('adults', 'tree_visits.adult_id', '=', 'adults.id')
				->leftJoin('adults2', 'tree_visits.adult2_id', '=', 'adults2.id')
				->leftJoin('matures', 'tree_visits.mature_id', '=', 'matures.id')
				->leftJoin('matures2', 'tree_visits.mature2_id', '=', 'matures2.id')
				->leftJoin('matures3', 'tree_visits.mature3_id', '=', 'matures3.id')
				->leftJoin('matures4', 'tree_visits.mature4_id', '=', 'matures4.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->where('homogeneous_areas.id', $request->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$start, $end])
				->selectRaw("
					DATE(tree_visits.date) as date,
					SUM(
						COALESCE(smalls.witchs_broom, 0) +
						COALESCE(mediums2.witchs_broom, 0) +
						COALESCE(mediums3.witchs_broom, 0) +
						COALESCE(adults.witchs_broom, 0) +
						COALESCE(adults2.witchs_broom, 0) +
						COALESCE(matures.witchs_broom, 0) +
						COALESCE(matures2.witchs_broom, 0) +
						COALESCE(matures3.witchs_broom, 0) +
						COALESCE(matures4.witchs_broom, 0)
					) as total
				")
				->groupByRaw('DATE(tree_visits.date)')
				->orderBy('date')
				->pluck('total', 'date');

			return response()->json([
				'areaId' => $request->areaHomogeneaId,
				'totals' => $totals
			]);

		} catch (\Exception $e) {
			Log::error('Erro ao buscar dados de vassoura-de-bruxa:', [
				'message' => $e->getMessage(),
				'trace' => $e->getTraceAsString(),
			]);

			return response()->json(['error' => 'Erro ao obter os dados'], 500);
		}
	}

	public function getCollectionDataRotten(Request $request)
	{
		try {
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			Log::info('Request Data', [
				'propertyId' => $request->propertyId,
				'areaHomogeneaId' => $request->areaHomogeneaId,
				'startDate' => $request->startDate,
				'endDate' => $request->endDate,
			]);

			// Conversão das datas
			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);

			$start = Carbon::createFromFormat('F/Y', "{$months[$startMonth]}/$startYear")->startOfMonth();
			$end = Carbon::createFromFormat('F/Y', "{$months[$endMonth]}/$endYear")->endOfMonth();

			Log::info('Datas convertidas:', [
				'startDate' => $start->format('Y-m-d'),
				'endDate' => $end->format('Y-m-d')
			]);

			$totals = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->leftJoin('mediums2', 'tree_visits.medium2_id', '=', 'mediums2.id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->leftJoin('adults', 'tree_visits.adult_id', '=', 'adults.id')
				->leftJoin('adults2', 'tree_visits.adult2_id', '=', 'adults2.id')
				->leftJoin('matures', 'tree_visits.mature_id', '=', 'matures.id')
				->leftJoin('matures2', 'tree_visits.mature2_id', '=', 'matures2.id')
				->leftJoin('matures3', 'tree_visits.mature3_id', '=', 'matures3.id')
				->leftJoin('matures4', 'tree_visits.mature4_id', '=', 'matures4.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->where('homogeneous_areas.id', $request->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$start, $end])
				->selectRaw("
					DATE(tree_visits.date) as date,
					SUM(
						COALESCE(mediums2.rotten, 0) +
						COALESCE(mediums3.rotten, 0) +
						COALESCE(adults.rotten, 0) +
						COALESCE(adults2.rotten, 0) +
						COALESCE(matures.rotten, 0) +
						COALESCE(matures2.rotten, 0) +
						COALESCE(matures3.rotten, 0) +
						COALESCE(matures4.rotten, 0)
					) as total
				")
				->groupByRaw('DATE(tree_visits.date)')
				->orderBy('date')
				->pluck('total', 'date');

			return response()->json([
				'areaId' => $request->areaHomogeneaId,
				'totals' => $totals
			]);

		} catch (\Exception $e) {
			Log::error('Erro ao buscar dados de frutos podres:', [
				'message' => $e->getMessage(),
				'trace' => $e->getTraceAsString()
			]);

			return response()->json(['error' => 'Erro ao obter os dados'], 500);
		}
	}

	public function getCollectionDataPiece(Request $request)
	{
		try {
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			Log::info('Request Data', [
				'propertyId' => $request->propertyId,
				'areaHomogeneaId' => $request->areaHomogeneaId,
				'startDate' => $request->startDate,
				'endDate' => $request->endDate,
			]);

			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);

			$start = Carbon::createFromFormat('F/Y', "{$months[$startMonth]}/$startYear")->startOfMonth();
			$end = Carbon::createFromFormat('F/Y', "{$months[$endMonth]}/$endYear")->endOfMonth();

			Log::info('Datas convertidas:', [
				'startDate' => $start->format('Y-m-d'),
				'endDate' => $end->format('Y-m-d')
			]);

			$totals = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->leftJoin('smalls', 'tree_visits.small_id', '=', 'smalls.id')
				->leftJoin('mediums', 'tree_visits.medium_id', '=', 'mediums.id')
				->leftJoin('mediums2', 'tree_visits.medium2_id', '=', 'mediums2.id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->where('homogeneous_areas.id', $request->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$start, $end])
				->selectRaw("
					DATE(tree_visits.date) as date,
					SUM(
						COALESCE(smalls.piece, 0) +
						COALESCE(mediums.piece, 0) +
						COALESCE(mediums2.piece, 0) +
						COALESCE(mediums3.piece, 0)
					) as total
				")
				->groupByRaw('DATE(tree_visits.date)')
				->orderBy('date')
				->pluck('total', 'date');

			return response()->json([
				'areaId' => $request->areaHomogeneaId,
				'totals' => $totals
			]);

		} catch (\Exception $e) {
			Log::error('Erro ao buscar dados de frutos pecos:', [
				'message' => $e->getMessage(),
				'trace' => $e->getTraceAsString()
			]);

			return response()->json(['error' => 'Erro ao obter os dados'], 500);
		}
	}

	public function getCollectionDataRat(Request $request)
	{
		try {
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			Log::info('Request Data', [
				'propertyId' => $request->propertyId,
				'areaHomogeneaId' => $request->areaHomogeneaId,
				'startDate' => $request->startDate,
				'endDate' => $request->endDate,
			]);

			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);

			$start = Carbon::createFromFormat('F/Y', "{$months[$startMonth]}/$startYear")->startOfMonth();
			$end = Carbon::createFromFormat('F/Y', "{$months[$endMonth]}/$endYear")->endOfMonth();

			Log::info('Datas convertidas:', [
				'startDate' => $start->format('Y-m-d'),
				'endDate' => $end->format('Y-m-d'),
			]);

			$totals = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->leftJoin('mediums2', 'tree_visits.medium2_id', '=', 'mediums2.id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->leftJoin('adults', 'tree_visits.adult_id', '=', 'adults.id')
				->leftJoin('adults2', 'tree_visits.adult2_id', '=', 'adults2.id')
				->leftJoin('matures', 'tree_visits.mature_id', '=', 'matures.id')
				->leftJoin('matures2', 'tree_visits.mature2_id', '=', 'matures2.id')
				->leftJoin('matures3', 'tree_visits.mature3_id', '=', 'matures3.id')
				->leftJoin('matures4', 'tree_visits.mature4_id', '=', 'matures4.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->where('homogeneous_areas.id', $request->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$start, $end])
				->selectRaw("
					DATE(tree_visits.date) as date,
					SUM(
						COALESCE(mediums2.rat, 0) +
						COALESCE(mediums3.rat, 0) +
						COALESCE(adults.rat, 0) +
						COALESCE(adults2.rat, 0) +
						COALESCE(matures.rat, 0) +
						COALESCE(matures2.rat, 0) +
						COALESCE(matures3.rat, 0) +
						COALESCE(matures4.rat, 0)
					) as total
				")
				->groupByRaw('DATE(tree_visits.date)')
				->orderBy('date')
				->pluck('total', 'date');

			return response()->json([
				'areaId' => $request->areaHomogeneaId,
				'totals' => $totals
			]);

		} catch (\Exception $e) {
			Log::error('Erro ao buscar dados de frutos comidos por ratos:', [
				'error_message' => $e->getMessage(),
				'trace' => $e->getTraceAsString()
			]);
			return response()->json(['error' => 'Erro ao obter os dados'], 500);
		}
	}

	public function getCollectionDataHarvestedHA(Request $request)
	{
		try {
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			Log::info('Request Data', [
				'propertyId' => $request->propertyId,
				'areaHomogeneaId' => $request->areaHomogeneaId,
				'startDate' => $request->startDate,
				'endDate' => $request->endDate,
			]);

			// Conversão de datas
			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);

			$start = Carbon::createFromFormat('F/Y', "{$months[$startMonth]}/$startYear")->startOfMonth();
			$end = Carbon::createFromFormat('F/Y', "{$months[$endMonth]}/$endYear")->endOfMonth();

			Log::info('Datas convertidas:', [
				'startDate' => $start->format('Y-m-d'),
				'endDate' => $end->format('Y-m-d'),
			]);

			// SQL otimizado
			$totals = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->leftJoin('adults', 'tree_visits.adult_id', '=', 'adults.id')
				->leftJoin('adults2', 'tree_visits.adult2_id', '=', 'adults2.id')
				->leftJoin('matures', 'tree_visits.mature_id', '=', 'matures.id')
				->leftJoin('matures2', 'tree_visits.mature2_id', '=', 'matures2.id')
				->leftJoin('matures3', 'tree_visits.mature3_id', '=', 'matures3.id')
				->leftJoin('matures4', 'tree_visits.mature4_id', '=', 'matures4.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->where('homogeneous_areas.id', $request->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$start, $end])
				->selectRaw("
					DATE_FORMAT(tree_visits.date, '%d/%m') as day_month,
					SUM(
						COALESCE(mediums3.harvested, 0) +
						COALESCE(adults.harvested, 0) +
						COALESCE(adults2.harvested, 0) +
						COALESCE(matures.harvested, 0) +
						COALESCE(matures2.harvested, 0) +
						COALESCE(matures3.harvested, 0) +
						COALESCE(matures4.harvested, 0)
					) as total
				")
				->groupByRaw("day_month")
				->get()
				->pluck('total', 'day_month')
				->toArray();

			// Ordenar como antes: meses de outubro (10) em diante vêm antes
			uksort($totals, function ($a, $b) {
				[$dayA, $monthA] = explode('/', $a);
				[$dayB, $monthB] = explode('/', $b);

				$monthA = (int)$monthA;
				$monthB = (int)$monthB;

				$adjustedA = $monthA >= 10 ? $monthA - 12 : $monthA;
				$adjustedB = $monthB >= 10 ? $monthB - 12 : $monthB;

				if ($adjustedA === $adjustedB) {
					return (int)$dayA <=> (int)$dayB;
				}

				return $adjustedA <=> $adjustedB;
			});

			// Retornar apenas os valores em array
			return response()->json(array_values($totals));

		} catch (\Exception $e) {
			Log::error('Erro ao buscar dados colhidos por HA:', [
				'error_message' => $e->getMessage(),
				'trace' => $e->getTraceAsString()
			]);
			return response()->json(['error' => 'Erro ao obter os dados'], 500);
		}
	}

	public function getCollectionDataLossHA(Request $request)
	{
		try {
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			Log::info('Request Data', [
				'propertyId' => $request->propertyId,
				'areaHomogeneaId' => $request->areaHomogeneaId,
				'startDate' => $request->startDate,
				'endDate' => $request->endDate,
			]);

			// Mapeamento de meses PT-BR para EN
			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			// Conversão de datas
			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);
			$start = Carbon::createFromFormat('F/Y', "{$months[$startMonth]}/$startYear")->startOfMonth();
			$end = Carbon::createFromFormat('F/Y', "{$months[$endMonth]}/$endYear")->endOfMonth();

			Log::info('Datas convertidas:', [
				'startDate' => $start->format('Y-m-d'),
				'endDate' => $end->format('Y-m-d'),
			]);

			// Consulta otimizada com soma no SQL
			$totals = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->leftJoin('smalls', 'tree_visits.small_id', '=', 'smalls.id')
				->leftJoin('mediums', 'tree_visits.medium_id', '=', 'mediums.id')
				->leftJoin('mediums2', 'tree_visits.medium2_id', '=', 'mediums2.id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->leftJoin('adults', 'tree_visits.adult_id', '=', 'adults.id')
				->leftJoin('adults2', 'tree_visits.adult2_id', '=', 'adults2.id')
				->leftJoin('matures', 'tree_visits.mature_id', '=', 'matures.id')
				->leftJoin('matures2', 'tree_visits.mature2_id', '=', 'matures2.id')
				->leftJoin('matures3', 'tree_visits.mature3_id', '=', 'matures3.id')
				->leftJoin('matures4', 'tree_visits.mature4_id', '=', 'matures4.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->where('homogeneous_areas.id', $request->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$start, $end])
				->selectRaw("
					DATE_FORMAT(tree_visits.date, '%d/%m') as day_month,
					SUM(
						COALESCE(smalls.loss, 0) +
						COALESCE(mediums.loss, 0) +
						COALESCE(mediums2.loss, 0) +
						COALESCE(mediums3.loss, 0) +
						COALESCE(adults.loss, 0) +
						COALESCE(adults2.loss, 0) +
						COALESCE(matures.loss, 0) +
						COALESCE(matures2.loss, 0) +
						COALESCE(matures3.loss, 0) +
						COALESCE(matures4.loss, 0)
					) as total
				")
				->groupByRaw("day_month")
				->get()
				->pluck('total', 'day_month')
				->toArray();

			// Ordenação estilo safra (outubro a setembro)
			uksort($totals, function ($a, $b) {
				[$dayA, $monthA] = explode('/', $a);
				[$dayB, $monthB] = explode('/', $b);

				$monthA = (int)$monthA;
				$monthB = (int)$monthB;

				$adjustedA = $monthA >= 10 ? $monthA - 12 : $monthA;
				$adjustedB = $monthB >= 10 ? $monthB - 12 : $monthB;

				return $adjustedA === $adjustedB
					? (int)$dayA <=> (int)$dayB
					: $adjustedA <=> $adjustedB;
			});

			return response()->json(array_values($totals));

		} catch (\Exception $e) {
			Log::error('Erro ao buscar dados de perdas por HA:', [
				'error_message' => $e->getMessage(),
				'trace' => $e->getTraceAsString()
			]);
			return response()->json(['error' => 'Erro ao obter os dados'], 500);
		}
	}

	//Continuar daqui
	public function getCollectionDataWBHA(Request $request)
	{
		try {
			// Validação dos parâmetros
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			Log::info('Request Data', [
				'propertyId' => $request->propertyId,
				'areaHomogeneaId' => $request->areaHomogeneaId,
				'startDate' => $request->startDate,
				'endDate' => $request->endDate,
			]);

			// Conversão de meses PT-BR para EN
			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			// Conversão de datas
			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);
			$start = Carbon::createFromFormat('F/Y', "{$months[$startMonth]}/$startYear")->startOfMonth();
			$end = Carbon::createFromFormat('F/Y', "{$months[$endMonth]}/$endYear")->endOfMonth();

			Log::info('Datas convertidas:', [
				'startDate' => $start->format('Y-m-d'),
				'endDate' => $end->format('Y-m-d'),
			]);

			// Consulta direta e agregada no SQL
			$totals = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->leftJoin('smalls', 'tree_visits.small_id', '=', 'smalls.id')
				->leftJoin('mediums2', 'tree_visits.medium2_id', '=', 'mediums2.id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->leftJoin('adults', 'tree_visits.adult_id', '=', 'adults.id')
				->leftJoin('adults2', 'tree_visits.adult2_id', '=', 'adults2.id')
				->leftJoin('matures', 'tree_visits.mature_id', '=', 'matures.id')
				->leftJoin('matures2', 'tree_visits.mature2_id', '=', 'matures2.id')
				->leftJoin('matures3', 'tree_visits.mature3_id', '=', 'matures3.id')
				->leftJoin('matures4', 'tree_visits.mature4_id', '=', 'matures4.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->where('homogeneous_areas.id', $request->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$start, $end])
				->selectRaw("
					DATE_FORMAT(tree_visits.date, '%d/%m') as day_month,
					SUM(
						COALESCE(smalls.witchs_broom, 0) +
						COALESCE(mediums2.witchs_broom, 0) +
						COALESCE(mediums3.witchs_broom, 0) +
						COALESCE(adults.witchs_broom, 0) +
						COALESCE(adults2.witchs_broom, 0) +
						COALESCE(matures.witchs_broom, 0) +
						COALESCE(matures2.witchs_broom, 0) +
						COALESCE(matures3.witchs_broom, 0) +
						COALESCE(matures4.witchs_broom, 0)
					) as total
				")
				->groupByRaw("day_month")
				->get()
				->pluck('total', 'day_month')
				->toArray();

			// Ordenação estilo safra (outubro a setembro)
			uksort($totals, function ($a, $b) {
				[$dayA, $monthA] = explode('/', $a);
				[$dayB, $monthB] = explode('/', $b);

				$adjustedA = (int)$monthA >= 10 ? (int)$monthA - 12 : (int)$monthA;
				$adjustedB = (int)$monthB >= 10 ? (int)$monthB - 12 : (int)$monthB;

				return $adjustedA === $adjustedB
					? (int)$dayA <=> (int)$dayB
					: $adjustedA <=> $adjustedB;
			});

			return response()->json(array_values($totals));

		} catch (\Exception $e) {
			Log::error('Erro ao buscar dados de vassoura-de-bruxa por HA:', [
				'error_message' => $e->getMessage(),
				'trace' => $e->getTraceAsString()
			]);
			return response()->json(['error' => 'Erro ao obter os dados'], 500);
		}
	}

	public function getCollectionDataPieceHA(Request $request)
	{
		try {
			// Validação dos parâmetros
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			Log::info('Request Data', [
				'propertyId' => $request->propertyId,
				'areaHomogeneaId' => $request->areaHomogeneaId,
				'startDate' => $request->startDate,
				'endDate' => $request->endDate,
			]);

			// Conversão de meses PT-BR para EN
			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			// Conversão de datas
			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);
			$start = Carbon::createFromFormat('F/Y', "{$months[$startMonth]}/$startYear")->startOfMonth();
			$end = Carbon::createFromFormat('F/Y', "{$months[$endMonth]}/$endYear")->endOfMonth();

			Log::info('Datas convertidas:', [
				'startDate' => $start->format('Y-m-d'),
				'endDate' => $end->format('Y-m-d'),
			]);

			// Consulta agregada diretamente no SQL
			$totals = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->leftJoin('smalls', 'tree_visits.small_id', '=', 'smalls.id')
				->leftJoin('mediums', 'tree_visits.medium_id', '=', 'mediums.id')
				->leftJoin('mediums2', 'tree_visits.medium2_id', '=', 'mediums2.id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->where('homogeneous_areas.id', $request->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$start, $end])
				->selectRaw("
					DATE_FORMAT(tree_visits.date, '%d/%m') as day_month,
					SUM(
						COALESCE(smalls.piece, 0) +
						COALESCE(mediums.piece, 0) +
						COALESCE(mediums2.piece, 0) +
						COALESCE(mediums3.piece, 0)
					) as total
				")
				->groupByRaw("day_month")
				->get()
				->pluck('total', 'day_month')
				->toArray();

			// Ordenação estilo safra (outubro a setembro)
			uksort($totals, function ($a, $b) {
				[$dayA, $monthA] = explode('/', $a);
				[$dayB, $monthB] = explode('/', $b);

				$adjustedA = (int)$monthA >= 10 ? (int)$monthA - 12 : (int)$monthA;
				$adjustedB = (int)$monthB >= 10 ? (int)$monthB - 12 : (int)$monthB;

				return $adjustedA === $adjustedB
					? (int)$dayA <=> (int)$dayB
					: $adjustedA <=> $adjustedB;
			});

			return response()->json(array_values($totals));

		} catch (\Exception $e) {
			Log::error('Erro ao buscar dados de frutos partidos por HA:', [
				'error_message' => $e->getMessage(),
				'trace' => $e->getTraceAsString()
			]);
			return response()->json(['error' => 'Erro ao obter os dados'], 500);
		}
	}


	public function getCollectionDataRatHA(Request $request)
	{
		try {
			// Validação dos parâmetros
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			Log::info('Request Data', [
				'propertyId' => $request->propertyId,
				'areaHomogeneaId' => $request->areaHomogeneaId,
				'startDate' => $request->startDate,
				'endDate' => $request->endDate,
			]);

			// Mapeamento de meses
			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			// Conversão de datas
			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);
			$start = Carbon::createFromFormat('F/Y', "{$months[$startMonth]}/$startYear")->startOfMonth();
			$end = Carbon::createFromFormat('F/Y', "{$months[$endMonth]}/$endYear")->endOfMonth();

			Log::info('Datas convertidas:', [
				'startDate' => $start->format('Y-m-d'),
				'endDate' => $end->format('Y-m-d'),
			]);

			// Consulta otimizada com agregação de dados de rato
			$totals = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->leftJoin('mediums2', 'tree_visits.medium2_id', '=', 'mediums2.id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->leftJoin('adults', 'tree_visits.adult_id', '=', 'adults.id')
				->leftJoin('adults2', 'tree_visits.adult2_id', '=', 'adults2.id')
				->leftJoin('matures', 'tree_visits.mature_id', '=', 'matures.id')
				->leftJoin('matures2', 'tree_visits.mature2_id', '=', 'matures2.id')
				->leftJoin('matures3', 'tree_visits.mature3_id', '=', 'matures3.id')
				->leftJoin('matures4', 'tree_visits.mature4_id', '=', 'matures4.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->where('homogeneous_areas.id', $request->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$start, $end])
				->selectRaw("
					DATE_FORMAT(tree_visits.date, '%d/%m') as day_month,
					SUM(
						COALESCE(mediums2.rat, 0) +
						COALESCE(mediums3.rat, 0) +
						COALESCE(adults.rat, 0) +
						COALESCE(adults2.rat, 0) +
						COALESCE(matures.rat, 0) +
						COALESCE(matures2.rat, 0) +
						COALESCE(matures3.rat, 0) +
						COALESCE(matures4.rat, 0)
					) as total
				")
				->groupByRaw("day_month")
				->get()
				->pluck('total', 'day_month')
				->toArray();

			// Ordenar estilo safra (outubro a setembro)
			uksort($totals, function ($a, $b) {
				[$dayA, $monthA] = explode('/', $a);
				[$dayB, $monthB] = explode('/', $b);

				$adjustedA = (int)$monthA >= 10 ? (int)$monthA - 12 : (int)$monthA;
				$adjustedB = (int)$monthB >= 10 ? (int)$monthB - 12 : (int)$monthB;

				return $adjustedA === $adjustedB
					? (int)$dayA <=> (int)$dayB
					: $adjustedA <=> $adjustedB;
			});

			return response()->json(array_values($totals));

		} catch (\Exception $e) {
			Log::error('Erro ao buscar dados de rat por HA:', [
				'error_message' => $e->getMessage(),
				'trace' => $e->getTraceAsString()
			]);
			return response()->json(['error' => 'Erro ao obter os dados'], 500);
		}
	}


	public function getCollectionDataRottenHA(Request $request)
	{
		try {
			// Validação dos parâmetros
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			Log::info('Request Data', [
				'propertyId' => $request->propertyId,
				'areaHomogeneaId' => $request->areaHomogeneaId,
				'startDate' => $request->startDate,
				'endDate' => $request->endDate,
			]);

			// Conversão de meses PT-BR para EN
			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			// Conversão de datas
			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);
			$start = Carbon::createFromFormat('F/Y', "{$months[$startMonth]}/$startYear")->startOfMonth();
			$end = Carbon::createFromFormat('F/Y', "{$months[$endMonth]}/$endYear")->endOfMonth();

			Log::info('Datas convertidas:', [
				'startDate' => $start->format('Y-m-d'),
				'endDate' => $end->format('Y-m-d'),
			]);

			// Consulta SQL com agregação de frutos podres
			$totals = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->leftJoin('mediums2', 'tree_visits.medium2_id', '=', 'mediums2.id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->leftJoin('adults', 'tree_visits.adult_id', '=', 'adults.id')
				->leftJoin('adults2', 'tree_visits.adult2_id', '=', 'adults2.id')
				->leftJoin('matures', 'tree_visits.mature_id', '=', 'matures.id')
				->leftJoin('matures2', 'tree_visits.mature2_id', '=', 'matures2.id')
				->leftJoin('matures3', 'tree_visits.mature3_id', '=', 'matures3.id')
				->leftJoin('matures4', 'tree_visits.mature4_id', '=', 'matures4.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->where('homogeneous_areas.id', $request->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$start, $end])
				->selectRaw("
					DATE_FORMAT(tree_visits.date, '%d/%m') as day_month,
					SUM(
						COALESCE(mediums2.rotten, 0) +
						COALESCE(mediums3.rotten, 0) +
						COALESCE(adults.rotten, 0) +
						COALESCE(adults2.rotten, 0) +
						COALESCE(matures.rotten, 0) +
						COALESCE(matures2.rotten, 0) +
						COALESCE(matures3.rotten, 0) +
						COALESCE(matures4.rotten, 0)
					) as total
				")
				->groupByRaw("day_month")
				->get()
				->pluck('total', 'day_month')
				->toArray();

			// Ordenação estilo safra (outubro a setembro)
			uksort($totals, function ($a, $b) {
				[$dayA, $monthA] = explode('/', $a);
				[$dayB, $monthB] = explode('/', $b);

				$adjustedA = (int)$monthA >= 10 ? (int)$monthA - 12 : (int)$monthA;
				$adjustedB = (int)$monthB >= 10 ? (int)$monthB - 12 : (int)$monthB;

				return $adjustedA === $adjustedB
					? (int)$dayA <=> (int)$dayB
					: $adjustedA <=> $adjustedB;
			});

			return response()->json(array_values($totals));

		} catch (\Exception $e) {
			Log::error('Erro ao buscar dados de frutos podres por HA:', [
				'error_message' => $e->getMessage(),
				'trace' => $e->getTraceAsString()
			]);
			return response()->json(['error' => 'Erro ao obter os dados'], 500);
		}
	}

	/**
	 * Calcula o total de cacau por faixa de tamanho para um período específico
	 *
	 * @param Request $request
	 * @return \Illuminate\Http\JsonResponse
	 */
	public function getTotalCocoaByRange(Request $request)
	{
		try {
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
				'faixaInicial' => 'required|integer|min:0|max:10',
				'faixaFinal' => 'required|integer|min:0|max:10',
				'coletaInicial' => 'required|string',
				'coletaFinal' => 'required|string',
			]);

			$propertyId = $request->input('propertyId');
			$areaHomogeneaId = $request->input('areaHomogeneaId');
			$startDateInput = $request->input('startDate');
			$endDateInput = $request->input('endDate');
			$faixaInicial = $request->input('faixaInicial');
			$faixaFinal = $request->input('faixaFinal');
			$coletaInicial = $request->input('coletaInicial');
			$coletaFinal = $request->input('coletaFinal');

			// Conversão de datas para o formato correto
			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			// Converter startDate
			list($startMonth, $startYear) = explode('/', $startDateInput);
			$startMonthEnglish = $months[$startMonth] ?? null;
			if (!$startMonthEnglish) {
				throw new \Exception("Mês de início inválido: $startMonth");
			}
			$startDate = Carbon::createFromFormat('F/Y', $startMonthEnglish . '/' . $startYear)->startOfMonth();

			// Converter endDate
			list($endMonth, $endYear) = explode('/', $endDateInput);
			$endMonthEnglish = $months[$endMonth] ?? null;
			if (!$endMonthEnglish) {
				throw new \Exception("Mês de fim inválido: $endMonth");
			}
			$endDate = Carbon::createFromFormat('F/Y', $endMonthEnglish . '/' . $endYear)->endOfMonth();

			// Converter datas de coleta para Carbon
			$coletaInicialDate = Carbon::createFromFormat('d/m/Y', $coletaInicial);
			$coletaFinalDate = Carbon::createFromFormat('d/m/Y', $coletaFinal);

			// Buscar dados de coleta
			$sampling_points = SamplingPoint::whereHas('stratum', function ($query) use ($propertyId) {
				$query->where('property_id', $propertyId);
			})->get();

			$samplingPointIds = $sampling_points->pluck('id');

			$all_tree_visits = TreeVisit::with([
					'bobbin', 'small', 'medium', 'medium2', 'medium3',
					'adult', 'adult2', 'mature', 'mature2', 'mature3', 'mature4'
				])
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->whereIn('trees.sampling_point_id', $samplingPointIds)
				->where('homogeneous_areas.id', '=', $areaHomogeneaId)
				->whereBetween('tree_visits.date', [$startDate, $endDate])
				->whereBetween('tree_visits.date', [$coletaInicialDate, $coletaFinalDate])
				->select('tree_visits.*')
				->get();

			// Definir faixas de tamanho
			$faixas = [
				"0 - 21", "21 - 42", "42 - 63", "63 - 84", "84 - 105",
				"105 - 126", "126 - 147", "147 - 168", "168 - 189", "189 - 210", "> 210"
			];

			// Processar dados por data
			$totalCocoa = [];
			$chartLabels = [];
			$chartDatasets = [];

			// Agrupar visitas por data
			$visitsByDate = $all_tree_visits->groupBy(function($visit) {
				return Carbon::parse($visit->date)->format('Y-m-d');
			});

			foreach ($visitsByDate as $date => $visits) {
				$dateFormatted = Carbon::parse($date)->format('d/m/Y');
				$chartLabels[] = $dateFormatted;

				$totalsForDate = array_fill(0, ($faixaFinal - $faixaInicial + 1), 0);

				foreach ($visits as $visit) {
					for ($j = $faixaInicial; $j <= $faixaFinal; $j++) {
						$total = $this->getTotalCocoaForRange($j, $visit);
						$totalsForDate[$j - $faixaInicial] += $total;
					}
				}

				$totalCocoa[$date] = $totalsForDate;
			}

			// Preparar datasets para o gráfico
			for ($i = 0; $i < ($faixaFinal - $faixaInicial + 1); $i++) {
				$colors = ["orange", "green", "yellow", "red", "blue", "purple", "brown", "pink", "gray", "cyan", "black"];

				$data = [];
				foreach ($totalCocoa as $dateData) {
					$data[] = $dateData[$i] ?? 0;
				}

				$chartDatasets[] = [
					'label' => $faixas[$faixaInicial + $i],
					'backgroundColor' => $colors[$i % count($colors)],
					'data' => $data
				];
			}

			return response()->json([
				'success' => true,
				'data' => [
					'labels' => $chartLabels,
					'datasets' => $chartDatasets,
					'totalCocoa' => $totalCocoa
				]
			]);

		} catch (\Exception $e) {
			Log::error('Erro ao calcular total de cacau por faixa:', [
				'error_message' => $e->getMessage(),
				'trace' => $e->getTraceAsString()
			]);
			return response()->json(['error' => 'Erro ao obter os dados'], 500);
		}
	}

	/**
	 * Retorna o total de cacau para uma faixa específica
	 *
	 * @param int $index
	 * @param object $treeVisit
	 * @return int
	 */
	private function getTotalCocoaForRange($index, $treeVisit)
	{
		switch($index) {
			case 0: return $treeVisit->bobbin ? $treeVisit->bobbin->total : 0;
			case 1: return $treeVisit->small ? $treeVisit->small->total : 0;
			case 2: return $treeVisit->medium ? $treeVisit->medium->total : 0;
			case 3: return $treeVisit->medium2 ? $treeVisit->medium2->total : 0;
			case 4: return $treeVisit->medium3 ? $treeVisit->medium3->total : 0;
			case 5: return $treeVisit->adult ? $treeVisit->adult->total : 0;
			case 6: return $treeVisit->adult2 ? $treeVisit->adult2->total : 0;
			case 7: return $treeVisit->mature ? $treeVisit->mature->total : 0;
			case 8: return $treeVisit->mature2 ? $treeVisit->mature2->total : 0;
			case 9: return $treeVisit->mature3 ? $treeVisit->mature3->total : 0;
			case 10: return $treeVisit->mature4 ? $treeVisit->mature4->total : 0;
			default: return 0;
		}
	}
}
