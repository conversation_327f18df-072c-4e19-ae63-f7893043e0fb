<?php

namespace App\Http\Controllers\API;

use PDF;
use Maatwebsite\Excel\Facades\Excel;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;

use App\Exports\ExcelExport;
use App\Imports\ExcelImport;
use App\Exports\BlockExport;
use App\Exports\PracticeExport;
use App\Exports\ExcelPropertyExport;
use App\Exports\ModelExport;

use App\Jobs\ImportSpreadSheet;

use App\User;
use App\Models\Stratum;
use App\Models\Property;
use App\Models\TreeVisit;
use App\Models\SamplingPoint;
use Illuminate\Support\Facades\Auth;
use App\Models\Collection;
use App\Http\Resources\TreeVisitResource;

set_time_limit(0);

class DataController extends BaseController
{
	private $propertyId;
    private $startDate;
    private $endDate;
	private $areaHomogeneaId;

	public function getCollectionData(Request $request)
    {


        try {
            $request->validate([
                'propertyId' => 'required|integer|exists:properties,id',
                'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
                'startDate' => 'required|string',
                'endDate' => 'required|string',
            ]);
			Log::info('Request Data', [
				'propertyId' => $request->input('propertyId'),
				'areaHomogeneaId' => $request->input('areaHomogeneaId'),
				'startDate' => $request->input('startDate'),
				'endDate' => $request->input('endDate'),
			]);

            $this->propertyId = $request->input('propertyId');
            $this->areaHomogeneaId = $request->input('areaHomogeneaId');
            $startDateInput = $request->input('startDate');
            $endDateInput = $request->input('endDate');

            // Conversão de datas para o formato correto
            $months = [
                'Janeiro' => 'January',
				'Fevereiro' => 'February',
				'Março' => 'March',
				'Abril' => 'April',
                'Maio' => 'May',
				'Junho' => 'June',
				'Julho' => 'July',
				'Agosto' => 'August',
                'Setembro' => 'September',
				'Outubro' => 'October',
				'Novembro' => 'November',
				'Dezembro' => 'December'
            ];

            // Converter o mês de startDate
			list($startMonth, $startYear) = explode('/', $startDateInput);
			$startMonthEnglish = $months[$startMonth] ?? null;
			if (!$startMonthEnglish) {
				throw new \Exception("Mês de início inválido: $startMonth");
			}
			$this->startDate = Carbon::createFromFormat('F/Y', $startMonthEnglish . '/' . $startYear)->startOfMonth();

			// Converter o mês de endDate
			list($endMonth, $endYear) = explode('/', $endDateInput);
			$endMonthEnglish = $months[$endMonth] ?? null;
			if (!$endMonthEnglish) {
				throw new \Exception("Mês de fim inválido: $endMonth");
			}
			$this->endDate = Carbon::createFromFormat('F/Y', $endMonthEnglish . '/' . $endYear)->endOfMonth();

			Log::info('Datas convertidas:', ['startDate' => $this->startDate->format('Y-m-d'), 'endDate' => $this->endDate->format('Y-m-d')]);

            // Filtrar Sampling Points com base no propertyId
			$sampling_points = SamplingPoint::whereHas('stratum', function ($query) {
				$query->where('property_id', $this->propertyId);
			})->get();

			// Extrair os IDs dos pontos amostrais
			$samplingPointIds = $sampling_points->pluck('id');

			// Fazer uma única query para buscar todas as visitas
			$all_tree_visits = TreeVisit::join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->whereIn('trees.sampling_point_id', $samplingPointIds)
				->where('homogeneous_areas.id', '=', $this->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$this->startDate, $this->endDate])
				->select(
					'tree_visits.*',
					'trees.id as tree_id',
					'sampling_points.id as sampling_point_id',
					'strata.id as stratum_id',
					'homogeneous_areas.id as homogeneous_area_id'
				)
				->get();

			// Logar quantidade
			Log::info('Total tree visits processed:', [
				'total_visits' => $all_tree_visits->count(),
			]);

			// Converter os dados
			$data = TreeVisitResource::collection($all_tree_visits);


        	return response()->json($data);

        } catch (\Exception $e) {
            Log::error('Erro ao buscar dados de coleta:', ['error_message' => $e->getMessage()]);
            return response()->json(['error' => 'Erro ao obter os dados'], 500);
        }

    }

	public function getCollectionDataMultiArea(Request $request)
	{
		try {
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaIds' => 'required|array|min:1',
				'areaHomogeneaIds.*' => 'integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			$monthsMap = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June', 'Julho' => 'July',
				'Agosto' => 'August', 'Setembro' => 'September', 'Outubro' => 'October',
				'Novembro' => 'November', 'Dezembro' => 'December'
			];

			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);

			$start = Carbon::createFromFormat('F/Y', "{$monthsMap[$startMonth]}/$startYear")->startOfMonth();
			$end = Carbon::createFromFormat('F/Y', "{$monthsMap[$endMonth]}/$endYear")->endOfMonth();

			$areaIds = $request->areaHomogeneaIds;

			// Aqui é onde concentramos tudo no banco
			$data = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->leftJoin('smalls', 'tree_visits.small_id', '=', 'smalls.id')
				->leftJoin('mediums', 'tree_visits.medium_id', '=', 'mediums.id')
				->leftJoin('mediums2', 'tree_visits.medium2_id', '=', 'mediums2.id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->leftJoin('adults', 'tree_visits.adult_id', '=', 'adults.id')
				->leftJoin('adults2', 'tree_visits.adult2_id', '=', 'adults2.id')
				->leftJoin('matures', 'tree_visits.mature_id', '=', 'matures.id')
				->leftJoin('matures2', 'tree_visits.mature2_id', '=', 'matures2.id')
				->leftJoin('matures3', 'tree_visits.mature3_id', '=', 'matures3.id')
				->leftJoin('matures4', 'tree_visits.mature4_id', '=', 'matures4.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->whereIn('homogeneous_areas.id', $areaIds)
				->whereBetween('tree_visits.date', [$start, $end])
				->selectRaw("
					MONTH(tree_visits.date) as month,
					YEAR(tree_visits.date) as year,
					COALESCE(mediums3.harvested, 0) +
					COALESCE(adults.harvested, 0) +
					COALESCE(adults2.harvested, 0) +
					COALESCE(matures.harvested, 0) +
					COALESCE(matures2.harvested, 0) +
					COALESCE(matures3.harvested, 0) +
					COALESCE(matures4.harvested, 0) as harvested,

					COALESCE(smalls.loss, 0) +
					COALESCE(mediums.loss, 0) +
					COALESCE(mediums2.loss, 0) +
					COALESCE(mediums3.loss, 0) +
					COALESCE(adults.loss, 0) +
					COALESCE(adults2.loss, 0) +
					COALESCE(matures.loss, 0) +
					COALESCE(matures2.loss, 0) +
					COALESCE(matures3.loss, 0) +
					COALESCE(matures4.loss, 0) as loss,

					COALESCE(smalls.piece, 0) + COALESCE(mediums.piece, 0) +
					COALESCE(mediums2.piece, 0) + COALESCE(mediums3.piece, 0) as piece,

					COALESCE(mediums2.rat, 0) + COALESCE(mediums3.rat, 0) +
					COALESCE(adults.rat, 0) + COALESCE(adults2.rat, 0) +
					COALESCE(matures.rat, 0) + COALESCE(matures2.rat, 0) +
					COALESCE(matures3.rat, 0) + COALESCE(matures4.rat, 0) as rat,

					COALESCE(mediums2.rotten, 0) + COALESCE(mediums3.rotten, 0) +
					COALESCE(adults.rotten, 0) + COALESCE(adults2.rotten, 0) +
					COALESCE(matures.rotten, 0) + COALESCE(matures2.rotten, 0) +
					COALESCE(matures3.rotten, 0) + COALESCE(matures4.rotten, 0) as rotten,

					COALESCE(smalls.witchs_broom, 0) + COALESCE(mediums2.witchs_broom, 0) +
					COALESCE(mediums3.witchs_broom, 0) + COALESCE(adults.witchs_broom, 0) +
					COALESCE(adults2.witchs_broom, 0) + COALESCE(matures.witchs_broom, 0) +
					COALESCE(matures2.witchs_broom, 0) + COALESCE(matures3.witchs_broom, 0) +
					COALESCE(matures4.witchs_broom, 0) as witch_broom
				")
				->get();

			// Agrupar resultados em PHP
			$totals = [
				'totalHarvested' => 0,
				'totalLoss' => 0,
				'totalPiece' => 0,
				'totalRat' => 0,
				'totalRotten' => 0,
				'totalWitchBroom' => 0
			];

			$monthly = array_fill(0, 12, 0);

			foreach ($data as $d) {
				$totals['totalHarvested'] += $d->harvested;
				$totals['totalLoss'] += $d->loss;
				$totals['totalPiece'] += $d->piece;
				$totals['totalRat'] += $d->rat;
				$totals['totalRotten'] += $d->rotten;
				$totals['totalWitchBroom'] += $d->witch_broom;

				$monthly[$d->month - 1] += $d->harvested;
			}

			$lossEstimate = $totals['totalPiece'] + $totals['totalRat'] + $totals['totalRotten'] + $totals['totalWitchBroom'] + $totals['totalLoss'];
			$totalCocoa = $lossEstimate + $totals['totalHarvested'];

			$monthKeys = ['out', 'nov', 'dez', 'jan', 'fev', 'mar', 'abr', 'mai', 'jun', 'jul', 'ago', 'set'];
			$reordered = array_merge(array_slice($monthly, 9), array_slice($monthly, 0, 9));
			$totalHarvestedByMonth = array_combine($monthKeys, $reordered);

			return response()->json([
				'totalCocoaData' => array_merge($totals, [
					'lossEstimate' => $lossEstimate,
					'totalCocoa' => $totalCocoa
				]),
				'totalHarvested' => $totalHarvestedByMonth
			]);

		} catch (\Exception $e) {
			Log::error('Erro ao buscar dados de múltiplas áreas:', ['error_message' => $e->getMessage()]);
			return response()->json(['error' => 'Erro ao obter os dados'], 500);
		}
	}

	public function getFruitDistribution(Request $request)
	{
		try {
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			$propertyId = $request->propertyId;
			$areaHomogeneaId = $request->areaHomogeneaId;

			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);

			$startDate = Carbon::createFromFormat('F/Y', ($months[$startMonth] ?? throw new \Exception("Mês inválido: $startMonth")) . '/' . $startYear)->startOfMonth();
			$endDate = Carbon::createFromFormat('F/Y', ($months[$endMonth] ?? throw new \Exception("Mês inválido: $endMonth")) . '/' . $endYear)->endOfMonth();

			$treeVisits = TreeVisit::with([
				'bobbin', 'small', 'medium', 'medium2', 'medium3',
				'adult', 'adult2', 'mature', 'mature2', 'mature3', 'mature4'
			])
			->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
			->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
			->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
			->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
			->where('homogeneous_areas.property_id', $propertyId)
			->where('homogeneous_areas.id', $areaHomogeneaId)
			->whereBetween('tree_visits.date', [$startDate, $endDate])
			->get();

			$totals = [
				'harvested' => 0,
				'loss' => 0,
				'rat' => 0,
				'piece' => 0,
				'rotten' => 0,
				'witchs_broom' => 0,
				'totalFruits' => 0,
				'visitDates' => [],
			];

			$stages = [
				'bobbin', 'small', 'medium', 'medium2', 'medium3',
				'adult', 'adult2', 'mature', 'mature2', 'mature3', 'mature4'
			];

			foreach ($treeVisits as $visit) {
				$totals['visitDates'][] = $visit->date;

				foreach ($stages as $stage) {
					$data = $visit->$stage;

					if ($data) {
						$totals['harvested']     += $data->harvested ?? 0;
						$totals['loss']          += $data->loss ?? 0;
						$totals['rat']           += $data->rat ?? 0;
						$totals['piece']         += $data->piece ?? 0;
						$totals['rotten']        += $data->rotten ?? 0;
						$totals['witchs_broom']  += $data->witchs_broom ?? 0;
						$totals['totalFruits']   += $data->total ?? 0;
					}
				}
			}

			$totals['visitDates'] = array_values(array_unique($totals['visitDates']));

			Log::info('Distribuição de frutos otimizada:', $totals);

			return response()->json($totals);
		} catch (\Exception $e) {
			Log::error('Erro ao obter distribuição de frutos: ' . $e->getMessage());
			return response()->json(['error' => 'Erro ao processar os dados.'], 500);
		}
	}

	public function getCollectionDataHarvested(Request $request)
	{
		try {
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);

			$start = Carbon::createFromFormat('F/Y', "{$months[$startMonth]}/$startYear")->startOfMonth();
			$end = Carbon::createFromFormat('F/Y', "{$months[$endMonth]}/$endYear")->endOfMonth();

			$totals = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->leftJoin('adults', 'tree_visits.adult_id', '=', 'adults.id')
				->leftJoin('adults2', 'tree_visits.adult2_id', '=', 'adults2.id')
				->leftJoin('matures', 'tree_visits.mature_id', '=', 'matures.id')
				->leftJoin('matures2', 'tree_visits.mature2_id', '=', 'matures2.id')
				->leftJoin('matures3', 'tree_visits.mature3_id', '=', 'matures3.id')
				->leftJoin('matures4', 'tree_visits.mature4_id', '=', 'matures4.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->where('homogeneous_areas.id', $request->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$start, $end])
				->selectRaw("
					DATE(tree_visits.date) as date,
					SUM(
						COALESCE(mediums3.harvested, 0) +
						COALESCE(adults.harvested, 0) +
						COALESCE(adults2.harvested, 0) +
						COALESCE(matures.harvested, 0) +
						COALESCE(matures2.harvested, 0) +
						COALESCE(matures3.harvested, 0) +
						COALESCE(matures4.harvested, 0)
					) as total
				")
				->groupByRaw('DATE(tree_visits.date)')
				->orderBy('date')
				->pluck('total', 'date'); // Retorna array associativo: ['2025-01-01' => 25, ...]

			return response()->json([
				'areaId' => $request->areaHomogeneaId,
				'totals' => $totals,
			]);

		} catch (\Exception $e) {
			Log::error('Erro ao buscar dados de coleta:', [
				'message' => $e->getMessage(),
				'trace' => $e->getTraceAsString(),
			]);

			return response()->json(['error' => 'Erro ao obter os dados.'], 500);
		}
	}


	public function getCollectionDataLoss(Request $request)
	{
		try {
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);

			$start = Carbon::createFromFormat('F/Y', "{$months[$startMonth]}/$startYear")->startOfMonth();
			$end = Carbon::createFromFormat('F/Y', "{$months[$endMonth]}/$endYear")->endOfMonth();

			$totals = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->leftJoin('smalls', 'tree_visits.small_id', '=', 'smalls.id')
				->leftJoin('mediums', 'tree_visits.medium_id', '=', 'mediums.id')
				->leftJoin('mediums2', 'tree_visits.medium2_id', '=', 'mediums2.id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->leftJoin('adults', 'tree_visits.adult_id', '=', 'adults.id')
				->leftJoin('adults2', 'tree_visits.adult2_id', '=', 'adults2.id')
				->leftJoin('matures', 'tree_visits.mature_id', '=', 'matures.id')
				->leftJoin('matures2', 'tree_visits.mature2_id', '=', 'matures2.id')
				->leftJoin('matures3', 'tree_visits.mature3_id', '=', 'matures3.id')
				->leftJoin('matures4', 'tree_visits.mature4_id', '=', 'matures4.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->where('homogeneous_areas.id', $request->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$start, $end])
				->selectRaw("
					DATE(tree_visits.date) as date,
					SUM(
						COALESCE(smalls.loss, 0) +
						COALESCE(mediums.loss, 0) +
						COALESCE(mediums2.loss, 0) +
						COALESCE(mediums3.loss, 0) +
						COALESCE(adults.loss, 0) +
						COALESCE(adults2.loss, 0) +
						COALESCE(matures.loss, 0) +
						COALESCE(matures2.loss, 0) +
						COALESCE(matures3.loss, 0) +
						COALESCE(matures4.loss, 0)
					) as total
				")
				->groupByRaw('DATE(tree_visits.date)')
				->orderBy('date')
				->pluck('total', 'date'); // Ex: ['2025-04-02' => 23.7]

			return response()->json([
				'areaId' => $request->areaHomogeneaId,
				'totals' => $totals
			]);

		} catch (\Exception $e) {
			Log::error('Erro ao buscar dados de perdas:', [
				'message' => $e->getMessage(),
				'trace' => $e->getTraceAsString(),
			]);

			return response()->json(['error' => 'Erro ao obter os dados.'], 500);
		}
	}

	public function getCollectionDataWB(Request $request)
	{
		try {
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);

			$start = Carbon::createFromFormat('F/Y', "{$months[$startMonth]}/$startYear")->startOfMonth();
			$end = Carbon::createFromFormat('F/Y', "{$months[$endMonth]}/$endYear")->endOfMonth();

			$totals = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->leftJoin('smalls', 'tree_visits.small_id', '=', 'smalls.id')
				->leftJoin('mediums2', 'tree_visits.medium2_id', '=', 'mediums2.id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->leftJoin('adults', 'tree_visits.adult_id', '=', 'adults.id')
				->leftJoin('adults2', 'tree_visits.adult2_id', '=', 'adults2.id')
				->leftJoin('matures', 'tree_visits.mature_id', '=', 'matures.id')
				->leftJoin('matures2', 'tree_visits.mature2_id', '=', 'matures2.id')
				->leftJoin('matures3', 'tree_visits.mature3_id', '=', 'matures3.id')
				->leftJoin('matures4', 'tree_visits.mature4_id', '=', 'matures4.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->where('homogeneous_areas.id', $request->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$start, $end])
				->selectRaw("
					DATE(tree_visits.date) as date,
					SUM(
						COALESCE(smalls.witchs_broom, 0) +
						COALESCE(mediums2.witchs_broom, 0) +
						COALESCE(mediums3.witchs_broom, 0) +
						COALESCE(adults.witchs_broom, 0) +
						COALESCE(adults2.witchs_broom, 0) +
						COALESCE(matures.witchs_broom, 0) +
						COALESCE(matures2.witchs_broom, 0) +
						COALESCE(matures3.witchs_broom, 0) +
						COALESCE(matures4.witchs_broom, 0)
					) as total
				")
				->groupByRaw('DATE(tree_visits.date)')
				->orderBy('date')
				->pluck('total', 'date');

			return response()->json([
				'areaId' => $request->areaHomogeneaId,
				'totals' => $totals
			]);

		} catch (\Exception $e) {
			Log::error('Erro ao buscar dados de vassoura-de-bruxa:', [
				'message' => $e->getMessage(),
				'trace' => $e->getTraceAsString(),
			]);

			return response()->json(['error' => 'Erro ao obter os dados'], 500);
		}
	}

	public function getCollectionDataRotten(Request $request)
	{
		try {
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			Log::info('Request Data', [
				'propertyId' => $request->propertyId,
				'areaHomogeneaId' => $request->areaHomogeneaId,
				'startDate' => $request->startDate,
				'endDate' => $request->endDate,
			]);

			// Conversão das datas
			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);

			$start = Carbon::createFromFormat('F/Y', "{$months[$startMonth]}/$startYear")->startOfMonth();
			$end = Carbon::createFromFormat('F/Y', "{$months[$endMonth]}/$endYear")->endOfMonth();

			Log::info('Datas convertidas:', [
				'startDate' => $start->format('Y-m-d'),
				'endDate' => $end->format('Y-m-d')
			]);

			$totals = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->leftJoin('mediums2', 'tree_visits.medium2_id', '=', 'mediums2.id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->leftJoin('adults', 'tree_visits.adult_id', '=', 'adults.id')
				->leftJoin('adults2', 'tree_visits.adult2_id', '=', 'adults2.id')
				->leftJoin('matures', 'tree_visits.mature_id', '=', 'matures.id')
				->leftJoin('matures2', 'tree_visits.mature2_id', '=', 'matures2.id')
				->leftJoin('matures3', 'tree_visits.mature3_id', '=', 'matures3.id')
				->leftJoin('matures4', 'tree_visits.mature4_id', '=', 'matures4.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->where('homogeneous_areas.id', $request->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$start, $end])
				->selectRaw("
					DATE(tree_visits.date) as date,
					SUM(
						COALESCE(mediums2.rotten, 0) +
						COALESCE(mediums3.rotten, 0) +
						COALESCE(adults.rotten, 0) +
						COALESCE(adults2.rotten, 0) +
						COALESCE(matures.rotten, 0) +
						COALESCE(matures2.rotten, 0) +
						COALESCE(matures3.rotten, 0) +
						COALESCE(matures4.rotten, 0)
					) as total
				")
				->groupByRaw('DATE(tree_visits.date)')
				->orderBy('date')
				->pluck('total', 'date');

			return response()->json([
				'areaId' => $request->areaHomogeneaId,
				'totals' => $totals
			]);

		} catch (\Exception $e) {
			Log::error('Erro ao buscar dados de frutos podres:', [
				'message' => $e->getMessage(),
				'trace' => $e->getTraceAsString()
			]);

			return response()->json(['error' => 'Erro ao obter os dados'], 500);
		}
	}

	public function getCollectionDataPiece(Request $request)
	{
		try {
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			Log::info('Request Data', [
				'propertyId' => $request->propertyId,
				'areaHomogeneaId' => $request->areaHomogeneaId,
				'startDate' => $request->startDate,
				'endDate' => $request->endDate,
			]);

			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);

			$start = Carbon::createFromFormat('F/Y', "{$months[$startMonth]}/$startYear")->startOfMonth();
			$end = Carbon::createFromFormat('F/Y', "{$months[$endMonth]}/$endYear")->endOfMonth();

			Log::info('Datas convertidas:', [
				'startDate' => $start->format('Y-m-d'),
				'endDate' => $end->format('Y-m-d')
			]);

			$totals = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->leftJoin('smalls', 'tree_visits.small_id', '=', 'smalls.id')
				->leftJoin('mediums', 'tree_visits.medium_id', '=', 'mediums.id')
				->leftJoin('mediums2', 'tree_visits.medium2_id', '=', 'mediums2.id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->where('homogeneous_areas.id', $request->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$start, $end])
				->selectRaw("
					DATE(tree_visits.date) as date,
					SUM(
						COALESCE(smalls.piece, 0) +
						COALESCE(mediums.piece, 0) +
						COALESCE(mediums2.piece, 0) +
						COALESCE(mediums3.piece, 0)
					) as total
				")
				->groupByRaw('DATE(tree_visits.date)')
				->orderBy('date')
				->pluck('total', 'date');

			return response()->json([
				'areaId' => $request->areaHomogeneaId,
				'totals' => $totals
			]);

		} catch (\Exception $e) {
			Log::error('Erro ao buscar dados de frutos pecos:', [
				'message' => $e->getMessage(),
				'trace' => $e->getTraceAsString()
			]);

			return response()->json(['error' => 'Erro ao obter os dados'], 500);
		}
	}

	public function getCollectionDataRat(Request $request)
	{
		try {
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			Log::info('Request Data', [
				'propertyId' => $request->propertyId,
				'areaHomogeneaId' => $request->areaHomogeneaId,
				'startDate' => $request->startDate,
				'endDate' => $request->endDate,
			]);

			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);

			$start = Carbon::createFromFormat('F/Y', "{$months[$startMonth]}/$startYear")->startOfMonth();
			$end = Carbon::createFromFormat('F/Y', "{$months[$endMonth]}/$endYear")->endOfMonth();

			Log::info('Datas convertidas:', [
				'startDate' => $start->format('Y-m-d'),
				'endDate' => $end->format('Y-m-d'),
			]);

			$totals = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->leftJoin('mediums2', 'tree_visits.medium2_id', '=', 'mediums2.id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->leftJoin('adults', 'tree_visits.adult_id', '=', 'adults.id')
				->leftJoin('adults2', 'tree_visits.adult2_id', '=', 'adults2.id')
				->leftJoin('matures', 'tree_visits.mature_id', '=', 'matures.id')
				->leftJoin('matures2', 'tree_visits.mature2_id', '=', 'matures2.id')
				->leftJoin('matures3', 'tree_visits.mature3_id', '=', 'matures3.id')
				->leftJoin('matures4', 'tree_visits.mature4_id', '=', 'matures4.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->where('homogeneous_areas.id', $request->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$start, $end])
				->selectRaw("
					DATE(tree_visits.date) as date,
					SUM(
						COALESCE(mediums2.rat, 0) +
						COALESCE(mediums3.rat, 0) +
						COALESCE(adults.rat, 0) +
						COALESCE(adults2.rat, 0) +
						COALESCE(matures.rat, 0) +
						COALESCE(matures2.rat, 0) +
						COALESCE(matures3.rat, 0) +
						COALESCE(matures4.rat, 0)
					) as total
				")
				->groupByRaw('DATE(tree_visits.date)')
				->orderBy('date')
				->pluck('total', 'date');

			return response()->json([
				'areaId' => $request->areaHomogeneaId,
				'totals' => $totals
			]);

		} catch (\Exception $e) {
			Log::error('Erro ao buscar dados de frutos comidos por ratos:', [
				'error_message' => $e->getMessage(),
				'trace' => $e->getTraceAsString()
			]);
			return response()->json(['error' => 'Erro ao obter os dados'], 500);
		}
	}

	public function getCollectionDataHarvestedHA(Request $request)
	{
		try {
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			Log::info('Request Data', [
				'propertyId' => $request->propertyId,
				'areaHomogeneaId' => $request->areaHomogeneaId,
				'startDate' => $request->startDate,
				'endDate' => $request->endDate,
			]);

			// Conversão de datas
			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);

			$start = Carbon::createFromFormat('F/Y', "{$months[$startMonth]}/$startYear")->startOfMonth();
			$end = Carbon::createFromFormat('F/Y', "{$months[$endMonth]}/$endYear")->endOfMonth();

			Log::info('Datas convertidas:', [
				'startDate' => $start->format('Y-m-d'),
				'endDate' => $end->format('Y-m-d'),
			]);

			// SQL otimizado
			$totals = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->leftJoin('adults', 'tree_visits.adult_id', '=', 'adults.id')
				->leftJoin('adults2', 'tree_visits.adult2_id', '=', 'adults2.id')
				->leftJoin('matures', 'tree_visits.mature_id', '=', 'matures.id')
				->leftJoin('matures2', 'tree_visits.mature2_id', '=', 'matures2.id')
				->leftJoin('matures3', 'tree_visits.mature3_id', '=', 'matures3.id')
				->leftJoin('matures4', 'tree_visits.mature4_id', '=', 'matures4.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->where('homogeneous_areas.id', $request->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$start, $end])
				->selectRaw("
					DATE_FORMAT(tree_visits.date, '%d/%m') as day_month,
					SUM(
						COALESCE(mediums3.harvested, 0) +
						COALESCE(adults.harvested, 0) +
						COALESCE(adults2.harvested, 0) +
						COALESCE(matures.harvested, 0) +
						COALESCE(matures2.harvested, 0) +
						COALESCE(matures3.harvested, 0) +
						COALESCE(matures4.harvested, 0)
					) as total
				")
				->groupByRaw("day_month")
				->get()
				->pluck('total', 'day_month')
				->toArray();

			// Ordenar como antes: meses de outubro (10) em diante vêm antes
			uksort($totals, function ($a, $b) {
				[$dayA, $monthA] = explode('/', $a);
				[$dayB, $monthB] = explode('/', $b);

				$monthA = (int)$monthA;
				$monthB = (int)$monthB;

				$adjustedA = $monthA >= 10 ? $monthA - 12 : $monthA;
				$adjustedB = $monthB >= 10 ? $monthB - 12 : $monthB;

				if ($adjustedA === $adjustedB) {
					return (int)$dayA <=> (int)$dayB;
				}

				return $adjustedA <=> $adjustedB;
			});

			// Retornar apenas os valores em array
			return response()->json(array_values($totals));

		} catch (\Exception $e) {
			Log::error('Erro ao buscar dados colhidos por HA:', [
				'error_message' => $e->getMessage(),
				'trace' => $e->getTraceAsString()
			]);
			return response()->json(['error' => 'Erro ao obter os dados'], 500);
		}
	}

	public function getCollectionDataLossHA(Request $request)
	{
		try {
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			Log::info('Request Data', [
				'propertyId' => $request->propertyId,
				'areaHomogeneaId' => $request->areaHomogeneaId,
				'startDate' => $request->startDate,
				'endDate' => $request->endDate,
			]);

			// Mapeamento de meses PT-BR para EN
			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			// Conversão de datas
			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);
			$start = Carbon::createFromFormat('F/Y', "{$months[$startMonth]}/$startYear")->startOfMonth();
			$end = Carbon::createFromFormat('F/Y', "{$months[$endMonth]}/$endYear")->endOfMonth();

			Log::info('Datas convertidas:', [
				'startDate' => $start->format('Y-m-d'),
				'endDate' => $end->format('Y-m-d'),
			]);

			// Consulta otimizada com soma no SQL
			$totals = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->leftJoin('smalls', 'tree_visits.small_id', '=', 'smalls.id')
				->leftJoin('mediums', 'tree_visits.medium_id', '=', 'mediums.id')
				->leftJoin('mediums2', 'tree_visits.medium2_id', '=', 'mediums2.id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->leftJoin('adults', 'tree_visits.adult_id', '=', 'adults.id')
				->leftJoin('adults2', 'tree_visits.adult2_id', '=', 'adults2.id')
				->leftJoin('matures', 'tree_visits.mature_id', '=', 'matures.id')
				->leftJoin('matures2', 'tree_visits.mature2_id', '=', 'matures2.id')
				->leftJoin('matures3', 'tree_visits.mature3_id', '=', 'matures3.id')
				->leftJoin('matures4', 'tree_visits.mature4_id', '=', 'matures4.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->where('homogeneous_areas.id', $request->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$start, $end])
				->selectRaw("
					DATE_FORMAT(tree_visits.date, '%d/%m') as day_month,
					SUM(
						COALESCE(smalls.loss, 0) +
						COALESCE(mediums.loss, 0) +
						COALESCE(mediums2.loss, 0) +
						COALESCE(mediums3.loss, 0) +
						COALESCE(adults.loss, 0) +
						COALESCE(adults2.loss, 0) +
						COALESCE(matures.loss, 0) +
						COALESCE(matures2.loss, 0) +
						COALESCE(matures3.loss, 0) +
						COALESCE(matures4.loss, 0)
					) as total
				")
				->groupByRaw("day_month")
				->get()
				->pluck('total', 'day_month')
				->toArray();

			// Ordenação estilo safra (outubro a setembro)
			uksort($totals, function ($a, $b) {
				[$dayA, $monthA] = explode('/', $a);
				[$dayB, $monthB] = explode('/', $b);

				$monthA = (int)$monthA;
				$monthB = (int)$monthB;

				$adjustedA = $monthA >= 10 ? $monthA - 12 : $monthA;
				$adjustedB = $monthB >= 10 ? $monthB - 12 : $monthB;

				return $adjustedA === $adjustedB
					? (int)$dayA <=> (int)$dayB
					: $adjustedA <=> $adjustedB;
			});

			return response()->json(array_values($totals));

		} catch (\Exception $e) {
			Log::error('Erro ao buscar dados de perdas por HA:', [
				'error_message' => $e->getMessage(),
				'trace' => $e->getTraceAsString()
			]);
			return response()->json(['error' => 'Erro ao obter os dados'], 500);
		}
	}

	//Continuar daqui
	public function getCollectionDataWBHA(Request $request)
	{
		try {
			// Validação dos parâmetros
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			Log::info('Request Data', [
				'propertyId' => $request->propertyId,
				'areaHomogeneaId' => $request->areaHomogeneaId,
				'startDate' => $request->startDate,
				'endDate' => $request->endDate,
			]);

			// Conversão de meses PT-BR para EN
			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			// Conversão de datas
			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);
			$start = Carbon::createFromFormat('F/Y', "{$months[$startMonth]}/$startYear")->startOfMonth();
			$end = Carbon::createFromFormat('F/Y', "{$months[$endMonth]}/$endYear")->endOfMonth();

			Log::info('Datas convertidas:', [
				'startDate' => $start->format('Y-m-d'),
				'endDate' => $end->format('Y-m-d'),
			]);

			// Consulta direta e agregada no SQL
			$totals = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->leftJoin('smalls', 'tree_visits.small_id', '=', 'smalls.id')
				->leftJoin('mediums2', 'tree_visits.medium2_id', '=', 'mediums2.id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->leftJoin('adults', 'tree_visits.adult_id', '=', 'adults.id')
				->leftJoin('adults2', 'tree_visits.adult2_id', '=', 'adults2.id')
				->leftJoin('matures', 'tree_visits.mature_id', '=', 'matures.id')
				->leftJoin('matures2', 'tree_visits.mature2_id', '=', 'matures2.id')
				->leftJoin('matures3', 'tree_visits.mature3_id', '=', 'matures3.id')
				->leftJoin('matures4', 'tree_visits.mature4_id', '=', 'matures4.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->where('homogeneous_areas.id', $request->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$start, $end])
				->selectRaw("
					DATE_FORMAT(tree_visits.date, '%d/%m') as day_month,
					SUM(
						COALESCE(smalls.witchs_broom, 0) +
						COALESCE(mediums2.witchs_broom, 0) +
						COALESCE(mediums3.witchs_broom, 0) +
						COALESCE(adults.witchs_broom, 0) +
						COALESCE(adults2.witchs_broom, 0) +
						COALESCE(matures.witchs_broom, 0) +
						COALESCE(matures2.witchs_broom, 0) +
						COALESCE(matures3.witchs_broom, 0) +
						COALESCE(matures4.witchs_broom, 0)
					) as total
				")
				->groupByRaw("day_month")
				->get()
				->pluck('total', 'day_month')
				->toArray();

			// Ordenação estilo safra (outubro a setembro)
			uksort($totals, function ($a, $b) {
				[$dayA, $monthA] = explode('/', $a);
				[$dayB, $monthB] = explode('/', $b);

				$adjustedA = (int)$monthA >= 10 ? (int)$monthA - 12 : (int)$monthA;
				$adjustedB = (int)$monthB >= 10 ? (int)$monthB - 12 : (int)$monthB;

				return $adjustedA === $adjustedB
					? (int)$dayA <=> (int)$dayB
					: $adjustedA <=> $adjustedB;
			});

			return response()->json(array_values($totals));

		} catch (\Exception $e) {
			Log::error('Erro ao buscar dados de vassoura-de-bruxa por HA:', [
				'error_message' => $e->getMessage(),
				'trace' => $e->getTraceAsString()
			]);
			return response()->json(['error' => 'Erro ao obter os dados'], 500);
		}
	}

	public function getCollectionDataPieceHA(Request $request)
	{
		try {
			// Validação dos parâmetros
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			Log::info('Request Data', [
				'propertyId' => $request->propertyId,
				'areaHomogeneaId' => $request->areaHomogeneaId,
				'startDate' => $request->startDate,
				'endDate' => $request->endDate,
			]);

			// Conversão de meses PT-BR para EN
			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			// Conversão de datas
			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);
			$start = Carbon::createFromFormat('F/Y', "{$months[$startMonth]}/$startYear")->startOfMonth();
			$end = Carbon::createFromFormat('F/Y', "{$months[$endMonth]}/$endYear")->endOfMonth();

			Log::info('Datas convertidas:', [
				'startDate' => $start->format('Y-m-d'),
				'endDate' => $end->format('Y-m-d'),
			]);

			// Consulta agregada diretamente no SQL
			$totals = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->leftJoin('smalls', 'tree_visits.small_id', '=', 'smalls.id')
				->leftJoin('mediums', 'tree_visits.medium_id', '=', 'mediums.id')
				->leftJoin('mediums2', 'tree_visits.medium2_id', '=', 'mediums2.id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->where('homogeneous_areas.id', $request->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$start, $end])
				->selectRaw("
					DATE_FORMAT(tree_visits.date, '%d/%m') as day_month,
					SUM(
						COALESCE(smalls.piece, 0) +
						COALESCE(mediums.piece, 0) +
						COALESCE(mediums2.piece, 0) +
						COALESCE(mediums3.piece, 0)
					) as total
				")
				->groupByRaw("day_month")
				->get()
				->pluck('total', 'day_month')
				->toArray();

			// Ordenação estilo safra (outubro a setembro)
			uksort($totals, function ($a, $b) {
				[$dayA, $monthA] = explode('/', $a);
				[$dayB, $monthB] = explode('/', $b);

				$adjustedA = (int)$monthA >= 10 ? (int)$monthA - 12 : (int)$monthA;
				$adjustedB = (int)$monthB >= 10 ? (int)$monthB - 12 : (int)$monthB;

				return $adjustedA === $adjustedB
					? (int)$dayA <=> (int)$dayB
					: $adjustedA <=> $adjustedB;
			});

			return response()->json(array_values($totals));

		} catch (\Exception $e) {
			Log::error('Erro ao buscar dados de frutos partidos por HA:', [
				'error_message' => $e->getMessage(),
				'trace' => $e->getTraceAsString()
			]);
			return response()->json(['error' => 'Erro ao obter os dados'], 500);
		}
	}


	public function getCollectionDataRatHA(Request $request)
	{
		try {
			// Validação dos parâmetros
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			Log::info('Request Data', [
				'propertyId' => $request->propertyId,
				'areaHomogeneaId' => $request->areaHomogeneaId,
				'startDate' => $request->startDate,
				'endDate' => $request->endDate,
			]);

			// Mapeamento de meses
			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			// Conversão de datas
			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);
			$start = Carbon::createFromFormat('F/Y', "{$months[$startMonth]}/$startYear")->startOfMonth();
			$end = Carbon::createFromFormat('F/Y', "{$months[$endMonth]}/$endYear")->endOfMonth();

			Log::info('Datas convertidas:', [
				'startDate' => $start->format('Y-m-d'),
				'endDate' => $end->format('Y-m-d'),
			]);

			// Consulta otimizada com agregação de dados de rato
			$totals = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->leftJoin('mediums2', 'tree_visits.medium2_id', '=', 'mediums2.id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->leftJoin('adults', 'tree_visits.adult_id', '=', 'adults.id')
				->leftJoin('adults2', 'tree_visits.adult2_id', '=', 'adults2.id')
				->leftJoin('matures', 'tree_visits.mature_id', '=', 'matures.id')
				->leftJoin('matures2', 'tree_visits.mature2_id', '=', 'matures2.id')
				->leftJoin('matures3', 'tree_visits.mature3_id', '=', 'matures3.id')
				->leftJoin('matures4', 'tree_visits.mature4_id', '=', 'matures4.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->where('homogeneous_areas.id', $request->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$start, $end])
				->selectRaw("
					DATE_FORMAT(tree_visits.date, '%d/%m') as day_month,
					SUM(
						COALESCE(mediums2.rat, 0) +
						COALESCE(mediums3.rat, 0) +
						COALESCE(adults.rat, 0) +
						COALESCE(adults2.rat, 0) +
						COALESCE(matures.rat, 0) +
						COALESCE(matures2.rat, 0) +
						COALESCE(matures3.rat, 0) +
						COALESCE(matures4.rat, 0)
					) as total
				")
				->groupByRaw("day_month")
				->get()
				->pluck('total', 'day_month')
				->toArray();

			// Ordenar estilo safra (outubro a setembro)
			uksort($totals, function ($a, $b) {
				[$dayA, $monthA] = explode('/', $a);
				[$dayB, $monthB] = explode('/', $b);

				$adjustedA = (int)$monthA >= 10 ? (int)$monthA - 12 : (int)$monthA;
				$adjustedB = (int)$monthB >= 10 ? (int)$monthB - 12 : (int)$monthB;

				return $adjustedA === $adjustedB
					? (int)$dayA <=> (int)$dayB
					: $adjustedA <=> $adjustedB;
			});

			return response()->json(array_values($totals));

		} catch (\Exception $e) {
			Log::error('Erro ao buscar dados de rat por HA:', [
				'error_message' => $e->getMessage(),
				'trace' => $e->getTraceAsString()
			]);
			return response()->json(['error' => 'Erro ao obter os dados'], 500);
		}
	}


	public function getCollectionDataRottenHA(Request $request)
	{
		try {
			// Validação dos parâmetros
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			Log::info('Request Data', [
				'propertyId' => $request->propertyId,
				'areaHomogeneaId' => $request->areaHomogeneaId,
				'startDate' => $request->startDate,
				'endDate' => $request->endDate,
			]);

			// Conversão de meses PT-BR para EN
			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			// Conversão de datas
			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);
			$start = Carbon::createFromFormat('F/Y', "{$months[$startMonth]}/$startYear")->startOfMonth();
			$end = Carbon::createFromFormat('F/Y', "{$months[$endMonth]}/$endYear")->endOfMonth();

			Log::info('Datas convertidas:', [
				'startDate' => $start->format('Y-m-d'),
				'endDate' => $end->format('Y-m-d'),
			]);

			// Consulta SQL com agregação de frutos podres
			$totals = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->leftJoin('mediums2', 'tree_visits.medium2_id', '=', 'mediums2.id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->leftJoin('adults', 'tree_visits.adult_id', '=', 'adults.id')
				->leftJoin('adults2', 'tree_visits.adult2_id', '=', 'adults2.id')
				->leftJoin('matures', 'tree_visits.mature_id', '=', 'matures.id')
				->leftJoin('matures2', 'tree_visits.mature2_id', '=', 'matures2.id')
				->leftJoin('matures3', 'tree_visits.mature3_id', '=', 'matures3.id')
				->leftJoin('matures4', 'tree_visits.mature4_id', '=', 'matures4.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->where('homogeneous_areas.id', $request->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$start, $end])
				->selectRaw("
					DATE_FORMAT(tree_visits.date, '%d/%m') as day_month,
					SUM(
						COALESCE(mediums2.rotten, 0) +
						COALESCE(mediums3.rotten, 0) +
						COALESCE(adults.rotten, 0) +
						COALESCE(adults2.rotten, 0) +
						COALESCE(matures.rotten, 0) +
						COALESCE(matures2.rotten, 0) +
						COALESCE(matures3.rotten, 0) +
						COALESCE(matures4.rotten, 0)
					) as total
				")
				->groupByRaw("day_month")
				->get()
				->pluck('total', 'day_month')
				->toArray();

			// Ordenação estilo safra (outubro a setembro)
			uksort($totals, function ($a, $b) {
				[$dayA, $monthA] = explode('/', $a);
				[$dayB, $monthB] = explode('/', $b);

				$adjustedA = (int)$monthA >= 10 ? (int)$monthA - 12 : (int)$monthA;
				$adjustedB = (int)$monthB >= 10 ? (int)$monthB - 12 : (int)$monthB;

				return $adjustedA === $adjustedB
					? (int)$dayA <=> (int)$dayB
					: $adjustedA <=> $adjustedB;
			});

			return response()->json(array_values($totals));

		} catch (\Exception $e) {
			Log::error('Erro ao buscar dados de frutos podres por HA:', [
				'error_message' => $e->getMessage(),
				'trace' => $e->getTraceAsString()
			]);
			return response()->json(['error' => 'Erro ao obter os dados'], 500);
		}
	}

	public function getCollectionDates(Request $request)
	{
		try {
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
			]);

			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March', 'Abril' => 'April',
				'Maio' => 'May', 'Junho' => 'June', 'Julho' => 'July', 'Agosto' => 'August',
				'Setembro' => 'September', 'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			list($startMonth, $startYear) = explode('/', $request->input('startDate'));
			list($endMonth, $endYear) = explode('/', $request->input('endDate'));

			$startDate = Carbon::createFromFormat('F/Y', $months[$startMonth] . '/' . $startYear)->startOfMonth();
			$endDate = Carbon::createFromFormat('F/Y', $months[$endMonth] . '/' . $endYear)->endOfMonth();

			$samplingPointIds = SamplingPoint::whereHas('stratum', function ($query) use ($request) {
				$query->where('property_id', $request->input('propertyId'));
			})->pluck('id');

			$visitDates = TreeVisit::join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->whereIn('trees.sampling_point_id', $samplingPointIds)
				->where('homogeneous_areas.id', $request->input('areaHomogeneaId'))
				->whereBetween('tree_visits.date', [$startDate, $endDate])
				->pluck('tree_visits.date')
				->unique()
				->sort()
				->values();

			return response()->json($visitDates);
		} catch (\Exception $e) {
			Log::error('Erro ao buscar datas de coleta:', ['error_message' => $e->getMessage()]);
			return response()->json(['error' => 'Erro ao obter as datas de visita'], 500);
		}
	}

	public function getTotalCocoaByRange(Request $request)
	{
		try {
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
				'faixaInicial' => 'required|integer|min:0|max:10',
				'faixaFinal' => 'required|integer|min:0|max:10',
				'coletaInicial' => 'required|string',
				'coletaFinal' => 'required|string',
			]);

			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			// Conversões de datas
			[$startMonth, $startYear] = explode('/', $request->startDate);
			$startDate = Carbon::createFromFormat('F/Y', $months[$startMonth] . '/' . $startYear)->startOfMonth();

			[$endMonth, $endYear] = explode('/', $request->endDate);
			$endDate = Carbon::createFromFormat('F/Y', $months[$endMonth] . '/' . $endYear)->endOfMonth();

			$coletaInicialDate = Carbon::createFromFormat('d/m/Y', $request->coletaInicial)->startOfDay();
			$coletaFinalDate = Carbon::createFromFormat('d/m/Y', $request->coletaFinal)->endOfDay();

			$faixaInicial = (int) $request->faixaInicial;
			$faixaFinal = (int) $request->faixaFinal;

			$faixas = [
				'bobbins', 'smalls', 'mediums', 'mediums2', 'mediums3',
				'adults', 'adults2', 'matures', 'matures2', 'matures3', 'matures4'
			];

			// Construir campos para SUM
			$selectFields = ["DATE(tree_visits.date) as visit_date"];
			for ($i = $faixaInicial; $i <= $faixaFinal; $i++) {
				$table = $faixas[$i];
				$alias = "faixa_$i";
				$selectFields[] = "SUM(COALESCE($table.total, 0)) as $alias";
			}

			// Executar a consulta otimizada
			$results = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				// LEFT JOINs com as tabelas de faixas
				->leftJoin('bobbins', 'tree_visits.bobbin_id', '=', 'bobbins.id')
				->leftJoin('smalls', 'tree_visits.small_id', '=', 'smalls.id')
				->leftJoin('mediums', 'tree_visits.medium_id', '=', 'mediums.id')
				->leftJoin('mediums2', 'tree_visits.medium2_id', '=', 'mediums2.id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->leftJoin('adults', 'tree_visits.adult_id', '=', 'adults.id')
				->leftJoin('adults2', 'tree_visits.adult2_id', '=', 'adults2.id')
				->leftJoin('matures', 'tree_visits.mature_id', '=', 'matures.id')
				->leftJoin('matures2', 'tree_visits.mature2_id', '=', 'matures2.id')
				->leftJoin('matures3', 'tree_visits.mature3_id', '=', 'matures3.id')
				->leftJoin('matures4', 'tree_visits.mature4_id', '=', 'matures4.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->where('homogeneous_areas.id', $request->areaHomogeneaId)
				->whereBetween('tree_visits.date', [$startDate, $endDate])
				->whereBetween('tree_visits.date', [$coletaInicialDate, $coletaFinalDate])
				->selectRaw(implode(', ', $selectFields))
				->groupBy('visit_date')
				->orderBy('visit_date')
				->get();

			// Preparar saída
			$labels = [];
			$datasets = [];
			$totalCocoa = [];

			$faixaLabels = [
				"0 - 21", "21 - 42", "42 - 63", "63 - 84", "84 - 105",
				"105 - 126", "126 - 147", "147 - 168", "168 - 189", "189 - 210", "> 210"
			];

			for ($i = $faixaInicial; $i <= $faixaFinal; $i++) {
				$datasets[] = [
					'label' => $faixaLabels[$i],
					'backgroundColor' => '#'.substr(md5($i), 0, 6), // cor aleatória
					'data' => []
				];
			}

			foreach ($results as $row) {
				$labels[] = Carbon::parse($row->visit_date)->format('d/m/Y');

				for ($i = $faixaInicial; $i <= $faixaFinal; $i++) {
					$datasets[$i - $faixaInicial]['data'][] = (int) $row->{"faixa_$i"};
				}

				$totalCocoa[$row->visit_date] = collect($row)->only(array_map(fn($i) => "faixa_$i", range($faixaInicial, $faixaFinal)))->toArray();
			}

			return response()->json([
				'success' => true,
				'data' => [
					'labels' => $labels,
					'datasets' => $datasets,
					'totalCocoa' => $totalCocoa
				]
			]);

		} catch (\Exception $e) {
			Log::error('Erro ao calcular total de cacau por faixa:', [
				'error_message' => $e->getMessage(),
				'trace' => $e->getTraceAsString()
			]);

			return response()->json(['error' => 'Erro ao obter os dados'], 500);
		}
	}

	/**
	 * Calcula o total de bilros e sua evolução ao longo do tempo (OTIMIZADO)
	 *
	 * @param Request $request
	 * @return \Illuminate\Http\JsonResponse
	 */
	public function getTotalBobbinsByPeriod(Request $request)
	{
		try {
			$request->validate([
				'propertyId' => 'required|integer|exists:properties,id',
				'areaHomogeneaId' => 'required|integer|exists:homogeneous_areas,id',
				'startDate' => 'required|string',
				'endDate' => 'required|string',
				'coletaDate' => 'required|string',
			]);

			// Mapeamento de meses
			$months = [
				'Janeiro' => 'January', 'Fevereiro' => 'February', 'Março' => 'March',
				'Abril' => 'April', 'Maio' => 'May', 'Junho' => 'June',
				'Julho' => 'July', 'Agosto' => 'August', 'Setembro' => 'September',
				'Outubro' => 'October', 'Novembro' => 'November', 'Dezembro' => 'December'
			];

			// Conversão de datas
			[$startMonth, $startYear] = explode('/', $request->startDate);
			[$endMonth, $endYear] = explode('/', $request->endDate);
			$start = Carbon::createFromFormat('F/Y', "{$months[$startMonth]}/$startYear")->startOfMonth();
			$end = Carbon::createFromFormat('F/Y', "{$months[$endMonth]}/$endYear")->endOfMonth();
			$coletaDate = Carbon::createFromFormat('d/m/Y', $request->coletaDate);

			// Calcular próxima safra
			$nextStart = Carbon::createFromFormat('F/Y', "October/$endYear")->startOfMonth();
			$nextEnd = Carbon::createFromFormat('F/Y', "September/" . ($endYear + 1))->endOfMonth();

			// Consulta SQL otimizada para buscar todos os dados necessários
			$data = DB::table('tree_visits')
				->join('trees', 'trees.id', '=', 'tree_visits.tree_id')
				->join('sampling_points', 'sampling_points.id', '=', 'trees.sampling_point_id')
				->join('strata', 'strata.id', '=', 'sampling_points.stratum_id')
				->join('homogeneous_areas', 'homogeneous_areas.id', '=', 'strata.homogeneous_area_id')
				->leftJoin('bobbins', 'tree_visits.bobbin_id', '=', 'bobbins.id')
				->leftJoin('smalls', 'tree_visits.small_id', '=', 'smalls.id')
				->leftJoin('mediums', 'tree_visits.medium_id', '=', 'mediums.id')
				->leftJoin('mediums2', 'tree_visits.medium2_id', '=', 'mediums2.id')
				->leftJoin('mediums3', 'tree_visits.medium3_id', '=', 'mediums3.id')
				->leftJoin('adults', 'tree_visits.adult_id', '=', 'adults.id')
				->leftJoin('adults2', 'tree_visits.adult2_id', '=', 'adults2.id')
				->leftJoin('matures', 'tree_visits.mature_id', '=', 'matures.id')
				->leftJoin('matures2', 'tree_visits.mature2_id', '=', 'matures2.id')
				->leftJoin('matures3', 'tree_visits.mature3_id', '=', 'matures3.id')
				->leftJoin('matures4', 'tree_visits.mature4_id', '=', 'matures4.id')
				->where('homogeneous_areas.property_id', $request->propertyId)
				->where('homogeneous_areas.id', $request->areaHomogeneaId)
				->where(function($query) use ($start, $end, $nextStart, $nextEnd) {
					$query->whereBetween('tree_visits.date', [$start, $end])
						  ->orWhereBetween('tree_visits.date', [$nextStart, $nextEnd]);
				})
				->selectRaw("
					tree_visits.date,
					COALESCE(bobbins.total, 0) as bobbin_total,
					COALESCE(smalls.piece, 0) as small_piece,
					COALESCE(smalls.loss, 0) as small_loss,
					COALESCE(smalls.witchs_broom, 0) as small_wb,
					COALESCE(mediums.piece, 0) as medium_piece,
					COALESCE(mediums.loss, 0) as medium_loss,
					COALESCE(mediums2.piece, 0) as medium2_piece,
					COALESCE(mediums2.loss, 0) as medium2_loss,
					COALESCE(mediums2.witchs_broom, 0) as medium2_wb,
					COALESCE(mediums2.rat, 0) as medium2_rat,
					COALESCE(mediums2.rotten, 0) as medium2_rotten,
					COALESCE(mediums3.piece, 0) as medium3_piece,
					COALESCE(mediums3.loss, 0) as medium3_loss,
					COALESCE(mediums3.witchs_broom, 0) as medium3_wb,
					COALESCE(mediums3.rat, 0) as medium3_rat,
					COALESCE(mediums3.rotten, 0) as medium3_rotten,
					COALESCE(mediums3.harvested, 0) as medium3_harvested,
					COALESCE(adults.loss, 0) as adult_loss,
					COALESCE(adults.witchs_broom, 0) as adult_wb,
					COALESCE(adults.rat, 0) as adult_rat,
					COALESCE(adults.rotten, 0) as adult_rotten,
					COALESCE(adults.harvested, 0) as adult_harvested,
					COALESCE(adults2.loss, 0) as adult2_loss,
					COALESCE(adults2.witchs_broom, 0) as adult2_wb,
					COALESCE(adults2.rat, 0) as adult2_rat,
					COALESCE(adults2.rotten, 0) as adult2_rotten,
					COALESCE(adults2.harvested, 0) as adult2_harvested,
					COALESCE(matures.loss, 0) as mature_loss,
					COALESCE(matures.witchs_broom, 0) as mature_wb,
					COALESCE(matures.rat, 0) as mature_rat,
					COALESCE(matures.rotten, 0) as mature_rotten,
					COALESCE(matures.harvested, 0) as mature_harvested,
					COALESCE(matures2.loss, 0) as mature2_loss,
					COALESCE(matures2.witchs_broom, 0) as mature2_wb,
					COALESCE(matures2.rat, 0) as mature2_rat,
					COALESCE(matures2.rotten, 0) as mature2_rotten,
					COALESCE(matures2.harvested, 0) as mature2_harvested,
					COALESCE(matures3.loss, 0) as mature3_loss,
					COALESCE(matures3.witchs_broom, 0) as mature3_wb,
					COALESCE(matures3.rat, 0) as mature3_rat,
					COALESCE(matures3.rotten, 0) as mature3_rotten,
					COALESCE(matures3.harvested, 0) as mature3_harvested,
					COALESCE(matures4.loss, 0) as mature4_loss,
					COALESCE(matures4.witchs_broom, 0) as mature4_wb,
					COALESCE(matures4.rat, 0) as mature4_rat,
					COALESCE(matures4.rotten, 0) as mature4_rotten,
					COALESCE(matures4.harvested, 0) as mature4_harvested
				")
				->orderBy('tree_visits.date')
				->get();

			// Processar dados otimizado
			$result = $this->processBobbinsDataOptimized($data, $coletaDate, $start, $end);

			return response()->json([
				'success' => true,
				'data' => $result
			]);

		} catch (\Exception $e) {
			Log::error('Erro ao calcular total de bilros:', [
				'error_message' => $e->getMessage(),
				'trace' => $e->getTraceAsString()
			]);
			return response()->json(['error' => 'Erro ao obter os dados'], 500);
		}
	}

	/**
	 * Processa os dados de bilros de forma otimizada usando dados já agregados do SQL
	 */
	private function processBobbinsDataOptimized($data, $coletaDate, $startDate, $endDate)
	{
		// Definir faixas de tamanho
		$faixas = [
			"0 - 21", "21 - 42", "42 - 63", "63 - 84", "84 - 105",
			"105 - 126", "126 - 147", "147 - 168", "168 - 189", "189 - 210", "> 210"
		];

		// Separar dados por período (safra atual e próxima)
		$currentSafraData = $data->filter(function($item) use ($startDate, $endDate) {
			$itemDate = Carbon::parse($item->date);
			return $itemDate->between($startDate, $endDate);
		});

		$nextSafraData = $data->filter(function($item) use ($startDate, $endDate) {
			$itemDate = Carbon::parse($item->date);
			return !$itemDate->between($startDate, $endDate);
		});

		// Obter datas únicas da safra atual
		$visitDates = $currentSafraData->pluck('date')->unique()->sort()->values()->toArray();

		// Encontrar índice da coleta selecionada
		$coletaIndex = array_search($coletaDate->format('Y-m-d'), array_map(function($date) {
			return Carbon::parse($date)->format('Y-m-d');
		}, $visitDates));

		if ($coletaIndex === false) {
			throw new \Exception('Data de coleta não encontrada nos dados');
		}

		// Calcular total de bilros na coleta selecionada usando SQL agregado
		$totalBobbins = $currentSafraData
			->where('date', $visitDates[$coletaIndex])
			->sum('bobbin_total');

		if ($totalBobbins === 0) {
			return [
				'labels' => [[$coletaDate->format('d/m/Y'), $faixas[0]]],
				'datasets' => $this->createEmptyDatasets(),
				'chartTitle' => 'Não houve bilros na coleta selecionada',
				'totalBobbins' => 0
			];
		}

		// Processar evolução dos bilros de forma otimizada
		$resultsSafra = $this->processBobbinsEvolutionOptimized($currentSafraData, $nextSafraData, $visitDates, $coletaIndex, $totalBobbins);

		// Preparar dados para o gráfico
		$chartData = $this->prepareBobbinsChartData($resultsSafra, $faixas, $totalBobbins);

		return $chartData;
	}

	/**
	 * Processa a evolução dos bilros de forma otimizada usando dados SQL agregados
	 */
	private function processBobbinsEvolutionOptimized($currentSafraData, $nextSafraData, $visitDates, $coletaIndex, $totalBobbins)
	{
		$resultsSafra = [];
		$startDate = \Carbon\Carbon::parse($visitDates[$coletaIndex])->format('Y-m-d');

		// Agrupar dados por data usando SQL agregado
		$groupedByDate = $currentSafraData->groupBy(function($item) {
			return Carbon::parse($item->date)->format('Y-m-d');
		});

		$orderedDates = $groupedByDate->keys()->sort()->values()->toArray();
		$startIndex = array_search($startDate, $orderedDates);

		if ($startIndex === false) {
			throw new \Exception('Data de início não encontrada');
		}

		$faixa = 0;
		$acumulate = [
			'piece' => 0, 'loss' => 0, 'witchsBroom' => 0,
			'rat' => 0, 'rotten' => 0, 'harvested' => 0,
		];

		// Processar dados da safra atual de forma otimizada
		$faixa = $this->processSafraDataOptimized($groupedByDate, $orderedDates, $startIndex + 1, 11, $faixa, $acumulate, $totalBobbins, $resultsSafra);

		// Processar dados da próxima safra se necessário
		if ($faixa < 11 && $nextSafraData->count() > 0) {
			$groupedByDateNext = $nextSafraData->groupBy(function($item) {
				return Carbon::parse($item->date)->format('Y-m-d');
			});
			$orderedDatesNext = $groupedByDateNext->keys()->sort()->values()->toArray();

			$this->processSafraDataOptimized($groupedByDateNext, $orderedDatesNext, 0, 11, $faixa, $acumulate, $totalBobbins, $resultsSafra);
		}

		return $this->shiftBobbinsResults($resultsSafra, $totalBobbins);
	}

	/**
	 * Processa dados de uma safra específica de forma otimizada
	 */
	private function processSafraDataOptimized($groupedByDate, $orderedDates, $startIndex, $maxFaixa, $faixaInicial, &$acumulate, $totalBobbins, &$resultsSafra)
	{
		$faixa = $faixaInicial;

		// Primeira passagem: calcular totais por faixa usando dados SQL agregados
		for ($i = $startIndex; $i < count($orderedDates) && $faixa < $maxFaixa; $i++) {
			$currentDate = $orderedDates[$i];
			$visits = $groupedByDate[$currentDate];

			foreach ($visits as $visit) {
				$result = $this->getTotalsForBobbinsRangeOptimized($faixa, $visit, $totalBobbins);

				if (!isset($resultsSafra[$currentDate])) {
					$resultsSafra[$currentDate] = [
						'date' => $currentDate,
						'bobbins' => $faixa === 0 ? $totalBobbins : 0,
						'piece' => 0, 'loss' => 0, 'witchsBroom' => 0,
						'rat' => 0, 'rotten' => 0, 'harvested' => 0,
					];
				}

				foreach ($result as $key => $value) {
					if ($key !== 'bobbins') {
						$resultsSafra[$currentDate][$key] += $value;
					}
				}
			}
			$faixa++;
		}

		// Segunda passagem: acumular valores
		for ($i = $startIndex; $i < count($orderedDates); $i++) {
			$date = $orderedDates[$i];
			if (isset($resultsSafra[$date])) {
				$data = &$resultsSafra[$date];

				$acumulate['piece'] += $data['piece'];
				$acumulate['loss'] += $data['loss'];
				$acumulate['witchsBroom'] += $data['witchsBroom'];
				$acumulate['rat'] += $data['rat'];
				$acumulate['rotten'] += $data['rotten'];
				$acumulate['harvested'] += $data['harvested'];

				$data['piece'] = $acumulate['piece'];
				$data['loss'] = $acumulate['loss'];
				$data['witchsBroom'] = $acumulate['witchsBroom'];
				$data['rat'] = $acumulate['rat'];
				$data['rotten'] = $acumulate['rotten'];
				$data['harvested'] = $acumulate['harvested'];

				$totalFinished = $data['piece'] + $data['loss'] + $data['witchsBroom'] + $data['rat'] + $data['rotten'] + $data['harvested'];
				$data['bobbins'] = max(0, $totalBobbins - $totalFinished);
			}
		}

		return $faixa;
	}

	/**
	 * Cria datasets vazios para quando não há bilros
	 */
	private function createEmptyDatasets()
	{
		$categories = [
			['key' => 'bobbins', 'label' => 'Frutos', 'color' => 'yellow'],
			['key' => 'harvested', 'label' => 'Colhido', 'color' => 'green'],
			['key' => 'piece', 'label' => 'Peco', 'color' => 'blue'],
			['key' => 'loss', 'label' => 'Perda', 'color' => 'gray'],
			['key' => 'witchsBroom', 'label' => 'Vassoura de Bruxa', 'color' => 'brown'],
			['key' => 'rat', 'label' => 'Rato', 'color' => 'red'],
			['key' => 'rotten', 'label' => 'Podre', 'color' => 'purple'],
		];

		return array_map(function($cat) {
			return [
				'label' => $cat['label'],
				'data' => [0],
				'backgroundColor' => $cat['color'],
				'stack' => 'a'
			];
		}, $categories);
	}

	/**
	 * Retorna os totais para uma faixa específica de bilros usando dados SQL otimizados
	 */
	private function getTotalsForBobbinsRangeOptimized($index, $visit, $totalBobbins)
	{
		$results = [
			'bobbins' => 0, 'piece' => 0, 'loss' => 0, 'witchsBroom' => 0,
			'rat' => 0, 'rotten' => 0, 'harvested' => 0,
		];

		switch($index) {
			case 0:
				$results['bobbins'] = $totalBobbins;
				break;
			case 1:
				$results['piece'] = $visit->small_piece ?? 0;
				$results['loss'] = $visit->small_loss ?? 0;
				$results['witchsBroom'] = $visit->small_wb ?? 0;
				break;
			case 2:
				$results['piece'] = $visit->medium_piece ?? 0;
				$results['loss'] = $visit->medium_loss ?? 0;
				break;
			case 3:
				$results['piece'] = $visit->medium2_piece ?? 0;
				$results['loss'] = $visit->medium2_loss ?? 0;
				$results['witchsBroom'] = $visit->medium2_wb ?? 0;
				$results['rat'] = $visit->medium2_rat ?? 0;
				$results['rotten'] = $visit->medium2_rotten ?? 0;
				break;
			case 4:
				$results['piece'] = $visit->medium3_piece ?? 0;
				$results['loss'] = $visit->medium3_loss ?? 0;
				$results['witchsBroom'] = $visit->medium3_wb ?? 0;
				$results['rat'] = $visit->medium3_rat ?? 0;
				$results['rotten'] = $visit->medium3_rotten ?? 0;
				$results['harvested'] = $visit->medium3_harvested ?? 0;
				break;
			case 5:
				$results['loss'] = $visit->adult_loss ?? 0;
				$results['witchsBroom'] = $visit->adult_wb ?? 0;
				$results['rat'] = $visit->adult_rat ?? 0;
				$results['rotten'] = $visit->adult_rotten ?? 0;
				$results['harvested'] = $visit->adult_harvested ?? 0;
				break;
			case 6:
				$results['loss'] = $visit->adult2_loss ?? 0;
				$results['witchsBroom'] = $visit->adult2_wb ?? 0;
				$results['rat'] = $visit->adult2_rat ?? 0;
				$results['rotten'] = $visit->adult2_rotten ?? 0;
				$results['harvested'] = $visit->adult2_harvested ?? 0;
				break;
			case 7:
				$results['loss'] = $visit->mature_loss ?? 0;
				$results['witchsBroom'] = $visit->mature_wb ?? 0;
				$results['rat'] = $visit->mature_rat ?? 0;
				$results['rotten'] = $visit->mature_rotten ?? 0;
				$results['harvested'] = $visit->mature_harvested ?? 0;
				break;
			case 8:
				$results['loss'] = $visit->mature2_loss ?? 0;
				$results['witchsBroom'] = $visit->mature2_wb ?? 0;
				$results['rat'] = $visit->mature2_rat ?? 0;
				$results['rotten'] = $visit->mature2_rotten ?? 0;
				$results['harvested'] = $visit->mature2_harvested ?? 0;
				break;
			case 9:
				$results['loss'] = $visit->mature3_loss ?? 0;
				$results['witchsBroom'] = $visit->mature3_wb ?? 0;
				$results['rat'] = $visit->mature3_rat ?? 0;
				$results['rotten'] = $visit->mature3_rotten ?? 0;
				$results['harvested'] = $visit->mature3_harvested ?? 0;
				break;
			case 10:
				$results['loss'] = $visit->mature4_loss ?? 0;
				$results['witchsBroom'] = $visit->mature4_wb ?? 0;
				$results['rat'] = $visit->mature4_rat ?? 0;
				$results['rotten'] = $visit->mature4_rotten ?? 0;
				$results['harvested'] = $visit->mature4_harvested ?? 0;
				break;
		}

		return $results;
	}

	/**
	 * Desloca os resultados dos bilros conforme lógica original
	 */
	private function shiftBobbinsResults($resultsSafra, $totalBobbins)
	{
		$originalArray = array_values($resultsSafra);
		$shiftedArray = [];

		// Adiciona a primeira posição com bobbins cheio e os demais zerados
		if (count($originalArray) > 0) {
			$shiftedArray[] = [
				'date' => $originalArray[0]['date'],
				'bobbins' => $totalBobbins,
				'piece' => 0,
				'loss' => 0,
				'witchsBroom' => 0,
				'rat' => 0,
				'rotten' => 0,
				'harvested' => 0
			];
		}

		// Adiciona os valores deslocados (desconsidera o último)
		for ($i = 0; $i < count($originalArray) - 1; $i++) {
			$next = $originalArray[$i];
			$shiftedArray[] = [
				'date' => $originalArray[$i + 1]['date'],
				'bobbins' => $next['bobbins'],
				'piece' => $next['piece'],
				'loss' => $next['loss'],
				'witchsBroom' => $next['witchsBroom'],
				'rat' => $next['rat'],
				'rotten' => $next['rotten'],
				'harvested' => $next['harvested']
			];
		}

		return $shiftedArray;
	}

	/**
	 * Prepara os dados do gráfico de bilros
	 */
	private function prepareBobbinsChartData($resultsSafraArray, $faixas, $totalBobbins)
	{
		// Preparar labels
		$labels = [];
		foreach ($resultsSafraArray as $index => $entry) {
			$formattedDate = Carbon::parse($entry['date'])->format('d/m/Y');
			$faixa = $faixas[$index] ?? "";
			$labels[] = [$formattedDate, $faixa];
		}

		// Preparar datasets
		$categories = [
			['key' => 'bobbins', 'label' => 'Frutos', 'color' => 'yellow'],
			['key' => 'harvested', 'label' => 'Colhido', 'color' => 'green'],
			['key' => 'piece', 'label' => 'Peco', 'color' => 'blue'],
			['key' => 'loss', 'label' => 'Perda', 'color' => 'gray'],
			['key' => 'witchsBroom', 'label' => 'Vassoura de Bruxa', 'color' => 'brown'],
			['key' => 'rat', 'label' => 'Rato', 'color' => 'red'],
			['key' => 'rotten', 'label' => 'Podre', 'color' => 'purple'],
		];

		$datasets = [];
		foreach ($categories as $cat) {
			$data = [];
			foreach ($resultsSafraArray as $entry) {
				$data[] = $entry[$cat['key']] ?? 0;
			}

			$datasets[] = [
				'label' => $cat['label'],
				'data' => $data,
				'backgroundColor' => $cat['color'],
				'stack' => 'a'
			];
		}

		// Calcular título do gráfico
		$totalHarvested = count($resultsSafraArray) > 0
			? $resultsSafraArray[count($resultsSafraArray) - 1]['harvested']
			: 0;

		$rendimento = $totalBobbins > 0 ? (($totalHarvested / $totalBobbins) * 100) : 0;
		$chartTitle = "Taxa de Aproveitamento dos Frutos Colhidos: " . number_format($rendimento, 2) . "%";

		return [
			'labels' => $labels,
			'datasets' => $datasets,
			'chartTitle' => $chartTitle,
			'totalBobbins' => $totalBobbins
		];
	}
}
