<?php

namespace App\Http\Controllers\API;

use App\Models\Property;
use App\Models\Geolocation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Http\Resources\PropertyResource;
use Illuminate\Support\Facades\Validator;

/**
 * @group Gerenciamento de Propriedades
 *
 * @authenticated
 *
 * Endpoints e funções para gerenciamento de propriedades
 *
 * @apiResourceCollection App\Http\Resources\PropertyResource
 * @apiResourceModel App\Models\Property
 */
class PropertyController extends BaseController
{
	protected $messages = [
		'name.required' => 'Nome da propriedade inválido',
		'name.unique' => 'Nome da propriedade já está registrado',
		'owner_name.required' => 'Nome do proprietário é obrigatório',
		'area_name.required' => 'Nome da área é obrigatório',
		'description.required' => 'Descrição inválida'
	];
	/**
	 * Create a new PropertyController instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		// $this->middleware('auth:api');
	}

	/**
	 * Exibe todas as propriedades
	 *
	 * @responseFile responses/properties/index.json
	 * @responseFile 404 responses/404.json
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function index()
	{
		$user_id = Auth::id();

		$properties = Property::where('owner_id', '=', $user_id)
			->orderBy('id', 'asc')
			->get();

		return $this->send_response(PropertyResource::collection($properties));
	}

	public function shared()
	{
		$properties = Property::where('status', '=', 2)->orderBy('id', 'asc')->get();

		return $this->send_response(PropertyResource::collection($properties));
	}

	/**
	 * Cria uma nova propriedade
	 *
	 * @bodyParam name string required Nome da propriedade. Example: Fazenda Primavera
	 * @bodyParam owner_name string required Nome do proprietário. Example: Lindolfo
	 * @bodyParam description string required Descrição qualquer sobre a propriedade (podendo ser localização, proximidade, etc). Example: Próximo a Fazendo Verão
	 *
	 * @responseFile responses/properties/store.json
	 * @responseFile 404 responses/404.json
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @return \Illuminate\Http\Response
	 */
	public function store(Request $request)
	{
		$check_label = $this->check_label(
			$request->get('name'),
		);

		if (!$check_label) {
			return $this->send_error(
				'Propriedade já existente',
				'Você já tem uma propriedade cadastrada com esse nome. Escolha um nome único!',
				400
			);
		}

		$validate = array(
			'name' => 'bail|required',
			'owner_name' => 'required',
			'area_name' => 'required',
			'status' => 'required',
			'city' => 'required',
			'area' => 'required',
			'uf' => 'required',
		);

		$validator = Validator::make($request->all(), $validate, $this->messages);

		if ($validator->fails()) {
			return $this->send_error(
				'Erro ao tentar cadastrar propriedade',
				$validator->errors()
			);
		}

		$rule = Property::whereHas(
			'geolocation',
			function ($geolocation) use ($request) {
				$geolocation->where('latitude', '=', $request->geolocation['latitude'])
				->where('longitude', '=', $request->geolocation['longitude']);
			}
		)->first();

		if ($rule) {
			return $this->send_error(
				'Já existe uma propriedade localizada nessas coordenadas',
			);
		}

		$property_geolocation = new Geolocation();
		$property_geolocation->fill($request->get('geolocation'));
		$property_geolocation->save();

		$property = new Property();
		$property->name = $request->get('name');
		$property->area = $request->get('area');
		$property->owner_name = $request->get('owner_name');
		$property->area_name = $request->get('area_name');
		$property->description = $request->get('description');
		$property->status = $request->get('status');
		$property->city = $request->get('city');
		$property->uf = $request->get('uf');
		$property->owners()->associate($request->get('owner_id'));
		$property->geolocation()->associate($property_geolocation);
		$property->save();

		return $this->send_response(
			new PropertyResource($property),
			'Propriedade registrada',
			201
		);
	}

	/**
	 * Exibe uma propriedade específica
	 *
	 * @queryParam property required Identificador único da propriedade. Example: 1
	 *
	 * @responseFile responses/properties/show.json
	 * @responseFile 404 responses/404.json
	 *
	 * @param  \App\Models\Property  $property
	 * @return \Illuminate\Http\Response
	 */
	public function show(Property $property)
	{
		if (!$property) {
			return $this->send_error('Propriedade não encontrada');
		}

		return $this->send_response(new PropertyResource($property));
	}


	/**
	 * Atualiza uma propriedade específica
	 *
	 * @queryParam property required Identificador único da propriedade. Example: 4
	 *
	 * @bodyParam name string required Nome da propriedade. Example: Fazenda Verão
	 * @bodyParam owner_name string required Nome do proprietário. Example: Lindolfo
	 * @bodyParam description string required Descrição qualquer sobre a propriedade (podendo ser localização, proximidade, etc). Example: Próximo a Fazendo Outono
	 *
	 * @responseFile responses/properties/update.json
	 * @responseFile 404 responses/404.json
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @param  \App\Models\Property  $property
	 * @return \Illuminate\Http\Response
	 */
	public function update(Request $request, Property $property)
	{
		if (!$property) {
			return $this->send_error('Propriedade não encontrada');
		}

		$property_geolocation = $request->only('geolocation');

		$filtered['latitude'] = $property_geolocation['geolocation']['latitude'];
		$filtered['longitude'] = $property_geolocation['geolocation']['longitude'];
		$filtered['ratio'] = $property_geolocation['geolocation']['ratio'];

		$property->geolocation()->update($filtered);

		$property->name = $request->get('name');
		$property->area = $request->get('area');
		$property->owner_name = $request->get('owner_name');
		$property->description = $request->get('description');
		$property->update();

		return $this->send_response(
			new PropertyResource($property),
			'Propriedade atualizada com sucesso'
		);
	}

	/**
	 * Desativa ou ativa uma propriedade específica
	 *
	 * @queryParam property required Identificador único da propriedade. Example: 4
	 *
	 * @responseFile responses/properties/deactivate.json
	 * @responseFile 404 responses/404.json
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @param  \App\Models\Property  $property
	 * @return \Illuminate\Http\Response
	 */
	public function status(Request $request, Property $property)
	{
		$message = '';
		$status = $request->get('status');

		switch ($status) {
			case 0:
				$message = 'Propriedade desativada';
				break;
			case 1:
				$message = 'Propriedade ativada';
				break;
			case 2:
				$message = 'Propriedade ativada e compartilhada';
				break;
			default:
				break;
		}

		$property->status = $status;
		$property->update();

		return $this->send_response(
			new PropertyResource($property),
			$message,
			200
		);
	}

	/**
	 * Remove uma propriedade e todas as entidades relacionadas
	 *
	 * @queryParam property required Identificador único da propriedade. Example: 1
	 *
	 * @responseFile responses/properties/destroy.json
	 * @responseFile 404 responses/404.json
	 *
	 * @param  \App\Models\Property  $property
	 * @return \Illuminate\Http\Response
	 */
	public function destroy(Property $property)
	{
		if (!$property) {
			return $this->send_error('Propriedade não encontrada');
		}

		// Verificar se o usuário é o proprietário ou administrador
		$user_id = Auth::id();

		// Administradores podem excluir qualquer propriedade
		$isAdmin = $this->is_admin();

		if (!$isAdmin && $property->owner_id !== $user_id) {
			return $this->send_error(
				'Acesso negado',
				'Você não tem permissão para excluir esta propriedade',
				403
			);
		}

		DB::beginTransaction();

		try {
			// Buscar todas as áreas homogêneas da propriedade
			$homogeneousAreas = $property->homogeneous_areas;

			foreach ($homogeneousAreas as $homogeneousArea) {
				// Buscar todos os estratos da área homogênea
				$strata = $homogeneousArea->strata;

				foreach ($strata as $stratum) {
					// Buscar todos os pontos de amostragem do estrato
					$samplingPoints = $stratum->sampling_points;

					foreach ($samplingPoints as $samplingPoint) {
						// Buscar todas as árvores do ponto de amostragem
						$trees = $samplingPoint->trees;

						foreach ($trees as $tree) {
							// Buscar todas as visitas da árvore
							$treeVisits = $tree->tree_visits;

							foreach ($treeVisits as $treeVisit) {
								// Deletar registros de frutos relacionados
								if ($treeVisit->bobbin_id) {
									DB::table('bobbins')->where('id', $treeVisit->bobbin_id)->delete();
								}
								if ($treeVisit->small_id) {
									DB::table('smalls')->where('id', $treeVisit->small_id)->delete();
								}
								if ($treeVisit->medium_id) {
									DB::table('mediums')->where('id', $treeVisit->medium_id)->delete();
								}
								if ($treeVisit->medium2_id) {
									DB::table('mediums2')->where('id', $treeVisit->medium2_id)->delete();
								}
								if ($treeVisit->medium3_id) {
									DB::table('mediums3')->where('id', $treeVisit->medium3_id)->delete();
								}
								if ($treeVisit->adult_id) {
									DB::table('adults')->where('id', $treeVisit->adult_id)->delete();
								}
								if ($treeVisit->adult2_id) {
									DB::table('adults2')->where('id', $treeVisit->adult2_id)->delete();
								}
								if ($treeVisit->mature_id) {
									DB::table('matures')->where('id', $treeVisit->mature_id)->delete();
								}
								if ($treeVisit->mature2_id) {
									DB::table('matures2')->where('id', $treeVisit->mature2_id)->delete();
								}
								if ($treeVisit->mature3_id) {
									DB::table('matures3')->where('id', $treeVisit->mature3_id)->delete();
								}
								if ($treeVisit->mature4_id) {
									DB::table('matures4')->where('id', $treeVisit->mature4_id)->delete();
								}

								// Deletar geolocalização da visita se existir
								if ($treeVisit->geolocation_id) {
									DB::table('geolocations')->where('id', $treeVisit->geolocation_id)->delete();
								}

								// Deletar a visita da árvore
								$treeVisit->delete();
							}

							// Deletar RFID relacionado à árvore se existir
							if ($tree->rfid_id) {
								DB::table('rfids')->where('id', $tree->rfid_id)->delete();
							}

							// Deletar a árvore
							$tree->delete();
						}

						// Deletar coletores do ponto de amostragem
						DB::table('sampling_points_collectors')
							->where('sampling_point_id', $samplingPoint->id)
							->delete();

						// Deletar geolocalização do ponto de amostragem
						if ($samplingPoint->geolocation_id) {
							DB::table('geolocations')->where('id', $samplingPoint->geolocation_id)->delete();
						}

						// Deletar o ponto de amostragem
						$samplingPoint->delete();
					}

					// Deletar o estrato
					$stratum->delete();
				}

				// Deletar informações de visita da área homogênea
				$homogeneousArea->visits()->delete();

				// Deletar a área homogênea
				$homogeneousArea->delete();
			}

			// Deletar blocos da propriedade
			$blocks = $property->blocks;
			foreach ($blocks as $block) {
				// Deletar geolocalização do bloco se existir
				if ($block->geolocation_id) {
					DB::table('geolocations')->where('id', $block->geolocation_id)->delete();
				}
				$block->delete();
			}

			// Deletar RFIDs relacionados à propriedade
			DB::table('rfids')->where('property_id', $property->id)->delete();

			// Deletar geolocalização da propriedade
			if ($property->geolocation_id) {
				DB::table('geolocations')->where('id', $property->geolocation_id)->delete();
			}

			// Finalmente, deletar a propriedade
			$property->delete();

			DB::commit();

			return $this->send_response(
				null,
				'Propriedade e todos os dados relacionados foram excluídos com sucesso',
				200
			);

		} catch (\Exception $exception) {
			DB::rollback();

			// Log do erro para debug
			Log::error('Erro ao excluir propriedade: ' . $exception->getMessage());

			return $this->send_error(
				'Erro ao excluir propriedade',
				'Ocorreu um erro interno. Tente novamente mais tarde.',
				500
			);
		}
	}

	protected function check_label($name)
	{
		$user_id = Auth::id();
		$name_lower = strtolower($name);

		$property = Property::where('owner_id', '=', $user_id)
			->where(DB::raw('LOWER(name)'), 'LIKE', $name_lower)
			->get()
			->toArray();

		// $rule = Property::where(DB::raw('LOWER(name)'), 'LIKE', $name_lower)
		// 	->get()
		// 	->toArray();

		if ($property) {
			return false;
		}

		return true;
	}

	/**
	 * Verifica se o usuário autenticado é administrador
	 *
	 * @return bool
	 */
	protected function is_admin()
	{
		try {
			$roles = auth('api')->user()
				->roles()
				->where('label', 'admin')
				->get()
				->toArray();

			return count($roles) > 0;
		} catch (\Exception $e) {
			return false;
		}
	}
}
