<?php

namespace App\Exports;

use App\Models\Stratum;
use App\Models\Property;
use App\Models\SamplingPoint;
use App\Models\HomogeneousArea;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Concerns\Exportable;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Http\Resources\SamplingPointResource;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class ExcelExport implements WithMultipleSheets, ShouldQueue
{
	use Exportable;

	private $property;

	public function __construct($property)
	{
		$this->property = $property;
	}

	public function sheets(): array
	{
		$user_id = Auth::id();
		$is_admin = $this->is_admin();
		$is_farmer = $this->is_farmer();
		$sheets = [];

		if ($is_admin || $is_farmer) {
			$property = Property::where('id', '=', $this->property)
				->where('owner_id', '=', $user_id)->first();

			$strata = [];
			$sampling_points = [];

			$homogeneous_areas =
				HomogeneousArea::where('property_id', '=', $property->id)
				->orderBy('id', 'asc')
				->get();

			foreach ($homogeneous_areas as $key => $homogeneous_area) {
				$strata[] =
					Stratum::where('homogeneous_area_id', '=', $homogeneous_area->id)
					->orderBy('id', 'asc')
					->get();
			}

			foreach ($strata as $key => $stratum) {
				foreach ($stratum as $key => $str) {
					$sampling_points[] = SamplingPoint::where('stratum_id', '=', $str->id)
						->orderBy('id', 'asc')
						->get();
				}
			}

			$expand_sampling_points = [];
			foreach ($sampling_points as $key => $sampling_point) {
				foreach ($sampling_point as $key => $value) {
					$expand_sampling_points[] = $value;
				}
			}

			$sampling_points =
				SamplingPointResource::collection($expand_sampling_points);

			foreach ($sampling_points as $sampling_point) {
				$sheets[] = new ExcelSheet($sampling_point);
			}
		}

		return $sheets;
	}

	private function is_admin()
	{
		$roles = auth('api')
			->user()
			->roles()
			->where('label', 'admin')
			->get()
			->toArray();

		$is_admin = count($roles) ? true : false;

		return $is_admin;
	}

	private function is_farmer()
	{
		$roles = auth('api')
			->user()
			->roles()
			->where('label', 'pre-registered')
			->get()
			->toArray();

		$is_farmer = count($roles) ? true : false;

		return $is_farmer;
	}
}
