<?php

namespace App\Exports;

use App\Models\VisitInformation;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithTitle;
use App\Http\Resources\VisitInformationResource;

class PracticeSheet implements FromView, WithTitle
{
    private $homogeneous_area;

    public function __construct($homogeneous_area)
    {
        $this->homogeneous_area = $homogeneous_area;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function view(): View
    {
        $visits_information = VisitInformation::where('homogeneous_area_id', '=', $this->homogeneous_area->id)->get();

        $data = VisitInformationResource::collection($visits_information);

        return view('templates.xls', [
            'data' => $data,
            'homogeneous_area' => $this->homogeneous_area,
        ]);
    }

    public function title(): string
    {
        $title = 'Área Homogênea ' . $this->homogeneous_area->label;

        return $title;
    }
}
