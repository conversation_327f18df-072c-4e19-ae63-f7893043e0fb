<?php

namespace App\Exports;

use App\Models\Property;
use App\Models\HomogeneousArea;
use Illuminate\Support\Facades\Auth;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Http\Resources\HomogeneousAreaResource;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class PracticeExport implements WithMultipleSheets, ShouldQueue
{
    private $property;

    public function __construct($property)
    {
        $this->property = $property;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function sheets(): array
    {
        $user_id = Auth::id();
        $sheets = [];

        $property = Property::where('id', '=', $this->property)
            ->where('owner_id', '=', $user_id)->first();

        $homogeneous_areas =
            HomogeneousArea::where('property_id', '=', $property->id)
            ->orderBy('id', 'asc')
            ->get();

        $expand_homogeneous_areas = [];
        foreach ($homogeneous_areas as $key => $homogeneous_area) {
            $expand_homogeneous_areas[] = $homogeneous_area;
        }

        $homogeneous_areas = HomogeneousAreaResource::collection($expand_homogeneous_areas);

        foreach ($homogeneous_areas as $homogeneous_area) {
            $sheets[] = new PracticeSheet($homogeneous_area);
        }

        return $sheets;
    }
}
