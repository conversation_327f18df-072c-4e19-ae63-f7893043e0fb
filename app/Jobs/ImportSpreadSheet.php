<?php

namespace App\Jobs;

use App\Imports;
use App\Imports\DataImport;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ImportSpreadSheet implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

	// public $tries = 100;
	public $file_name = '';
	public $user_id;

	/**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($file_name, $user_id)
    {
		$this->file_name = $file_name;
		$this->user_id = $user_id;
	}

    /**
     * Determine the time at which the job should timeout.
     *
     * @return \DateTime
     */
    // public function retryUntil()
    // {
    //     return now()->addMinutes(10);
    // }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $filePath = storage_path("app/{$this->file_name}");

        if (file_exists($filePath)) {
            Excel::import(new DataImport($this->user_id), $filePath);
            // return response()->json(['message' => 'Imported data.']);
        } else {
            throw new \Exception("File not found: {$filePath}");
        }
    }
}
