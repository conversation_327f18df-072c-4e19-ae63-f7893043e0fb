<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use App\Models\Property;
use Faker\Generator as Faker;

$factory->define(Property::class, function (Faker $faker) {
	$cities = $faker->randomElement([
		'Itab<PERSON>',
		'Itajuí<PERSON>',
		'<PERSON><PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON>',
		'Barro Preto',
		'<PERSON><PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON><PERSON>',
		'São José da Vitória',
		'Coaraci',
		'Ibicaraí',
		'Almadina',
		'Flores<PERSON> Azul',
		'Jussari',
		'Aurelino Leal',
	]);

	return [
		'name' => $faker->company,
		'owner_name' => $faker->firstName,
		'description' => $faker->realText($maxNbChars = 50, $indexSize = 2),
		'city' => $cities,
		'uf' => 'BA'
	];
});
