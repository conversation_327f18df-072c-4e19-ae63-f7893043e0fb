<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class RfidSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement("
            INSERT INTO rfids (code)
            SELECT LPAD(FLOOR(RAND() * 10000000000), 10, '0') AS random_rfid
            FROM trees
        ");

        DB::statement("
            UPDATE trees
            JOIN (
                SELECT rfids.id, rfids.code
                FROM rfids
                ORDER BY rfids.id
            ) AS rfid_data
            ON trees.id = rfid_data.id
            SET trees.rfid_id = rfid_data.id
        ");
    }
}
