<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class UpdateRfidsAndTreesTables extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('rfids', function (Blueprint $table) {
			$table->id();
			$table->string('code', 10)->unique();
			$table->timestamp('created_at')->default(DB::raw('CURRENT_TIMESTAMP'));
			$table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP'));
		});

		Schema::table('trees', function (Blueprint $table) {
			$table->dropColumn('rfid');
		});

		Schema::table('trees', function (Blueprint $table) {
			$table->unsignedBigInteger('rfid_id')->nullable()->unique();
			$table->foreign('rfid_id')->references('id')->on('rfids')->onDelete('set null');
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::table('trees', function (Blueprint $table) {
			$table->dropForeign(['rfid_id']);
			$table->dropColumn('rfid_id');
		});
		Schema::table('trees', function (Blueprint $table) {
			$table->string('rfid', 255)->nullable()->collation('utf8mb4_general_ci');
		});
		Schema::table('rfids', function (Blueprint $table) {
			$table->string('code', 255)->change();
		});

		Schema::dropIfExists('rfids');
	}
}
