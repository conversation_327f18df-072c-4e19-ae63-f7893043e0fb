<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTreesTable extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('trees', function (Blueprint $table) {
			$table->id();
			$table->string('label');
			$table->string('rfid')->nullable();
			$table->string('alternative_label')->nullable();
			$table->boolean('status')->status(true);
			$table->unsignedBigInteger('sampling_point_id');
			$table->timestamps();
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('trees');
	}
}
