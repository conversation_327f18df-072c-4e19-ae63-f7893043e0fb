<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPropertyIdToRfidsTable extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::table('rfids', function (Blueprint $table) {
			$table->unsignedBigInteger('property_id')
				->nullable();
			$table->foreign('property_id')
				->references('id')
				->on('sampling_points')
				->onDelete('set null');
			$table->unique(['property_id', 'code']);
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::table('rfids', function (Blueprint $table) {
			$table->dropUnique(['property_id', 'code']);
			$table->dropForeign(['property_id']);
			$table->dropColumn('property_id');
		});
	}
};
