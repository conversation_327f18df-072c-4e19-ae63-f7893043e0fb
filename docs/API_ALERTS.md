# API de Alertas - Central de Alertas

## Visão Geral

Esta documentação descreve as APIs desenvolvidas para a Central de Alertas do sistema de gestão de propriedades de cacau.

## Endpoints Disponíveis

### 1. Dados de Poda da Propriedade

**GET** `/api/v1/properties/{id}/last-pruning`

Retorna informações sobre a última poda realizada na propriedade.

#### Parâmetros
- `id` (integer, required): ID da propriedade

#### Resposta de Sucesso (200)
```json
{
  "success": true,
  "last_pruning_date": "2024-01-15",
  "months_ago": 5,
  "status": "maintenance",
  "message": "Poda de Manutenção",
  "needs_maintenance": true
}
```

#### Status Possíveis
- `ok`: Poda em dia (≤6 meses)
- `maintenance`: Precisa de manutenção (>6 meses)
- `never`: Nunca foi podada

---

### 2. Dados de Vassoura de Bruxa

**GET** `/api/v1/properties/{id}/witchs-broom-data`

Retorna dados de infecção por vassoura de bruxa dos últimos 12 meses.

#### Parâmetros
- `id` (integer, required): ID da propriedade

#### Resposta de Sucesso (200)
```json
{
  "success": true,
  "total_fruits": 1250,
  "witchs_broom_fruits": 187,
  "percentage": 14.96,
  "status": "high",
  "severity": "high",
  "visits_analyzed": 45
}
```

#### Níveis de Severidade
- `low`: 0-1% (Verde)
- `medium`: 2-10% (Amarelo)
- `high`: 11-20% (Vermelho)
- `critical`: >20% (Preto)

---

### 3. Estatísticas Gerais de Alertas

**GET** `/api/v1/alerts/statistics`

Retorna estatísticas consolidadas de todos os alertas do usuário.

#### Resposta de Sucesso (200)
```json
{
  "success": true,
  "statistics": {
    "poda": {
      "maintenance": 3,
      "ok": 7,
      "never": 2,
      "total": 12
    },
    "vassoura": {
      "critical": 1,
      "high": 2,
      "medium": 4,
      "low": 5,
      "total": 12
    }
  }
}
```

---

### 4. Alertas Críticos

**GET** `/api/v1/alerts/critical`

Retorna propriedades que requerem atenção imediata.

#### Resposta de Sucesso (200)
```json
{
  "success": true,
  "critical_alerts": [
    {
      "property": {
        "id": 1,
        "name": "Fazenda São João",
        "city": "Ilhéus",
        "uf": "BA"
      },
      "alerts": [
        {
          "type": "pruning",
          "severity": "high",
          "message": "Poda de Manutenção"
        },
        {
          "type": "witchs_broom",
          "severity": "critical",
          "message": "Vassoura de bruxa crítica (25.3%)"
        }
      ]
    }
  ],
  "count": 1
}
```

## Códigos de Erro

### 400 - Bad Request
```json
{
  "success": false,
  "message": "Parâmetros inválidos"
}
```

### 403 - Forbidden
```json
{
  "success": false,
  "message": "Acesso negado"
}
```

### 404 - Not Found
```json
{
  "success": false,
  "message": "Propriedade não encontrada"
}
```

### 500 - Internal Server Error
```json
{
  "success": false,
  "message": "Erro interno do servidor"
}
```

## Autenticação

Todas as rotas requerem autenticação via Bearer Token:

```
Authorization: Bearer {token}
```

## Permissões

- **Proprietário**: Acesso apenas às suas propriedades
- **Admin**: Acesso a todas as propriedades
- **Properties Manager**: Acesso a todas as propriedades

## Cache

O sistema utiliza cache Redis com duração de 30 minutos para otimizar performance:

- Cache de alertas de poda: `pruning_alert_{property_id}`
- Cache de alertas de vassoura: `witchs_broom_alert_{property_id}`
- Cache de estatísticas: `user_alerts_stats_{user_id}`

## Estrutura de Dados

### Campos de Visita de Árvore Analisados

Para cálculo da vassoura de bruxa, são analisados os seguintes estágios:

- `bobbin`: Frutos em estágio inicial
- `small`: Frutos pequenos
- `medium`, `medium2`, `medium3`: Frutos médios
- `adult`, `adult2`: Frutos adultos
- `mature`, `mature2`, `mature3`, `mature4`: Frutos maduros

Cada estágio contém:
```json
{
  "total": 50,
  "witchs_broom": 8
}
```

## Exemplos de Uso

### JavaScript (Frontend)
```javascript
// Buscar dados de poda
const response = await axios.get(`/api/v1/properties/${propertyId}/last-pruning`, {
  headers: { authorization: `bearer ${token}` }
});

// Buscar dados de vassoura de bruxa
const witchsBroomData = await axios.get(`/api/v1/properties/${propertyId}/witchs-broom-data`, {
  headers: { authorization: `bearer ${token}` }
});

// Buscar estatísticas
const stats = await axios.get('/api/v1/alerts/statistics', {
  headers: { authorization: `bearer ${token}` }
});
```

### cURL
```bash
# Buscar dados de poda
curl -X GET "https://api.example.com/api/v1/properties/1/last-pruning" \
  -H "Authorization: Bearer {token}"

# Buscar estatísticas
curl -X GET "https://api.example.com/api/v1/alerts/statistics" \
  -H "Authorization: Bearer {token}"
```

## Notas Importantes

1. **Performance**: Use cache sempre que possível
2. **Segurança**: Sempre validar permissões de acesso
3. **Dados**: Vassoura de bruxa considera apenas últimos 12 meses
4. **Poda**: Manutenção necessária após 6 meses
5. **Cores da Barra**: Verde (0-1%), Amarelo (2-10%), Vermelho (11-20%), Preto (>20%)
