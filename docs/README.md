# 📚 Documentação da PlataformaCacau

Este diretório contém a documentação técnica das principais telas e funcionalidades da PlataformaCacau.

## 📋 Índice de Documentações

### 🏠 Gestão de Propriedades
- **[AddProperties2.md](./AddProperties2.md)** - Cadastro de Blocos/Roças
  - Sistema de abas para múltiplos blocos
  - Validação de genótipos de cacau
  - Integração com clusterização automática

### 👥 Gestão de Usuários
- **[User.md](./User.md)** - Gestão de Usuários com Multiselect de Papéis
  - Sistema de papéis e permissões
  - Multiselects hierárquicos condicionais
  - Associação de usuários a propriedades/áreas

## 🎯 Padrões de Documentação

Cada documentação segue a estrutura:

### 📍 Localização
Caminho dos arquivos no projeto

### 🎯 Propósito
Objetivo e funcionalidade principal

### 🔗 Rota
URLs e parâmetros de acesso

### 📊 Estrutura de Dados
Modelos de dados e interfaces

### 🎨 Interface
Componentes visuais e layout

### ✅ Validações
Regras de negócio e validações

### 🔄 Fluxo de Funcionamento
Sequência de operações

### 📡 API Calls
Endpoints e estruturas de requisição

### 🎨 Design
Cores, componentes e responsividade

### 🔧 Métodos Principais
Funções JavaScript importantes

### 🎯 Casos de Uso
Exemplos práticos de utilização

### 🚨 Tratamento de Erros
Validações e feedback de erro

### 📱 Responsividade
Adaptações para diferentes telas

### 🔄 Integração
Conexões com outras telas/APIs

## 🛠️ Como Contribuir

### Adicionando Nova Documentação

1. **Criar arquivo** `.md` na pasta `docs/`
2. **Seguir estrutura** padrão acima
3. **Incluir exemplos** de código quando relevante
4. **Atualizar** este README.md com link

### Atualizando Documentação Existente

1. **Identificar** seção a ser atualizada
2. **Manter** estrutura e formatação
3. **Incluir** data da última atualização
4. **Testar** links e exemplos de código

## 📝 Convenções

### Formatação
- **Títulos**: Usar emojis para categorização
- **Código**: Blocos com syntax highlighting
- **Links**: Relativos para arquivos internos
- **Exemplos**: Incluir dados realistas

### Estrutura de Código
```javascript
// Sempre incluir comentários explicativos
function exemploFuncao() {
    // Explicar lógica complexa
    return resultado;
}
```

### Estrutura de API
```javascript
// Método HTTP + Endpoint
POST /api/v1/endpoint
{
    // Estrutura do payload
    campo: "valor",
    // Comentários sobre campos obrigatórios
}
```

## 🔍 Índice por Funcionalidade

### 🌱 Agricultura
- Cadastro de propriedades e blocos
- Genótipos de cacau
- Áreas homogêneas

### 👤 Usuários e Permissões
- Gestão de usuários
- Sistema de papéis
- Multiselects condicionais

### 📊 Interface
- Componentes Vue.js
- Responsividade
- Design patterns

### 🔌 Integrações
- APIs REST
- Validações
- Tratamento de erros

## 📅 Histórico de Atualizações

### 2024-12-27
- ✅ Criada documentação inicial
- ✅ AddProperties2.md - Cadastro de blocos
- ✅ User.md - Gestão de usuários
- ✅ README.md - Índice geral

## 🎯 Próximas Documentações

### Planejadas
- [ ] AlertCenter.md - Central de alertas
- [ ] HomogeneousArea.md - Áreas homogêneas
- [ ] Pruning.md - Alertas de poda
- [ ] WitchBroom.md - Alertas de vassoura-de-bruxa
- [ ] Properties.md - Listagem de propriedades
- [ ] Dashboard.md - Painel principal

### Sugeridas
- [ ] API.md - Documentação completa da API
- [ ] Components.md - Componentes reutilizáveis
- [ ] Deployment.md - Processo de deploy
- [ ] Testing.md - Estratégias de teste

## 📞 Contato

Para dúvidas sobre a documentação ou sugestões de melhorias:

- **Equipe de Desenvolvimento**: PlataformaCacau
- **Instituição**: UESC (Universidade Estadual de Santa Cruz)
- **Projeto**: Sistema de gestão estratégica para cacauicultores

## 📄 Licença

Esta documentação faz parte do projeto PlataformaCacau e segue as mesmas diretrizes de licenciamento do projeto principal.

---

**Última atualização**: 27 de dezembro de 2024  
**Versão da documentação**: 1.0.0
