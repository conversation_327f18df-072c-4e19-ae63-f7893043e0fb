# AddProperties2.vue - Cadastro de Blocos/Roças

## 📍 Localização
`resources/js/pages/Properties/AddProperties2.vue`

## 🎯 Propósito
Tela para cadastro detalhado de blocos/roças de uma propriedade, incluindo informações de área e genótipos de cacau.

## 🔗 Rota
- **Frontend**: `/panel/add-property2`
- **Parâmetros**: 
  - `propid` (ID da propriedade)
  - `area_name` (nome da divisão: Roça/Quadra/Bloco/etc.)

## 📊 Estrutura de Dados

### Props/Query Parameters
```javascript
{
    propid: "ID da propriedade",
    area_name: "Nome da divisão (Roça/Quadra/Bloco/etc.)"
}
```

### Data Structure
```javascript
{
    numBlocks: 0,           // Número de blocos a cadastrar (1-10)
    formData: [],           // Array com dados de cada bloco
    activeBlockIndex: 0,    // Índice do bloco ativo nas abas
    isSubmitting: false,    // Estado de envio
    propertyId: null,       // ID da propriedade
    areaName: "Bloco"       // Nome da divisão
}
```

### Estrutura de Cada Bloco
```javascript
{
    label: "Roça 1",
    area: null,             // Área em hectares
    
    // Genótipos (preenchidos pelo usuário)
    genotype: {
        common: {
            participation: 0,    // % de participação (0-100)
            type: "1",          // 1=Forasteiro, 2=Crioulo, 3=Trinitário
            age: 0,             // Idade em anos
            density: 0          // Plantas por hectare
        },
        hybrid: {
            participation: 0,
            age: 0,
            density: 0
        },
        cloned: {
            participation: 0,
            type: "1",          // 1=CCN-10, 2=CCN-51, etc.
            age: 0,
            density: 0
        }
    },
    
    // Campos com valores padrão (não editáveis)
    relief: "1",
    altitude: "1",
    production: { temple: 0, main: 0, total: 0, not_informed: 1 },
    handling: { temple: "1", main: "1" },
    soilUse: { type: "1", description: "" },
    rainfall: { /* todos os meses: "0", unknown: "1" */ },
    geolocation: { latitude: null, longitude: null },
    soilClass: { /* todas as classes com other: 100 */ }
}
```

## 🎨 Interface

### 1. Seção de Quantidade
- Input numérico para definir número de blocos (1-10)
- Gera automaticamente os formulários

### 2. Sistema de Abas
- **Abas horizontais** para navegar entre blocos
- **Ícone de muda** (fa-seedling) em cada aba
- **Aba ativa** destacada com cor verde
- **Responsivo** - vira vertical em mobile

### 3. Formulário de Cada Bloco

#### Informações Básicas
- **Área em hectares** (obrigatório, decimal)

#### Tabela de Genótipos
| Genótipo | Participação (%) | Tipo | Idade (anos) | Densidade (plantas/ha) |
|----------|------------------|------|--------------|------------------------|
| Comum    | 0-100           | Forasteiro/Crioulo/Trinitário | Numérico | Numérico |
| Híbrido  | 0-100           | N/A | Numérico | Numérico |
| Clonado  | 0-100           | CCN-10/CCN-51/CEPEC 2204/etc. | Numérico | Numérico |

## ✅ Validações

### 1. Participação de Genótipos
```javascript
checkGenotypeParticipation(index) {
    const total = common + hybrid + cloned;
    if (total !== 100) {
        // Exibe erro: "A soma das participações deve ser 100%"
    }
}
```

### 2. Campos Obrigatórios
- **Área** deve ser > 0
- **Soma dos genótipos** deve ser exatamente 100%

### 3. Campos Condicionais
- Campos de genótipo são **desabilitados** se participação = 0
- Campos são **resetados** quando participação volta a 0

## 🔄 Fluxo de Funcionamento

1. **Usuário define** número de blocos
2. **Sistema gera** abas e formulários
3. **Usuário navega** pelas abas preenchendo dados
4. **Validação em tempo real** da soma de genótipos
5. **Envio** cria blocos no backend
6. **Clusterização** automática para gerar áreas homogêneas
7. **Redirecionamento** para tela de áreas homogêneas

## 📡 API Calls

### Criação de Blocos
```javascript
// Para cada bloco
POST /api/v1/blocks
{
    label: "Roça 1",
    area: 2.5,
    propertyId: 123,
    genotypes: { /* dados dos genótipos */ },
    // ... outros campos com valores padrão
}
```

### Clusterização
```javascript
GET /api/v1/blocks-clustering?propid=123
```

## 🎨 Design

### Cores
- **Verde principal**: `#3d8160`, `#135D2A`
- **Fundo**: `#f5f8fd`
- **Cards**: Branco com sombra sutil

### Componentes
- **Abas modernas** com hover effects
- **Tabela responsiva** para genótipos
- **Badges** para identificar blocos ativos
- **Botões com gradiente** verde

### Responsividade
- **Desktop**: Abas horizontais
- **Mobile**: Abas verticais, formulário adaptado

## 🔧 Métodos Principais

### setActiveBlock(index)
Define qual bloco está ativo nas abas.

### checkGenotypeParticipation(index)
Valida se a soma dos genótipos é 100%.

### validateForm()
Valida todo o formulário antes do envio.

### onSubmit()
Processa o envio dos dados para o backend.

### cancelar()
Cancela o cadastro com confirmação do usuário.

## 🎯 Casos de Uso

### Cadastro Típico
1. Usuário vem da tela de propriedades
2. Define quantas roças quer cadastrar (ex: 3)
3. Preenche área de cada roça
4. Distribui genótipos (ex: 60% Comum, 40% Híbrido)
5. Sistema valida e cria blocos
6. Clusterização gera áreas homogêneas automaticamente

### Validação de Genótipos
- Se usuário coloca 60% + 30% = 90%, sistema mostra erro
- Campos ficam desabilitados se participação = 0
- Erro desaparece quando soma = 100%

## 🚨 Tratamento de Erros

### Validação Frontend
- Mensagens de erro em tempo real
- Campos obrigatórios destacados
- Botão de envio desabilitado se inválido

### Erros de API
- SweetAlert para feedback visual
- Console.log para debug
- Rollback em caso de falha

## 📱 Responsividade

### Desktop (>768px)
- Abas horizontais
- Tabela completa
- Layout em duas colunas

### Mobile (<768px)
- Abas verticais
- Tabela responsiva
- Botões em coluna única

## 🔄 Integração

### Tela Anterior
- **AddProperties.vue** - Cadastro da propriedade

### Tela Posterior
- **HomogeneousArea.vue** - Visualização das áreas geradas

### APIs Relacionadas
- `/api/v1/blocks` - CRUD de blocos
- `/api/v1/blocks-clustering` - Geração de áreas homogêneas
