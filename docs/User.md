# User.vue - Gestão de Usuários com Multiselect de Papéis

## 📍 Localização
- **Vue**: `resources/js/pages/User/User.vue`
- **JavaScript**: `resources/js/pages/User/User.js`

## 🎯 Propósito
Tela administrativa para gestão de usuários, com foco especial no sistema de papéis (roles) e permissões usando multiselect.

## 🔗 Rota
- **Frontend**: `/panel/users`
- **Permissão**: Apenas administradores

## 👥 Sistema de Papéis

### Estrutura de Papéis
```javascript
roles: [
    {
        id: 1,
        title: "Administrador",
        label: "admin"
    },
    {
        id: 2,
        title: "<PERSON><PERSON><PERSON> de Propriedades", 
        label: "properties-manager"
    },
    {
        id: 3,
        title: "<PERSON><PERSON>",
        label: "collector"
    },
    {
        id: 4,
        title: "Pré-cadastrado",
        label: "pre-registered"
    }
]
```

### Estrutura do Usuário
```javascript
user: {
    id: "",
    name: "",
    cpf: "",
    phone: "",
    email: "",
    password: "",
    status: true,
    roles: [],                  // Array de papéis do usuário
    properties: [],             // Propriedades associadas
    homogeneous_areas: [],      // Áreas homogêneas associadas
    strata: [],                // Unidades operacionais associadas
    sampling_points: []         // Pontos amostrais associados
}
```

## 🎛️ Interface de Papéis

### 1. Modal de Edição de Papéis
```html
<div id="modalUsersRoles">
    <h5>ATUALIZAR PAPÉIS DO USUÁRIO: {{user.name.toUpperCase()}}</h5>
    
    <!-- Seleção de Papéis via Checkboxes -->
    <div class="col-md-6">
        <label for="roles">Papéis</label>
        <div v-for="role in roles" :key="role.id">
            <div class="form-check">
                <input
                    type="checkbox"
                    :value="role"
                    v-model="currentRoles"
                    :checked="user.roles"
                />
                <span>{{ role.title }}</span>
            </div>
        </div>
    </div>
</div>
```

### 2. Multiselects Condicionais

#### Propriedades
```html
<multiselect
    v-model="user.properties"
    :options="properties"
    :placeholder="userIsCollector ? 'Selecione as propriedades' : 'Usuário não autorizado'"
    :disabled="!userIsCollector"
    track-by="id"
    label="name"
    :multiple="true"
    :close-on-select="false"
    class="w-100 min-width-multiselect"
/>
```

#### Áreas Homogêneas
```html
<multiselect
    v-model="user.homogeneous_areas"
    :options="filteredHomogeneousAreas"
    :placeholder="!userIsCollector ? 'Usuário não autorizado' : 
                  (user.properties.length === 0) ? 'Selecione uma propriedade primeiro' :
                  'Selecione as Áreas Homogêneas'"
    :disabled="!userIsCollector || user.properties.length === 0"
    track-by="id"
    label="label"
    :multiple="true"
/>
```

#### Unidades Operacionais
```html
<multiselect
    v-model="user.strata"
    :options="filteredStrata"
    :placeholder="!userIsCollector ? 'Usuário não autorizado' : 
                  (user.properties.length === 0) ? 'Selecione uma propriedade primeiro' :
                  (user.homogeneous_areas.length === 0) ? 'Selecione uma área homogênea primeiro' :
                  'Selecione as Unidades Operacionais'"
    :disabled="!userIsCollector || user.properties.length === 0 || user.homogeneous_areas.length === 0"
    track-by="id"
    :multiple="true"
/>
```

## 🔄 Lógica de Papéis

### 1. Detecção de Coletor
```javascript
isCollector(userRoles) {
    this.userIsCollector = userRoles.some(role => role.label === 'collector');
}
```

### 2. Watch de Papéis
```javascript
watch: {
    currentRoles(userRoles) {
        this.user.roles = userRoles;
        this.isCollector(userRoles);  // Habilita/desabilita multiselects
    }
}
```

### 3. Preenchimento de Papéis
```javascript
fill_roles(user) {
    this.currentRoles = _.map(user.roles, function(roles) {
        return roles;
    });
}
```

### 4. Conversão para IDs
```javascript
getRoleId(currentRoles) {
    this.rolesId = [];
    currentRoles.forEach(role => {
        this.rolesId.push(role.id);
    });
}
```

## 🎯 Funcionalidades dos Multiselects

### 1. Configuração Padrão
```javascript
// Propriedades comuns a todos os multiselects
{
    selectLabel: "Pressione para selecionar",
    deselectLabel: "Pressione para remover", 
    selectedLabel: "Selecionado",
    multiple: true,
    closeOnSelect: false,
    clearOnSelect: false,
    preserveSearch: true,
    preselectFirst: true
}
```

### 2. Templates Customizados
```html
<template slot="option" slot-scope="props">
    <div class="option__desc">
        <span class="option__title">
            {{ props.option.name }}
        </span>
    </div>
</template>
```

### 3. Filtragem Hierárquica
```javascript
computed: {
    filteredHomogeneousAreas() {
        // Filtra áreas baseadas nas propriedades selecionadas
        return this.homogeneous_areas.filter(area => 
            this.user.properties.some(prop => prop.id === area.property_id)
        );
    },
    
    filteredStrata() {
        // Filtra unidades baseadas nas áreas selecionadas
        return this.strata.filter(stratum =>
            this.user.homogeneous_areas.some(area => area.id === stratum.homogeneous_area_id)
        );
    }
}
```

## 🎨 Estilização dos Multiselects

### CSS Customizado
```scss
::v-deep .multiselect__tags {
    display: block;
    width: 100%;
}

::v-deep .multiselect__tag {
    display: block;
    width: 100%;
    white-space: normal;
    margin-bottom: 4px;
    text-align: left;
}

::v-deep .multiselect__tag:last-child {
    margin-bottom: 12px;
}

.min-width-multiselect {
    min-width: 200px;
}
```

## 📡 API Endpoints

### Buscar Dados
```javascript
// Papéis
GET /api/v1/roles

// Propriedades  
GET /api/v1/properties

// Áreas Homogêneas
GET /api/v1/homogeneous-areas

// Unidades Operacionais
GET /api/v1/strata
```

### Atualizar Usuário
```javascript
PUT /api/v1/users/{id}
{
    roles: [1, 3],                    // IDs dos papéis
    properties: [1, 2],               // IDs das propriedades
    homogeneous_areas: [1, 2, 3],     // IDs das áreas
    strata: [1, 2],                   // IDs das unidades
    sampling_points_id: [1, 2, 3, 4]  // IDs dos pontos amostrais
}
```

## 🔐 Regras de Negócio

### 1. Hierarquia de Permissões
- **Admin**: Acesso total
- **Properties Manager**: Gerencia propriedades
- **Collector**: Coleta dados em locais específicos
- **Pre-registered**: Acesso limitado

### 2. Dependências dos Multiselects
1. **Papéis** → Habilita/desabilita outros campos
2. **Propriedades** → Filtra áreas homogêneas
3. **Áreas Homogêneas** → Filtra unidades operacionais
4. **Unidades Operacionais** → Define pontos amostrais

### 3. Validações
- Apenas **coletores** podem ter propriedades/áreas associadas
- **Propriedades** devem ser selecionadas antes das áreas
- **Áreas** devem ser selecionadas antes das unidades

## 🎯 Casos de Uso

### 1. Criar Coletor
1. Selecionar papel "Coletor"
2. Multiselects são habilitados
3. Selecionar propriedades específicas
4. Selecionar áreas homogêneas dentro das propriedades
5. Selecionar unidades operacionais dentro das áreas

### 2. Editar Papéis
1. Clicar no botão "Editar Papéis" (fa-user-tag)
2. Modal abre com checkboxes dos papéis
3. Multiselects são atualizados conforme papéis
4. Salvar alterações

### 3. Remover Permissões
1. Desmarcar papel "Coletor"
2. Multiselects são automaticamente desabilitados
3. Dados de propriedades/áreas são mantidos mas inacessíveis

## 🔧 Métodos Principais

### editModalRoles(user)
Abre modal para edição de papéis de um usuário específico.

### fill_roles(user)
Preenche os papéis atuais do usuário no formulário.

### isCollector(userRoles)
Verifica se o usuário tem papel de coletor.

### getRoleId(currentRoles)
Converte array de objetos de papéis para array de IDs.

### onOptionSelect(event, type, options)
Manipula seleção nos multiselects com filtragem hierárquica.

### allRoles()
Busca todos os papéis disponíveis na API.

### update()
Atualiza dados do usuário incluindo papéis e associações.

## 🎨 Interface da Tabela

### Colunas Principais
- **Nome**: Nome completo do usuário
- **CPF**: Formatado com máscara (###.###.###-##)
- **Email**: Email de acesso
- **Telefone**: Contato do usuário
- **Papéis**: Lista dos papéis atribuídos
- **Status**: Ativo/Inativo
- **Ações**: Botões de editar e gerenciar papéis

### Botões de Ação
```html
<!-- Editar Usuário -->
<button title="Editar" class="btn btn-secondary" @click="editModal(props.row)">
    <i class="fas fa-cog fa-lg"></i>
</button>

<!-- Editar Papéis -->
<button title="Editar Papeis" class="btn btn-secondary" @click="editModalRoles(props.row)">
    <i class="fas fa-user-tag fa-lg"></i>
</button>
```

## 🚨 Tratamento de Erros

### Validação de CPF
```javascript
computed: {
    cpf() {
        return this.user.cpf;
    }
},
watch: {
    cpf(cpf) {
        if (!validate(cpf) && cpf.length === 14) {
            // Exibe erro de CPF inválido
        }
    }
}
```

### Validação de Email
- Verificação de formato válido
- Confirmação de email obrigatória

### Validação de Senha
- Confirmação de senha obrigatória
- Critérios de segurança

## 📱 Responsividade

### Desktop
- Modal com largura de 70%
- Multiselects em linha
- Tabela completa

### Mobile
- Modal adaptado
- Multiselects empilhados
- Tabela responsiva

## 🔄 Fluxo de Dados

### Carregamento Inicial
1. `created()` → Busca dados iniciais
2. `allRoles()` → Carrega papéis disponíveis
3. `allProperties()` → Carrega propriedades
4. `allHomogeneousAreas()` → Carrega áreas
5. `allStrata()` → Carrega unidades operacionais

### Edição de Papéis
1. Usuário clica em "Editar Papéis"
2. `editModalRoles()` → Abre modal
3. `fill_roles()` → Preenche papéis atuais
4. Usuário modifica checkboxes
5. `watch: currentRoles` → Atualiza interface
6. `update()` → Salva alterações

### Filtragem Hierárquica
1. Usuário seleciona propriedades
2. `filteredHomogeneousAreas` → Filtra áreas
3. Usuário seleciona áreas
4. `filteredStrata` → Filtra unidades
5. Sistema atualiza pontos amostrais automaticamente

## 🎯 Componentes Utilizados

### Vue Multiselect
- **Biblioteca**: `vue-multiselect`
- **Versão**: Compatível com Vue 2
- **Funcionalidades**: Múltipla seleção, busca, templates customizados

### Vue Good Table
- **Tabela**: Listagem de usuários
- **Paginação**: Configurável
- **Ordenação**: Por colunas
- **Busca**: Global

### SweetAlert2
- **Confirmações**: Exclusão de usuários
- **Feedback**: Sucesso/erro nas operações
- **Loading**: Estados de carregamento

## 🔐 Segurança

### Autenticação
- Token Bearer obrigatório
- Verificação de papel de admin

### Autorização
- Apenas admins podem gerenciar usuários
- Coletores só veem dados permitidos
- Validação no frontend e backend

### Validação de Dados
- CPF válido obrigatório
- Email único no sistema
- Senhas com critérios de segurança

## 📊 Métricas e Logs

### Console Logs
- Erros de API logados
- Estados de loading monitorados
- Validações registradas

### Feedback Visual
- Loading spinners
- Mensagens de sucesso/erro
- Estados desabilitados

## 🔄 Integração com Backend

### Endpoints Utilizados
- `GET /api/v1/users` - Listar usuários
- `POST /api/v1/users` - Criar usuário
- `PUT /api/v1/users/{id}` - Atualizar usuário
- `DELETE /api/v1/users/{id}` - Excluir usuário
- `GET /api/v1/roles` - Listar papéis
- `GET /api/v1/properties` - Listar propriedades
- `GET /api/v1/homogeneous-areas` - Listar áreas
- `GET /api/v1/strata` - Listar unidades operacionais

### Headers Obrigatórios
```javascript
headers: {
    authorization: `bearer ${this.authToken}`
}
```

## 🎨 Customizações CSS

### Multiselect Styling
- Tags em bloco para melhor legibilidade
- Espaçamento adequado entre itens
- Largura mínima para evitar quebras

### Modal Styling
- Largura responsiva (70% desktop)
- Centralização vertical e horizontal
- Padding adequado para conteúdo

### Tabela Styling
- Cores consistentes com tema
- Hover effects nos botões
- Ícones contextuais para ações
