@import "variables";
@import "~bootstrap/scss/bootstrap";

@import "sidebar.scss";
@import "bottom-tab.scss";

.list-container {
	background-color: $green-base;
	border-radius: 1em;
	color: $white;
	margin: 0.5% 0;

	.list__content {
		margin: 0.5%;
		width: 100%;
		a {
			color: $white;
			&:hover {
				color: $white;
			}
		}

		.list--name {
			font-size: 24px;
			margin-left: 2.5%;
		}
	}
}

.switch {
	position: relative;
	display: inline-block;
	width: 60px;
	height: 34px;

	input {
		opacity: 0;
		width: 0;
		height: 0;
		&:checked + .slider {
			background-color: #2196f3;
		}
		&:focus + .slider {
			box-shadow: 0 0 1px #2196f3;
		}
		&:checked + .slider:before {
			-webkit-transform: translateX(26px);
			-ms-transform: translateX(26px);
			transform: translateX(26px);
		}
	}

	.slider {
		position: absolute;
		cursor: pointer;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #ccc;
		-webkit-transition: 0.4s;
		transition: 0.4s;

		&:before {
			position: absolute;
			content: "";
			height: 26px;
			width: 26px;
			left: 4px;
			bottom: 4px;
			background-color: white;
			-webkit-transition: 0.4s;
			transition: 0.4s;
		}

		&.round {
			border-radius: 34px;
			&:before {
				border-radius: 50%;
			}
		}
	}
}

#custom-search-input {
	margin: 0;
	margin-top: 10px;
	padding: 0;

	.search-query {
		width: 100%;
		padding-right: 3px;
		padding-left: 15px;
		/* IE7-8 doesn't have border-radius, so don't indent the padding */
		margin-bottom: 0;
		-webkit-border-radius: 3px;
		-moz-border-radius: 3px;
		border-radius: 0;

		&:focus {
			z-index: 0;
		}
	}

	button {
		border: 0;
		background: none;
		/** belows styles are working good */
		padding: 2px 5px;
		margin-top: 2px;
		position: absolute;
		right: 0;
		/* IE7-8 doesn't have border-radius, so don't indent the padding */
		margin-bottom: 0;
		-webkit-border-radius: 3px;
		-moz-border-radius: 3px;
		border-radius: 3px;
		color: #d9230f;
		cursor: unset;
		z-index: 2;
	}
}

.loader-overlay {
	position: fixed;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #6161615b;
	z-index: 999;
	cursor: pointer;

	span.text {
		display: inline-block;
		position: relative;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		color: #fff;
	}

	.loader {
		animation: loader-animate 1.5s linear infinite;
		clip: rect(0, 80px, 80px, 40px);
		height: 80px;
		width: 80px;
		position: absolute;
		left: calc(50% - 40px);
		top: calc(50% - 40px);

		&:after {
			animation: loader-animate-after 1.5s ease-in-out infinite;
			clip: rect(0, 80px, 80px, 40px);
			content: "";
			border-radius: 50%;
			height: 80px;
			width: 80px;
			position: absolute;
		}
	}

	@keyframes loader-animate {
		0% {
			transform: rotate(0deg);
		}

		100% {
			transform: rotate(220deg);
		}
	}

	@keyframes loader-animate-after {
		0% {
			box-shadow: inset #fff 0 0 0 17px;
			transform: rotate(-140deg);
		}

		50% {
			box-shadow: inset #fff 0 0 0 2px;
		}

		100% {
			box-shadow: inset #fff 0 0 0 17px;
			transform: rotate(140deg);
		}
	}
}

.admin-content {
	margin-top: 2%;

	h2 {
		padding-right: 5% !important;
	}

	.admin-header {
		button {
			height: 100%;
			// width: 100%;
		}
	}
}
