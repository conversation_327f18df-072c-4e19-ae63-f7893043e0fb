// Fonts
@import url("https://fonts.googleapis.com/css2?family=Roboto");

// Variables
@import "variables";

// Bootstrap
// @import "~bootstrap/scss/bootstrap";

html {
	font-family: "Roboto", sans-serif;
	font-weight: 200;
	background: url("/img/background_login.png") no-repeat center center fixed;
	-webkit-background-size: cover;
	-moz-background-size: cover;
	-o-background-size: cover;
	background-size: cover;
}

html,
body {
	height: 100%;
	background-color: #3838386e;
	color: $dark;
}

body.login-box {
	background-color: #f7f9fb;
	font-size: 14px;
}

.login-box {
	.brand {
		width: 9em;
		height: 9em;
		margin: 10px auto;
		position: relative;
		z-index: 1;
	}

	.card-wrapper {
		margin-top: 5%;
		width: 400px;
	}

	.card {
		border-color: transparent;
		box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);

		&.fat {
			padding: 10px;
		}

		.card-title {
			margin-bottom: 30px;
		}

		.card-body {
			.form-inputs {
				padding-bottom: 3.5%;

				.input-group-text:focus {
					outline: none;
					box-shadow: none;
				}
			}
		}

		.form-control {
			background-color: #e7e7e7b9 !important;
			border: 0px;
			border-color: transparent;
			box-shadow: 0 4px 8px rgba(0, 0, 0, 0.144);
		}
	}

	@supports (-webkit-backdrop-filter: none) or (backdrop-filter: none) {
		.card {
			-webkit-backdrop-filter: blur(13px);
			backdrop-filter: blur(13px);
			background-color: rgba(255, 255, 255, 0.5);
		}
	}

	.form-control {
		border-width: 2.3px;
	}

	.form-group label {
		width: 100%;
	}

	.btn.btn-block {
		padding: 12px 10px;
	}

	.footer {
		margin: 3% 0 0 0;
		color: #ffffff;
		text-align: center;
	}
}

@media screen and (max-width: 425px) {
	.login-box .card-wrapper {
		width: 90%;
		margin: 0 auto;
	}
}

@media screen and (max-width: 320px) {
	.login-box .card.fat {
		padding: 0;
		.card-body {
			padding: 15px;
		}
	}
}
