<template>
	<div id="alert-button">
	  <button class="btn-alert" @click="handleClick">
		<i class="fas fa-exclamation-triangle"></i>
		<span v-if="text">{{ text }}</span>
	  </button>
	</div>
  </template>

  <script>
  export default {
	props: {
	  text: {
		type: String,
		default: '',
	  },
	},
	methods: {
	  handleClick() {
		// Altere conforme necessário: redirecionar, emitir evento, abrir modal, etc.
		this.$emit("alert-click");
	  },
	},
  };
  </script>

<style scoped lang="scss">
#alert-button {
	position: fixed;
	bottom: 2%;
	right: 2%;
	z-index: 9999;
}

@keyframes blink {
	0%, 100% {
		opacity: 1;
	}
	50% {
		opacity: 0.4;
	}
}

.btn-alert {
	border-radius: 3px;
	background-color: #fff3cd;
	color: #856404;
	border: 1px solid #ffeeba;
	box-shadow: 0px 8px 15px #0000002d;
	font-size: 1.7em;
	padding: 10px 30px;
	display: flex;
	align-items: center;
	gap: 8px;

	animation: blink 1.5s infinite;

	i {
		color: #ffc107;
		font-size: 2.0em;
	}

	&:hover {
		background-color: #ffeeba;
		cursor: pointer;
	}
}
</style>
