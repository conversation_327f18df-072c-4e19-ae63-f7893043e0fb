<template>
	<nav id="sidebar" :class="{ 'sidebar-collapsed': isCollapsed }">
		<!-- <PERSON><PERSON><PERSON> de Toggle -->
		<button class="sidebar-toggle" @click="toggleSidebar">
			<i :class="isCollapsed ? 'fas fa-chevron-right' : 'fas fa-chevron-left'"></i>
		</button>

		<div class="sidebar-top">
			<h1 v-show="!isCollapsed">PlataformaCacau</h1>
			<h1 v-show="isCollapsed" class="sidebar-title-collapsed">PC</h1>

			<div class="avatar">
				<img
					:title="isCollapsed ? userName : 'PlataformaCacau'"
					class="img-fluid"
					:src="'/img/farmer_avatar.png'"
				/>

				<p v-show="!isCollapsed" class="userName">{{ userName }}</p>
				<p v-show="!isCollapsed" class="userRole">{{ userRole }}</p>

				<div class="nav-links">
					<ul class="sidebar-links">
						<li>
							<button
								v-if="$can('admin') || $can('pre-registered')"
								tag="button"
								@click="navigateTo('/panel')"
								:class="{
									btn: true,
									'sidebar-link': true,
									active: path === 'alert-center',
								}"
							>
								<span class="fas fa-exclamation-triangle fa-lg"></span>
								<span v-show="!isCollapsed" class="link-text">Central de Alertas</span>
							</button>
						</li>
						<li>
							<!--
							<button
								v-if="$can('admin')"
								tag="button"
								@click="navigateTo('/panel/properties')"
								:class="{
									btn: true,
									'sidebar-link': true,
									active:
										path === 'properties' ||
										path === 'homogeneous-area' ||
										path === 'strata' ||
										path === 'sampling-point' ||
										path === 'trees' ||
										path === 'tree-visit',
								}"
							>
								<span class="fa fa-home fa-lg"></span>
								<span class="link-text">Propriedades CEPLAC</span>
							</button>
							-->
							<button
								v-if="$can('pre-registered')"
								tag="button"
								@click="navigateTo('/panel/properties')"
								:class="{
									btn: true,
									'sidebar-link': true,
									active:
										path === 'properties' ||
										path === 'homogeneous-area' ||
										path === 'strata' ||
										path === 'sampling-point' ||
										path === 'trees' ||
										path === 'tree-visit',
								}"
							>
								<span class="fa fa-home fa-lg"></span>
								<span v-show="!isCollapsed" class="link-text">Propriedades</span>
							</button>
						</li>
						<!--
						<li>
							<button
								v-if="$can('pre-registered')"
								tag="button"
								disabled
								:class="{
									btn: true,
									'sidebar-link': true,
								}"
							>
								<i class="fas fa-cogs fa-lg"></i>
								<span class="link-text">Serviços</span>
							</button>
						</li>
						-->
						<li>
							<button
								v-if="$can('admin')"
								tag="button"
								@click="navigateTo('/panel/properties/shared')"
								:class="{
									btn: true,
									'sidebar-link': true,
									active: path === 'shared-properties',
								}"
							>
								<span
									class="fa fa-share-alt-square fa-lg"
								></span>
								<span class="link-text"
									>Propriedades</span
								>
							</button>
						</li>
						<li>
							<button
								v-if="$can('admin') || $can('pre-registered')"
								tag="button"
								@click="navigateTo('/panel/data-analysis')"
								:class="{
									btn: true,
									'sidebar-link': true,
									active: path === 'data-analysis',
								}"
							>
								<span class="fas fa-chart-bar fa-lg"></span>
								<span v-show="!isCollapsed" class="link-text">Análise de Dados</span>
							</button>
						</li>
						<li>
							<button
								v-if="$can('admin') || $can('pre-registered')"
								tag="button"
								@click="navigateTo('/panel/users')"
								:class="{
									btn: true,
									'sidebar-link': true,
									active: path === 'users',
								}"
							>
								<span class="fas fa-user fa-lg"></span>
								<span v-show="!isCollapsed" class="link-text">Usuários</span>
							</button>
						</li>
						<li class="sidebar-item">
							<button
								v-if="$can('admin') || $can('pre-registered')"
								tag="button"
								@click="navigateTo('/panel/map')"
								:class="{
									btn: true,
									'sidebar-link': true,
									active: path === 'map',
								}"
							>
								<span
									class="fas fa-globe-americas fa-lg"
								></span>
								<span v-show="!isCollapsed" class="link-text">Mapa</span>
							</button>
						</li>
						<li>
							<button
								v-if="$can('admin') || $can('pre-registered')"
								tag="button"
								@click="navigateTo('/panel/rfids')"
								:class="{
									btn: true,
									'sidebar-link': true,
									active: path === 'rfids',
								}"
							>
								<span class="fas fa-tags fa-lg"></span>
								<span v-show="!isCollapsed" class="link-text">RFID</span>
							</button>
						</li>

						<li>
							<button
								v-if="$can('admin')"
								tag="button"
								@click="navigateTo('/panel/data-import')"
								:class="{
									btn: true,
									'sidebar-link': true,
									active: path === 'data-import',
								}"
							>
								<span class="fas fa-file-upload fa-lg"></span>
								<span v-show="!isCollapsed" class="link-text">Importar dados</span>
							</button>
						</li>
						<li>
							<button
								v-if="$can('admin')"
								tag="button"
								@click="navigateTo('/panel/docs')"
								:class="{
									btn: true,
									'sidebar-link': true,
									active: path === 'docs',
								}"
							>
								<span class="fas fa-book fa-lg"></span>
								<span v-show="!isCollapsed" class="link-text">Documentações</span>
							</button>
						</li>
					</ul>
				</div>
			</div>
		</div>

		<div class="nav-footer">
			<button
				class="btn btn-footer"
				onclick="event.preventDefault(); document.getElementById('logout-form').submit();"
			>
				<i class="fas fa-sign-out-alt fa-lg"></i>
				<span v-show="!isCollapsed">Sair</span>
			</button>
			<form
				id="logout-form"
				action="/logout"
				method="POST"
				style="display: none"
			>
				<input :value="csrfToken" name="_token" type="hidden" />
			</form>
		</div>
	</nav>
</template>

<script>
export default {
	data() {
		return {
			url: window.location.origin,
			csrfToken: null,
			roles: [],
			can: {},
			userName: null,
			userRole: null,
			actualPath: null,
			path: "",
			windowWidth: window.innerWidth,
			isCollapsed: false,
		};
	},

	created() {
		if (
			localStorage.getItem("path") !== this.$route.name &&
			this.windowWidth > 767
		) {
			this.path = this.$route.name;
			localStorage.setItem("path", this.$route.name);
		} else {
			this.path = this.$route.name;
		}

		this.userName = window.user.name;
		this.csrfToken = document.querySelector(
			'meta[name="csrf-token"]'
		).content;

		this.userRole = this.checkRole(window.Roles[0].label);

		// Carregar estado do sidebar
		const savedCollapsed = localStorage.getItem('sidebarCollapsed');
		if (savedCollapsed !== null) {
			this.isCollapsed = savedCollapsed === 'true';
		}

		// Aplicar classe inicial no body
		if (this.isCollapsed) {
			document.body.classList.add('sidebar-collapsed');
		}
	},

	mounted() {
		this.$nextTick(() => {
			window.addEventListener("resize", this.onResize);
		});
	},

	beforeDestroy() {
		window.removeEventListener("resize", this.onResize);
	},

	methods: {
		toggleSidebar() {
			this.isCollapsed = !this.isCollapsed;
			localStorage.setItem('sidebarCollapsed', this.isCollapsed);

			// Aplicar classe no body para ajustar o conteúdo
			if (this.isCollapsed) {
				document.body.classList.add('sidebar-collapsed');
			} else {
				document.body.classList.remove('sidebar-collapsed');
			}
		},

		navigateTo(path) {
			this.$router.push({ path });
			this.updatePath();
		},

		updatePath() {
			this.path = this.$route.name;
			localStorage.setItem("path", this.$route.name);
		},

		onResize() {
			this.windowWidth = window.innerWidth;

			if (
				localStorage.getItem("path") !== this.$route.name &&
				this.windowWidth < 768
			) {
				this.path = this.$route.name;
				localStorage.setItem("path", this.$route.name);
			} else {
				this.path = this.$route.name;
			}
		},

		checkRole(role) {
			switch (role) {
				case "admin":
					return "Administrador";
				case "properties-manager":
					return "Gerente de Propriedades";
				case "homogeneous_area-manager":
					return "Gerente de ÁHs";
				case "strata-manager":
					return "Gerente de UOs";
				case "sampling_points-manager":
					return "Gerente de PAs";
				case "users-manager":
					return "Gerente de Usuários";
				case "pre-registered":
					return "Cacauicultor";

				default:
					break;
			}
		},
	},

	props: ["src"],
};
</script>

<style lang="scss" scoped>
// Funcionalidade de colapsar e posição fixa
#sidebar {
	position: fixed;
	top: 0;
	left: 0;
	height: 100vh;
	z-index: 1000;
	transition: width 0.3s ease;
	overflow-x: hidden;
	overflow-y: auto;

	&.sidebar-collapsed {
		width: 70px;
	}
}

// Botão de toggle - posicionado dentro do sidebar para não ser cortado
.sidebar-toggle {
	position: absolute;
	top: 15px;
	right: 10px;
	width: 24px;
	height: 24px;
	background: rgba(255, 255, 255, 0.2);
	border: 1px solid rgba(255, 255, 255, 0.5);
	border-radius: 50%;
	color: white;
	cursor: pointer;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 10px;
	z-index: 1001;

	&:hover {
		background: rgba(255, 255, 255, 0.3);
	}

	&:focus {
		outline: none;
	}
}

// Título colapsado
.sidebar-title-collapsed {
	font-size: 1rem;
	font-weight: 700;
	text-align: center;
	margin-bottom: 1rem;
}

// Ajustar o conteúdo principal para não ficar atrás do sidebar
:global(.main-content),
:global(.container),
:global(.container-fluid),
:global(main) {
	margin-left: 280px; /* Largura padrão do sidebar */
	transition: margin-left 0.3s ease;
}

// Quando colapsado
:global(body.sidebar-collapsed) {
	:global(.main-content),
	:global(.container),
	:global(.container-fluid),
	:global(main) {
		margin-left: 70px;
	}
}

// Em mobile, não aplicar margin
@media (max-width: 768px) {
	:global(.main-content),
	:global(.container),
	:global(.container-fluid),
	:global(main) {
		margin-left: 0 !important;
	}
}
</style>
