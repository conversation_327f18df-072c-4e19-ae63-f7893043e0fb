<template>
	<nav id="sidebar" :class="{ 'sidebar-collapsed': isCollapsed }">
		<!-- <PERSON><PERSON><PERSON> de Toggle -->
		<button class="sidebar-toggle" @click="toggleSidebar">
			<i :class="isCollapsed ? 'fas fa-chevron-right' : 'fas fa-chevron-left'"></i>
		</button>

		<div class="sidebar-top">
			<h1 v-show="!isCollapsed">PlataformaCacau</h1>
			<h1 v-show="isCollapsed" class="sidebar-title-collapsed">PC</h1>

			<div class="avatar">
				<img
					:title="isCollapsed ? userName : 'PlataformaCacau'"
					class="img-fluid"
					:src="'/img/farmer_avatar.png'"
				/>

				<p v-show="!isCollapsed" class="userName">{{ userName }}</p>
				<p v-show="!isCollapsed" class="userRole">{{ userRole }}</p>

				<div class="nav-links">
					<ul class="sidebar-links">
						<li>
							<button
								v-if="$can('admin') || $can('pre-registered')"
								tag="button"
								@click="navigateTo('/panel')"
								:class="{
									btn: true,
									'sidebar-link': true,
									active: path === 'alert-center',
								}"
							>
								<span class="fas fa-exclamation-triangle fa-lg"></span>
								<span v-show="!isCollapsed" class="link-text">Central de Alertas</span>
							</button>
						</li>
						<li>
							<!--
							<button
								v-if="$can('admin')"
								tag="button"
								@click="navigateTo('/panel/properties')"
								:class="{
									btn: true,
									'sidebar-link': true,
									active:
										path === 'properties' ||
										path === 'homogeneous-area' ||
										path === 'strata' ||
										path === 'sampling-point' ||
										path === 'trees' ||
										path === 'tree-visit',
								}"
							>
								<span class="fa fa-home fa-lg"></span>
								<span class="link-text">Propriedades CEPLAC</span>
							</button>
							-->
							<button
								v-if="$can('pre-registered')"
								tag="button"
								@click="navigateTo('/panel/properties')"
								:class="{
									btn: true,
									'sidebar-link': true,
									active:
										path === 'properties' ||
										path === 'homogeneous-area' ||
										path === 'strata' ||
										path === 'sampling-point' ||
										path === 'trees' ||
										path === 'tree-visit',
								}"
							>
								<span class="fa fa-home fa-lg"></span>
								<span v-show="!isCollapsed" class="link-text">Propriedades</span>
							</button>
						</li>
						<!--
						<li>
							<button
								v-if="$can('pre-registered')"
								tag="button"
								disabled
								:class="{
									btn: true,
									'sidebar-link': true,
								}"
							>
								<i class="fas fa-cogs fa-lg"></i>
								<span class="link-text">Serviços</span>
							</button>
						</li>
						-->
						<li>
							<button
								v-if="$can('admin')"
								tag="button"
								@click="navigateTo('/panel/properties/shared')"
								:class="{
									btn: true,
									'sidebar-link': true,
									active: path === 'shared-properties',
								}"
							>
								<span
									class="fa fa-share-alt-square fa-lg"
								></span>
								<span v-show="!isCollapsed" class="link-text"
									>Propriedades</span
								>
							</button>
						</li>
						<li>
							<button
								v-if="$can('admin') || $can('pre-registered')"
								tag="button"
								@click="navigateTo('/panel/data-analysis')"
								:class="{
									btn: true,
									'sidebar-link': true,
									active: path === 'data-analysis',
								}"
							>
								<span class="fas fa-chart-bar fa-lg"></span>
								<span v-show="!isCollapsed" class="link-text">Análise de Dados</span>
							</button>
						</li>
						<li>
							<button
								v-if="$can('admin') || $can('pre-registered')"
								tag="button"
								@click="navigateTo('/panel/users')"
								:class="{
									btn: true,
									'sidebar-link': true,
									active: path === 'users',
								}"
							>
								<span class="fas fa-user fa-lg"></span>
								<span v-show="!isCollapsed" class="link-text">Usuários</span>
							</button>
						</li>
						<li class="sidebar-item">
							<button
								v-if="$can('admin') || $can('pre-registered')"
								tag="button"
								@click="navigateTo('/panel/map')"
								:class="{
									btn: true,
									'sidebar-link': true,
									active: path === 'map',
								}"
							>
								<span
									class="fas fa-globe-americas fa-lg"
								></span>
								<span v-show="!isCollapsed" class="link-text">Mapa</span>
							</button>
						</li>
						<li>
							<button
								v-if="$can('admin') || $can('pre-registered')"
								tag="button"
								@click="navigateTo('/panel/rfids')"
								:class="{
									btn: true,
									'sidebar-link': true,
									active: path === 'rfids',
								}"
							>
								<span class="fas fa-tags fa-lg"></span>
								<span v-show="!isCollapsed" class="link-text">RFID</span>
							</button>
						</li>

						<li>
							<button
								v-if="$can('admin')"
								tag="button"
								@click="navigateTo('/panel/data-import')"
								:class="{
									btn: true,
									'sidebar-link': true,
									active: path === 'data-import',
								}"
							>
								<span class="fas fa-file-upload fa-lg"></span>
								<span v-show="!isCollapsed" class="link-text">Importar dados</span>
							</button>
						</li>
						<li>
							<button
								v-if="$can('admin')"
								tag="button"
								@click="navigateTo('/panel/docs')"
								:class="{
									btn: true,
									'sidebar-link': true,
									active: path === 'docs',
								}"
							>
								<span class="fas fa-book fa-lg"></span>
								<span v-show="!isCollapsed" class="link-text">Documentações</span>
							</button>
						</li>
					</ul>
				</div>
			</div>
		</div>

		<div class="nav-footer">
			<button
				class="btn btn-footer"
				onclick="event.preventDefault(); document.getElementById('logout-form').submit();"
			>
				<i class="fas fa-sign-out-alt fa-lg"></i>
				<span v-show="!isCollapsed">Sair</span>
			</button>
			<form
				id="logout-form"
				action="/logout"
				method="POST"
				style="display: none"
			>
				<input :value="csrfToken" name="_token" type="hidden" />
			</form>
		</div>
	</nav>
</template>

<script>
export default {
	data() {
		return {
			url: window.location.origin,
			csrfToken: null,
			roles: [],
			can: {},
			userName: null,
			userRole: null,
			actualPath: null,
			path: "",
			windowWidth: window.innerWidth,
			isCollapsed: false,
		};
	},

	created() {
		if (
			localStorage.getItem("path") !== this.$route.name &&
			this.windowWidth > 767
		) {
			this.path = this.$route.name;
			localStorage.setItem("path", this.$route.name);
		} else {
			this.path = this.$route.name;
		}

		// Carregar estado do sidebar
		const savedCollapsed = localStorage.getItem('sidebarCollapsed');
		if (savedCollapsed !== null) {
			this.isCollapsed = savedCollapsed === 'true';
		}

		this.userName = window.user.name;
		this.csrfToken = document.querySelector(
			'meta[name="csrf-token"]'
		).content;

		this.userRole = this.checkRole(window.Roles[0].label);
	},

	mounted() {
		this.$nextTick(() => {
			window.addEventListener("resize", this.onResize);
		});
	},

	beforeDestroy() {
		window.removeEventListener("resize", this.onResize);
	},

	methods: {
		toggleSidebar() {
			this.isCollapsed = !this.isCollapsed;
			localStorage.setItem('sidebarCollapsed', this.isCollapsed);
			// Emitir evento para que outros componentes possam reagir
			this.$root.$emit('sidebar-toggled', this.isCollapsed);
		},

		navigateTo(path) {
			this.$router.push({ path });
			this.updatePath();
		},

		updatePath() {
			this.path = this.$route.name;
			localStorage.setItem("path", this.$route.name);
		},

		onResize() {
			this.windowWidth = window.innerWidth;

			if (
				localStorage.getItem("path") !== this.$route.name &&
				this.windowWidth < 768
			) {
				this.path = this.$route.name;
				localStorage.setItem("path", this.$route.name);
			} else {
				this.path = this.$route.name;
			}
		},

		checkRole(role) {
			switch (role) {
				case "admin":
					return "Administrador";
				case "properties-manager":
					return "Gerente de Propriedades";
				case "homogeneous_area-manager":
					return "Gerente de ÁHs";
				case "strata-manager":
					return "Gerente de UOs";
				case "sampling_points-manager":
					return "Gerente de PAs";
				case "users-manager":
					return "Gerente de Usuários";
				case "pre-registered":
					return "Cacauicultor";

				default:
					break;
			}
		},
	},

	props: ["src"],
};
</script>

<style lang="scss" scoped>
// Variáveis
$primary-color: #3d8160;
$primary-dark: #2d6047;
$sidebar-width: 280px;
$sidebar-collapsed-width: 70px;
$transition-duration: 0.3s;

// Sidebar Principal
#sidebar {
	position: fixed;
	top: 0;
	left: 0;
	height: 100vh;
	width: $sidebar-width;
	background: linear-gradient(180deg, $primary-color 0%, $primary-dark 100%);
	color: white;
	transition: width $transition-duration ease;
	z-index: 1000;
	overflow-x: hidden;
	overflow-y: auto;
	box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);

	&.sidebar-collapsed {
		width: $sidebar-collapsed-width;

		.sidebar-top h1 {
			opacity: 0;
		}

		.sidebar-title-collapsed {
			opacity: 1;
		}

		.avatar {
			.userName, .userRole {
				opacity: 0;
			}
		}

		.link-text {
			opacity: 0;
		}

		.nav-footer .btn-footer span {
			opacity: 0;
		}
	}
}

// Botão de Toggle
.sidebar-toggle {
	position: absolute;
	top: 15px;
	right: -15px;
	width: 30px;
	height: 30px;
	background: $primary-color;
	border: 2px solid white;
	border-radius: 50%;
	color: white;
	cursor: pointer;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 12px;
	transition: all $transition-duration ease;
	z-index: 1001;

	&:hover {
		background: $primary-dark;
		transform: scale(1.1);
	}

	&:focus {
		outline: none;
		box-shadow: 0 0 0 3px rgba(white, 0.3);
	}
}

// Header da Sidebar
.sidebar-top {
	padding: 2rem 1rem 1rem;
	text-align: center;
	border-bottom: 1px solid rgba(white, 0.2);

	h1 {
		font-size: 1.5rem;
		font-weight: 700;
		margin-bottom: 1rem;
		transition: opacity $transition-duration ease;
		white-space: nowrap;
	}

	.sidebar-title-collapsed {
		opacity: 0;
		font-size: 1.2rem;
		font-weight: 700;
		margin-bottom: 1rem;
		transition: opacity $transition-duration ease;
		position: absolute;
		top: 2rem;
		left: 50%;
		transform: translateX(-50%);
	}
}

// Avatar Section
.avatar {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 1rem;

	img {
		width: 60px;
		height: 60px;
		border-radius: 50%;
		border: 3px solid white;
		margin-bottom: 0.5rem;
		transition: all $transition-duration ease;
	}

	.userName, .userRole {
		margin: 0;
		font-size: 0.9rem;
		transition: opacity $transition-duration ease;
		white-space: nowrap;
	}

	.userName {
		font-weight: 600;
		margin-bottom: 0.25rem;
	}

	.userRole {
		font-size: 0.8rem;
		opacity: 0.8;
	}
}

// Navigation Links
.nav-links {
	flex: 1;
	padding: 1rem 0;
}

.sidebar-links {
	list-style: none;
	padding: 0;
	margin: 0;

	li {
		margin-bottom: 0.5rem;
	}
}

.sidebar-link {
	width: 100%;
	background: transparent;
	border: none;
	color: white;
	padding: 0.75rem 1rem;
	text-align: left;
	cursor: pointer;
	transition: all $transition-duration ease;
	display: flex;
	align-items: center;
	gap: 1rem;
	border-radius: 0 25px 25px 0;
	margin-right: 1rem;

	&:hover {
		background: rgba(white, 0.1);
		transform: translateX(5px);
	}

	&.active {
		background: rgba(white, 0.2);
		border-left: 4px solid white;
	}

	span:first-child {
		min-width: 20px;
		text-align: center;
	}

	.link-text {
		transition: opacity $transition-duration ease;
		white-space: nowrap;
		overflow: hidden;
	}
}

// Footer da Sidebar
.nav-footer {
	padding: 1rem;
	border-top: 1px solid rgba(white, 0.2);

	.btn-footer {
		width: 100%;
		background: rgba(white, 0.1);
		border: 1px solid rgba(white, 0.3);
		color: white;
		padding: 0.75rem 1rem;
		border-radius: 8px;
		cursor: pointer;
		transition: all $transition-duration ease;
		display: flex;
		align-items: center;
		gap: 0.5rem;
		justify-content: center;

		&:hover {
			background: rgba(white, 0.2);
			transform: translateY(-2px);
		}

		span {
			transition: opacity $transition-duration ease;
			white-space: nowrap;
		}
	}
}

// Responsividade
@media (max-width: 768px) {
	#sidebar {
		transform: translateX(-100%);

		&.sidebar-show {
			transform: translateX(0);
		}
	}
}

// Scrollbar personalizada
#sidebar::-webkit-scrollbar {
	width: 6px;
}

#sidebar::-webkit-scrollbar-track {
	background: rgba(white, 0.1);
}

#sidebar::-webkit-scrollbar-thumb {
	background: rgba(white, 0.3);
	border-radius: 3px;

	&:hover {
		background: rgba(white, 0.5);
	}
}
</style>
