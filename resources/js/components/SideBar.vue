<template>
	<nav :class="['sidebar', { minimized: isMinimized }]" id="sidebar">
		<!-- TOPO -->
		<div class="sidebar-top" :class="{ 'invisible': isMinimized }">
			<h1 class="px-3 pt-3">PlataformaCacau</h1>
			<div class="avatar text-center">
				<img title="PlataformaCacau" class="img-fluid" :src="'/img/farmer_avatar.png'" />
				<p class="userName">{{ userName }}</p>
				<p class="userRole">{{ userRole }}</p>
			</div>
		</div>

		<!-- LINKS -->
		<div class="nav-links flex-grow-1 d-flex flex-column">
			<ul class="sidebar-links flex-grow-1">
				<li>
					<button v-if="$can('admin') || $can('pre-registered')" tag="button"
						@click="navigateTo('/panel')"
						:class="{ btn: true, 'sidebar-link': true, active: path === 'alert-center' }">
						<span class="fas fa-exclamation-triangle fa-lg"></span>
						<span class="link-text" v-if="!isMinimized">Central de Alertas</span>
					</button>
				</li>

				<li>
					<button v-if="$can('pre-registered')" tag="button"
						@click="navigateTo('/panel/properties')"
						:class="{ btn: true, 'sidebar-link': true, active: ['properties','homogeneous-area','strata','sampling-point','trees','tree-visit'].includes(path) }">
						<span class="fa fa-home fa-lg"></span>
						<span class="link-text" v-if="!isMinimized">Propriedades</span>
					</button>
				</li>

				<li>
					<button v-if="$can('admin')" tag="button"
						@click="navigateTo('/panel/properties/shared')"
						:class="{ btn: true, 'sidebar-link': true, active: path === 'shared-properties' }">
						<span class="fa fa-share-alt-square fa-lg"></span>
						<span class="link-text" v-if="!isMinimized">Propriedades</span>
					</button>
				</li>

				<li>
					<button v-if="$can('admin') || $can('pre-registered')" tag="button"
						@click="navigateTo('/panel/data-analysis')"
						:class="{ btn: true, 'sidebar-link': true, active: path === 'data-analysis' }">
						<span class="fas fa-chart-bar fa-lg"></span>
						<span class="link-text" v-if="!isMinimized">Análise de Dados</span>
					</button>
				</li>

				<li>
					<button v-if="$can('admin') || $can('pre-registered')" tag="button"
						@click="navigateTo('/panel/users')"
						:class="{ btn: true, 'sidebar-link': true, active: path === 'users' }">
						<span class="fas fa-user fa-lg"></span>
						<span class="link-text" v-if="!isMinimized">Usuários</span>
					</button>
				</li>

				<li>
					<button v-if="$can('admin') || $can('pre-registered')" tag="button"
						@click="navigateTo('/panel/map')"
						:class="{ btn: true, 'sidebar-link': true, active: path === 'map' }">
						<span class="fas fa-globe-americas fa-lg"></span>
						<span class="link-text" v-if="!isMinimized">Mapa</span>
					</button>
				</li>

				<li>
					<button v-if="$can('admin') || $can('pre-registered')" tag="button"
						@click="navigateTo('/panel/rfids')"
						:class="{ btn: true, 'sidebar-link': true, active: path === 'rfids' }">
						<span class="fas fa-tags fa-lg"></span>
						<span class="link-text" v-if="!isMinimized">RFID</span>
					</button>
				</li>

				<li>
					<button v-if="$can('admin')" tag="button"
						@click="navigateTo('/panel/data-import')"
						:class="{ btn: true, 'sidebar-link': true, active: path === 'data-import' }">
						<span class="fas fa-file-upload fa-lg"></span>
						<span class="link-text" v-if="!isMinimized">Importar dados</span>
					</button>
				</li>

				<li>
					<button v-if="$can('admin')" tag="button"
						@click="navigateTo('/panel/docs')"
						:class="{ btn: true, 'sidebar-link': true, active: path === 'docs' }">
						<span class="fas fa-book fa-lg"></span>
						<span class="link-text" v-if="!isMinimized">Documentações</span>
					</button>
				</li>
			</ul>
		</div>

		<!-- FOOTER -->
		<div class="nav-footer d-flex flex-column align-items-center">
			<!-- BOTÃO DE MINIMIZAR -->
			<div class="toggle-wrapper">
				<button class="toggle-btn" @click="isMinimized = !isMinimized">
					<i class="fas" :class="isMinimized ? 'fa-chevron-right' : 'fa-chevron-left'"></i>
				</button>
			</div>

			<button class="btn btn-footer" @click.prevent="logout">
				<i class="fas fa-sign-out-alt fa-lg"></i>
				<span class="link-text" v-if="!isMinimized">Sair</span>
			</button>

			<form id="logout-form" action="/logout" method="POST" style="display: none">
				<input :value="csrfToken" name="_token" type="hidden" />
			</form>
		</div>
	</nav>
</template>

<script>
export default {
	data() {
		return {
			url: window.location.origin,
			csrfToken: null,
			roles: [],
			can: {},
			userName: null,
			userRole: null,
			actualPath: null,
			path: "",
			windowWidth: window.innerWidth,
			isMinimized: false,
		};
	},

	created() {
		if (
			localStorage.getItem("path") !== this.$route.name &&
			this.windowWidth > 767
		) {
			this.path = this.$route.name;
			localStorage.setItem("path", this.$route.name);
		} else {
			this.path = this.$route.name;
		}

		this.userName = window.user.name;
		this.csrfToken = document.querySelector('meta[name="csrf-token"]').content;
		this.userRole = this.checkRole(window.Roles[0].label);
	},

	mounted() {
		this.$nextTick(() => {
			window.addEventListener("resize", this.onResize);
		});
	},

	beforeDestroy() {
		window.removeEventListener("resize", this.onResize);
	},

	methods: {
		navigateTo(path) {
			this.$router.push({ path });
			this.updatePath();
		},

		updatePath() {
			this.path = this.$route.name;
			localStorage.setItem("path", this.$route.name);
		},

		onResize() {
			this.windowWidth = window.innerWidth;

			if (
				localStorage.getItem("path") !== this.$route.name &&
				this.windowWidth < 768
			) {
				this.path = this.$route.name;
				localStorage.setItem("path", this.$route.name);
			} else {
				this.path = this.$route.name;
			}
		},

		checkRole(role) {
			switch (role) {
				case "admin":
					return "Administrador";
				case "properties-manager":
					return "Gerente de Propriedades";
				case "homogeneous_area-manager":
					return "Gerente de ÁHs";
				case "strata-manager":
					return "Gerente de UOs";
				case "sampling_points-manager":
					return "Gerente de PAs";
				case "users-manager":
					return "Gerente de Usuários";
				case "pre-registered":
					return "Cacauicultor";
				default:
					return "";
			}
		},

		logout() {
			document.getElementById('logout-form').submit();
		}
	},

	props: ["src"],
};
</script>

<style scoped>
.sidebar {
	width: 250px;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	transition: width 0.3s ease;
	position: sticky;
	top: 0;
	left: 0;
}

.sidebar.minimized {
	width: 60px !important;
}

.sidebar .avatar img {
	width: 60px;
	height: auto;
	border-radius: 50%;
}

.sidebar .sidebar-links {
	list-style: none;
	padding: 0;
	margin: 0;
}

.sidebar .sidebar-link {
	display: flex;
	align-items: center;
	width: 100%;
	color: white;
	text-align: left;
	padding: 10px 15px;
	background: none;
	border: none;
	cursor: pointer;
	transition: background 0.2s;
}

.sidebar .sidebar-link:hover {
	background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .sidebar-link.active {
	background-color: rgba(255, 255, 255, 0.2);
}

.link-text {
	margin-left: 10px;
}

.btn-footer {
	width: 100%;
	text-align: left;
	padding: 10px 15px;
	display: flex;
	align-items: center;
	background: none;
	border: none;
	color: white;
	cursor: pointer;
}

.toggle-btn-footer {
	background: #ffffff22;
	border: none;
	color: white;
	border-radius: 50%;
	width: 35px;
	height: 35px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: background 0.2s ease;
}

.toggle-btn-footer:hover {
	background: #ffffff44;
}

.toggle-wrapper {
	position: absolute;
	top: 10px;
	right: 10px;
}

.toggle-btn {
	background: #ffffff22;
	border: none;
	color: white;
	border-radius: 50%;
	width: 35px;
	height: 35px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: background 0.2s ease;
}

.toggle-btn:hover {
	background: #ffffff44;
}

.sidebar.minimized .link-text {
	display: none !important;
}

/* mantém espaço mesmo que conteúdo esteja invisível */
.sidebar-top {
	padding: 10px;
	min-height: 180px;
	transition: opacity 0.3s;
}

.sidebar-top.invisible {
	opacity: 0;
	pointer-events: none;
}

.sidebar.minimized .sidebar-top {
	align-items: center;
	text-align: center;
}

</style>
