<template>
	<div class="chart-container">
	  <canvas ref="canvas"></canvas>
	</div>
  </template>

  <script>
  import { Bar } from 'vue-chartjs';

  export default {
	extends: Bar,
	props: ['chartData', 'chartOptions'],
	mounted() {
	  this.renderChart(this.chartData, this.chartOptions);
	},
	watch: {
	  chartData: {
		deep: true,
		handler() {
		  if (this.$data._chart) this.$data._chart.destroy();
		  this.renderChart(this.chartData, this.chartOptions);
		}
	  },
	  chartOptions: {
		deep: true,
		handler() {
		  if (this.$data._chart) this.$data._chart.destroy();
		  this.renderChart(this.chartData, this.chartOptions);
		}
	  }
	},
	beforeDestroy() {
	  if (this.$data._chart) {
		this.$data._chart.destroy();
	  }
	}
  };
  </script>

  <style scoped>
  .chart-container {
	position: relative;
	width: 100%;
	height: 60vh;
  }

  canvas {
	width: 100% !important;
	height: 100% !important;
  }
  </style>
