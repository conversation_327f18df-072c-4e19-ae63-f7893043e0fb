<template>
	<div class="bottom-tab-area">
		<nav id="bottom-tab">
			<ul class="bottom-tab-nav">
				<li>
					<button
						v-if="$can('admin') || $can('pre-registered')"
						tag="button"
						@click="navigateTo('/panel')"
						:class="{
							btn: true,
							'bottom-link': true,
							active: path === 'alert-center',
						}"
					>
						<i class="fas fa-exclamation-triangle"></i>
						<span class="link-text">Central de Alertas</span>
					</button>
				</li>
				<li>
					<button
						v-if="$can('pre-registered')"
						tag="button"
						@click="navigateTo('/panel/properties')"
						:class="{
							btn: true,
							'bottom-link': true,
							active:
								path === 'properties' ||
								path === 'homogeneous-area' ||
								path === 'strata' ||
								path === 'sampling-point' ||
								path === 'trees' ||
								path === 'tree-visit',
						}"
					>
						<i class="fa fa-home"></i>
						<span class="link-text">Propriedades</span>
					</button>
				</li>
				<li>
					<button
						v-if="$can('properties-manager') || $can('admin')"
						tag="button"
						@click="navigateTo('/panel/properties/shared')"
						:class="{
							btn: true,
							'bottom-link': true,
							active: path === 'shared-properties',
						}"
					>
						<i class="fa fa-share-alt-square"></i>
						<span class="link-text small"
							>Propriedades Compartilhadas</span
						>
					</button>
				</li>
				<li>
					<button
						v-if="$can('admin') || $can('pre-registered')"
						tag="button"
						@click="navigateTo('/panel/data-analysis')"
						:class="{
							btn: true,
							'bottom-link': true,
							active: path === 'data-analysis',
						}"
					>
						<i class="fas fa-chart-bar"></i>
						<span class="link-text small">Análise de Dados</span>
					</button>
				</li>
				<li>
					<button
						v-if="$can('users-manager') || $can('admin')"
						tag="button"
						@click="navigateTo('/panel/users')"
						:class="{
							btn: true,
							'bottom-link': true,
							active: path === 'users',
						}"
					>
						<i class="fas fa-user"></i>
						<span class="link-text">Usuários</span>
					</button>
				</li>
				<li>
					<button
						v-if="$can('properties-manager') || $can('admin')"
						tag="button"
						@click="navigateTo('/panel/map')"
						:class="{
							btn: true,
							'bottom-link': true,
							active: path === 'map',
						}"
					>
						<i class="fas fa-globe-americas"></i>
						<span class="link-text">Mapa</span>
					</button>
				</li>
				<li>
					<button
						v-if="$can('admin') || $can('pre-registered')"
						tag="button"
						@click="navigateTo('/panel/rfids')"
						:class="{
							btn: true,
							'bottom-link': true,
							active: path === 'rfids',
						}"
					>
						<i class="fas fa-tags"></i>
						<span class="link-text">RFID</span>
					</button>
				</li>

				<li>
					<button
						v-if="$can('admin')"
						tag="button"
						@click="navigateTo('/panel/docs')"
						:class="{
							btn: true,
							'bottom-link': true,
							active: path === 'docs',
						}"
					>
						<i class="fas fa-book"></i>
						<span class="link-text">Documentações</span>
					</button>
				</li>


				<li>
					<button
						v-if="$can('admin')"
						tag="button"
						@click="navigateTo('/panel/data-import')"
						:class="{
							btn: true,
							'bottom-link': true,
							active: path === 'data-import',
						}"
					>
						<i class="fas fa-file-upload"></i>
						<span class="link-text">Importar dados</span>
					</button>
				</li>

			</ul>
		</nav>
	</div>
</template>

<script>
export default {
	data() {
		return {
			url: window.location.origin,
			csrfToken: null,
			roles: [],
			can: {},
			userName: null,
			userRole: null,
			actualPath: null,
			path: null,
			windowWidth: window.innerWidth,
		};
	},

	created() {
		if (
			localStorage.getItem("path") !== this.$route.name &&
			this.windowWidth < 768
		) {
			this.path = this.$route.name;
			localStorage.setItem("path", this.$route.name);
		} else {
			this.path = this.$route.name;
		}

		this.userName = window.user.name;
		this.userRole = window.Roles[0].label;
		this.csrfToken = document.querySelector(
			'meta[name="csrf-token"]'
		).content;
	},

	mounted() {
		this.$nextTick(() => {
			window.addEventListener("resize", this.onResize);
		});
	},

	beforeDestroy() {
		window.removeEventListener("resize", this.onResize);
	},

	methods: {
		navigateTo(path) {
			this.$router.push({ path });
			this.updatePath();
		},

		updatePath() {
			this.path = this.$route.name;
			localStorage.setItem("path", this.$route.name);
		},

		onResize() {
			this.windowWidth = window.innerWidth;

			if (
				localStorage.getItem("path") !== this.$route.name &&
				this.windowWidth < 768
			) {
				this.path = this.$route.name;
				localStorage.setItem("path", this.$route.name);
			} else {
				this.path = this.$route.name;
			}
		},
	},

	props: ["src"],
};
</script>

<style lang="scss" scoped>
// Variáveis
$primary-color: #3d8160;
$primary-dark: #2d6047;

// Bottom Tab Area
.bottom-tab-area {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background: linear-gradient(180deg, $primary-color 0%, $primary-dark 100%);
	box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
	border-top: 1px solid rgba(255, 255, 255, 0.1);
}

// Bottom Tab Navigation
#bottom-tab {
	max-width: 100%;
	margin: 0 auto;
	padding: 0.5rem 1rem;
}

.bottom-tab-nav {
	display: flex;
	justify-content: space-around;
	align-items: center;
	list-style: none;
	margin: 0;
	padding: 0;
	gap: 0.5rem;

	li {
		flex: 1;
		display: flex;
		justify-content: center;
	}
}

// Bottom Links
.bottom-link {
	background: transparent;
	border: none;
	color: white;
	padding: 0.75rem 0.5rem;
	cursor: pointer;
	transition: all 0.3s ease;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 0.25rem;
	border-radius: 8px;
	min-width: 60px;
	text-decoration: none;

	&:hover {
		background: rgba(255, 255, 255, 0.1);
		transform: translateY(-2px);
		color: white;
		text-decoration: none;
	}

	&.active {
		background: rgba(255, 255, 255, 0.2);
		color: white;
	}

	&:focus {
		outline: none;
		box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
	}

	i {
		font-size: 1.2rem;
		margin-bottom: 0.25rem;
	}

	.link-text {
		font-size: 0.7rem;
		font-weight: 500;
		text-align: center;
		line-height: 1.2;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		max-width: 80px;
	}
}

// Responsividade
@media (max-width: 480px) {
	.bottom-tab-nav {
		gap: 0.25rem;
	}

	.bottom-link {
		padding: 0.5rem 0.25rem;
		min-width: 50px;

		i {
			font-size: 1rem;
		}

		.link-text {
			font-size: 0.65rem;
			max-width: 60px;
		}
	}
}

@media (max-width: 360px) {
	.bottom-link {
		.link-text {
			font-size: 0.6rem;
			max-width: 50px;
		}
	}
}

// Adicionar padding-bottom ao body quando o bottom tab estiver visível
:global(body) {
	padding-bottom: 80px;

	@media (min-width: 769px) {
		padding-bottom: 0;
	}
}
</style>
