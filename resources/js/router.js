import VueRouter from "vue-router";

import Map from "./pages/Map/Map.vue";
import Docs from "./pages/Docs/Docs.vue";
import User from "./pages/User/User.vue";
import Tree from "./pages/Tree/Tree.vue";
import Login from "./pages/Auth/Login.vue";
import Rfids from "./pages/Rfids/Rfids.vue";
import Signup from "./pages/Auth/Signup.vue";
import SignupUnified from "./pages/Auth/SignupUnified.vue";
import Strata from "./pages/Strata/Strata.vue";
import Reports from "./pages/Reports/Reports.vue";
import TreeVisit from "./pages/Tree/TreeVisit.vue";
import Overview from "./pages/Blocks/Overview.vue";
import EditBlock from "./pages/Blocks/EditBlock.vue";
import ListBlocks from "./pages/Blocks/ListBlocks.vue";
import DataImport from "./pages/DataImport/DataImport.vue";
import Properties from "./pages/Properties/Properties.vue";
import ForgotPassword from "./pages/Auth/ForgotPassword.vue";
import ChangePassword from "./pages/User/ChangePassword.vue";
import ResetPasswordForm from "./pages/Auth/ResetPasswordForm";
import AddProperties from "./pages/Properties/AddProperties.vue";
import SamplingPoint from "./pages/SamplingPoint/SamplingPoint.vue";
import SharedProperties from "./pages/Properties/SharedProperties.vue";
import AddOperationalUnit from "./pages/Strata/AddOperationalUnit.vue";
import HomogeneousArea from "./pages/HomogeneousArea/HomogeneousArea.vue";
import HomogeneousAreaPractice from "./pages/HomogeneousArea/HomogeneousAreaPractice.vue";
import TotalCocoa from "./pages/DataAnalysis/TotalData/TotalCocoa/TotalCocoa.vue";
import TotalPiece from "./pages/DataAnalysis/TotalData/TotalPiece/TotalPiece.vue";
import TotalWitchBroom from "./pages/DataAnalysis/TotalData/TotalWitchBroom/TotalWitchBroom.vue";
import TotalRat from "./pages/DataAnalysis/TotalData/TotalRat/TotalRat.vue";
import TotalLoss from "./pages/DataAnalysis/TotalData/TotalLoss/TotalLoss.vue";
import TotalRotten from "./pages/DataAnalysis/TotalData/TotalRotten/TotalRotten.vue";
import TotalHarvested from "./pages/DataAnalysis/TotalData/TotalHarvested/TotalHarvested.vue";
import SafraResults from "./pages/DataAnalysis/TotalData/TotalBobbins/TotalBobbins.vue";
import DataAnalysis from "./pages/DataAnalysis/DataAnalysis.vue";
import DataPercentage from "./pages/DataAnalysis/DataPercentage/DataPercentage.vue";
import HarvestSummary from "./pages/DataAnalysis/HarvestSummary/HarvestSummary.vue";
import TotalHarvestHA from "./pages/DataAnalysis/TotalDataHA/TotalHarvestedHA/TotalHarvestedHA.vue";
import TotalLossHA from "./pages/DataAnalysis/TotalDataHA/TotalLossHA/TotalLossHA.vue";
import TotalPieceHA from "./pages/DataAnalysis/TotalDataHA/TotalPieceHA/TotalPieceHA.vue";
import TotalRatHA from "./pages/DataAnalysis/TotalDataHA/TotalRatHA/TotalRatHA.vue";
import TotalRottenHA from "./pages/DataAnalysis/TotalDataHA/TotalRottenHA/TotalRottenHA.vue";
import TotalWitchBroomHA from "./pages/DataAnalysis/TotalDataHA/TotalWitchBroomHA/TotalWitchBroomHA.vue";
import FruitLossByArea from "./pages/DataAnalysis/FruitLoss/FruitLossByArea.vue";
import FruitLossByHarvest from "./pages/DataAnalysis/FruitLoss/FruitLossByHarvest.vue";
import AddProperties2 from "./pages/Properties/AddProperties2.vue";
import Pruning from "./pages/Alerts/Pruning/Pruning.vue";
import WitchBroom from "./pages/Alerts/WitchBroom/WitchBroom.vue";
import AlertCenter from "./pages/AlertCenter/AlertCenter.vue";

const router = new VueRouter({
	mode: "history",
	base: process.env.BASE_URL,
	routes: [
		{
			path: "/login",
			component: Login,
		},
		{
			path: "/signup",
			component: Signup,
		},
		{
			path: "/signup-unified",
			component: SignupUnified,
		},
		{
			path: "/reset",
			name: "reset-password",
			component: ForgotPassword,
		},
		{
			path: "/reset/:token",
			name: "reset-password-form",
			component: ResetPasswordForm,
		},
		{
			path: "/panel",
			name: "alert-center",
			component: AlertCenter,
			// beforeEnter: (to, from, next) => {
			// 	if (window.Roles[0].label === "pre-registered") {
			// 		next({ name: "properties" });
			// 	} else {
			// 		next();
			// 	}
			// },
		},
		{
			path: "/panel/homogeneous-area",
			component: HomogeneousArea,
			name: "homogeneous-area",
		},
		{
			path: "/panel/homogeneous-area-practice",
			component: HomogeneousAreaPractice,
		},
		{
			path: "/panel/strata",
			component: Strata,
			name: "strata",
		},
		{
			path: "/panel/blocks",
			component: ListBlocks,
		},
		{
			path: "/panel/add-operational-unit",
			component: AddOperationalUnit,
		},
		{
			path: "/panel/edit-block",
			component: EditBlock,
		},
		{
			path: "/panel/properties",
			component: Properties,
			name: "properties",
		},
		{
			path: "/panel/properties/shared",
			component: SharedProperties,
			name: "shared-properties",
		},
		{
			path: "/panel/map",
			component: Map,
			name: "map",
		},
		{
			path: "/panel/overview",
			component: Overview,
			name: "overview",
		},
		{
			path: "/panel/add-property",
			component: AddProperties,
		},
		{
			path: "/panel/users",
			component: User,
			name: "users",
		},
		{
			path: "/panel/rfids",
			component: Rfids,
			name: "rfids",
		},
		{
			path: "/panel/sampling-points",
			component: SamplingPoint,
			name: "sampling-point",
		},
		{
			path: "/panel/trees",
			component: Tree,
			name: "trees",
		},
		{
			path: "/panel/tree-visits",
			component: TreeVisit,
			name: "tree-visit",
		},
		{
			path: "/panel/reports",
			component: Reports,
			name: "reports",
		},
		{
			path: "/panel/docs",
			component: Docs,
			name: "docs",
		},
		{
			path: "/panel/data-import",
			component: DataImport,
			name: "data-import",
		},
		{
			path: "/panel/change-password",
			component: ChangePassword,
		},
		{
			path: "/panel/total-cocoa",
			component: TotalCocoa,
			name: "total-cocoa",
		},
		{
			path: "/panel/total-piece",
			component: TotalPiece,
			name: "total-piece",
		},
		{
			path: "/panel/total-witch-broom",
			component: TotalWitchBroom,
			name: "total-witch-broom",
		},
		{
			path: "/panel/total-rat",
			component: TotalRat,
			name: "total-rat",
		},
		{
			path: "/panel/total-loss",
			component: TotalLoss,
			name: "total-loss",
		},
		{
			path: "/panel/total-rotten",
			component: TotalRotten,
			name: "total-rotten",
		},
		{
			path: "/panel/total-harvested",
			component: TotalHarvested,
			name: "total-harvested",
		},
		{
			path: "/panel/safra-results",
			component: SafraResults,
			name: "safra-results",
		},
		{
			path: "/panel/data-analysis",
			component: DataAnalysis,
			name: "data-analysis",
		},
		{
			path: "/panel/data-percentage",
			component: DataPercentage,
			name: "data-percentage",
		},
		{
			path: "/panel/harvest-summary",
			component: HarvestSummary,
			name: "harvest-summary",
		},
		{
			path: "/panel/total-harvested-ha",
			component: TotalHarvestHA,
			name: "total-harvest-ha",
		},
		{
			path: "/panel/total-loss-ha",
			component: TotalLossHA,
			name: "total-loss-ha",
		},
		{
			path: "/panel/total-piece-ha",
			component: TotalPieceHA,
			name: "total-piece-ha",
		},
		{
			path: "/panel/total-rat-ha",
			component: TotalRatHA,
			name: "total-rat-ha",
		},
		{
			path: "/panel/total-rotten-ha",
			component: TotalRottenHA,
			name: "total-rotten-ha",
		},
		{
			path: "/panel/total-witch-broom-ha",
			component: TotalWitchBroomHA,
			name: "total-witch-broom-ha",
		},
		{
			path: "/panel/fruit-loss-by-area",
			component: FruitLossByArea,
			name: "fruit-loss-by-area",
		},
		{
			path: "/panel/fruit-loss-by-harvest",
			component: FruitLossByHarvest,
			name: "fruit-loss-by-harvest",
		},
		{
			path: "/panel/add-property2",
			component: AddProperties2,
			name: "add-property2",
		},
		{
			path: "/panel/pruning",
			component: Pruning,
			name: "pruning",
		},
		{
			path: "/panel/witch-broom",
			component: WitchBroom,
			name: "witch-broom",
		}
	],
});

export default router;
