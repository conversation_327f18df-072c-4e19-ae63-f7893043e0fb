import Vue from "vue";
import VueMask from "v-mask";
import VueTheMask from "vue-the-mask";
import Multiselect from "vue-multiselect";
import { validate } from "gerador-validador-cpf";
import { Fields, PaginationOptions } from "./TableOptions";

Vue.use(VueMask);
Vue.use(VueTheMask);
Vue.component("multiselect", Multiselect);

export default {
	components: { Multiselect },
	data() {
		return {
			authToken: null,
			isAdmin: false,
			users: [],
			user: {
				id: "",
				name: "",
				cpf: "",
				phone: "",
				email: "",
				password: "",
				status: true,
				homogeneous_areas: [],
				properties: [],
				strata: [],
				homogeneous_areas_id: null,
				sampling_points: [],
				sampling_points_id: null,
				roles: [],
				permissions: [],
			},
			userIsCollector: false,
			currentUser: null,
			email_confirm: "",
			password_confirm: "",
			roles: [],
			permissions: [],
			homogeneousAreasIds: [],
			samplingPointsIds: [],
			rolesId: [],
			permissionsId: [],
			currentRoles: {
				id: "",
				title: "",
				label: "",
			},
			currentPermissions: {
				id: "",
				title: "",
				label: "",
			},
			properties: [],
			homogeneous_areas: [],
			strata: [],
			sampling_points: [],
			messages: [],
			haveError: false,
			editMode: false,
			loading: false,
			fields: Fields,
			paginationOptions: PaginationOptions,
			value: null,
			options: ["list", "of", "options"],
			target: null,
			fileFormat: null,
			pdfLoading: false,
		};
	},

	created() {
		this.authToken = window.token;
		console.log(this.authToken);
		this.fields[4].sortFn = this.sortStatus;

		this.checkAdmin(window.Roles);
		this.index();
		this.allRoles();
		this.allSamplingPoints();
		this.allHomogeneousAreas();
		this.allProperties();
		// this.allPermissions();
	},

	computed: {
		userRoles() {
			return this.user.roles;
		},
		email() {
			return this.user.email;
		},
		password() {
			return this.user.password;
		},
		cpf() {
			return this.user.cpf;
		},
		filteredHomogeneousAreas() {
			// Se o usuário não for coletor ou não houver propriedades selecionadas, retorna vazio
			if (!this.userIsCollector || !this.user.properties?.length) return [];

			const selectedPropertyIds = this.user.properties.map(p => p.id);

			return this.homogeneous_areas.filter(area =>
			  selectedPropertyIds.includes(area.property.id)
			);
		  },
		  filteredStrata() {
			if (!this.userIsCollector || !this.user.homogeneous_areas?.length) return [];

			const selectedHAIds = this.user.homogeneous_areas.map(area => area.id);

			// Filtra pontos amostrais relacionados às áreas homogêneas selecionadas
			const matchingPoints = this.sampling_points.filter(point =>
			  selectedHAIds.includes(point.stratum.homogeneous_area.id)
			);

			// Retorna apenas um ponto amostral por stratum (eliminando duplicatas)
			const uniqueStrataMap = new Map();

			matchingPoints.forEach(point => {
			  const stratumId = point.stratum.id;
			  if (!uniqueStrataMap.has(stratumId)) {
				uniqueStrataMap.set(stratumId, point);
			  }
			});

			return Array.from(uniqueStrataMap.values());
		  }

	},

	watch: {
		currentRoles(userRoles) {
			this.user.roles = userRoles;
			this.isCollector(userRoles);
		},

		email(email) {
			this.user.email = email;
			this.emailValidate(email);
		},

		email_confirm(email_confirm) {
			this.email_confirm = email_confirm;
			this.emailConfirmation(email_confirm);
		},

		/*password(password) {
			this.user.password = password;
			this.passwordValidate(password);
		},*/

		password_confirm(password_confirm) {
			this.password_confirm = password_confirm;
			this.passwordConfirmation(password_confirm);
		},

		cpf(cpf) {
			this.user.cpf = cpf;
			this.cpfValidate(cpf);
		},

		'user.properties'(newProps) {
			// IDs das propriedades selecionadas
			const selectedIds = newProps.map(p => p.id);

			// Remove áreas homogêneas de propriedades que não estão mais selecionadas
			this.user.homogeneous_areas = this.user.homogeneous_areas.filter(ah =>
				selectedIds.includes(ah.property.id)
			);

			// Também remove unidades operacionais que pertencem a áreas removidas
			this.user.strata = this.user.strata.filter(stratum =>
				selectedIds.includes(stratum.stratum.homogeneous_area.property.id)
			);
		},

		// Quando as áreas homogêneas forem alteradas
		'user.homogeneous_areas'(newAreas) {
			// IDs das áreas selecionadas
			const selectedIds = newAreas.map(ah => ah.id);

			// Remove unidades operacionais de áreas que não estão mais selecionadas
			this.user.strata = this.user.strata.filter(stratum =>
				selectedIds.includes(stratum.stratum.homogeneous_area.id)
			);
		}
	},

	methods: {
		handleSelectAll(listName, allOptions) {
			this.user[listName] = [...allOptions];
		},

		isAllSelected(listName, allOptions) {
			const selectedIds = this.user[listName].map(o => o.id);
			return allOptions.every(o => selectedIds.includes(o.id));
		},

		getSelectableOptions(listName, allOptions, labelKey = 'name') {
			const isAll = this.isAllSelected(listName, allOptions);
			if (isAll) {
				// Se todas estiverem selecionadas, retorna apenas as opções reais (sem o botão especial)
				return allOptions;
			}

			const specialOption = {
				id: '__all__',
				[labelKey]: 'Selecionar todos',
				name: 'Selecionar todos',
				__isSpecial: true,
				__deselect: false,
				__class: 'selectLabel',
			};

			return [specialOption, ...allOptions];
		},

		onOptionSelect(option, listName, allOptions) {
			if (option.__isSpecial) {
				this.handleSelectAll(listName, allOptions);
			}
		},

		exportUserPDF() {
			this.pdfLoading = true;
			this.fileFormat = "pdf";
			this.target = "users";

			axios
				.post(
					"/api/v1/export-user-pdf",
					{ target: this.target }, // Passando o 'target' no corpo da requisição
					{
						responseType: "arraybuffer",
						headers: { Authorization: `Bearer ${this.authToken}` }, // Corrigido a interpolação
					}
				)
				.then((response) => {
					this.downloadFile(response, "Usuarios", this.fileFormat);
					this.pdfLoading = false;
				})
				.catch((err) => {
					console.log(err.response);
					this.pdfLoading = false;
				});
		},

		customPropertyLabel({ property, name }) {
			return `${name}`;
		},

		customLabel({ stratum, label }) {
			return `P.A. ${label}`;
		},

		customStrataLabel({ stratum, label }) {
			return `${stratum.label} - ${stratum.homogeneous_area.label} - ${stratum.homogeneous_area.property.name}`;
		},

		customHomogeneousAreaLabel({ property, label }) {
			return `${label} - ${property.name}`;
		},

		isCollector(roles) {
			roles.forEach((role) => {
				if (role.label === "collector") {
					this.userIsCollector = true;
				}
			});
		},

		checkAdmin(roles) {
			roles.forEach((role) => {
				if (role.label === "admin") {
					this.isAdmin = true;
				}
			});
		},

		cpfValidate(cpf) {
			if (!(cpf.length < 11)) {
				if (!validate(cpf)) {
					this.messages["cpf"] = "CPF inválido";
					this.haveError = true;
				} else {
					this.messages["cpf"] = null;
					this.haveError = false;
				}
			}
		},

		emailValidate(email) {
			if (/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(email)) {
				this.messages["email"] = null;
				this.haveError = false;
			} else {
				this.messages["email"] = "Endereço de email inválido";
				this.haveError = true;
			}
		},

		emailConfirmation(email_confirm) {
			if (this.user.email != email_confirm) {
				this.messages["email_confirm"] = "Confirme o endereço de email";
				this.haveError = true;
			} else {
				this.messages["email_confirm"] = null;
				this.haveError = false;
			}
		},

		passwordValidate(password) {
			if (!(password.length < 8)) {
				this.messages["password"] = null;
				this.haveError = false;
			} else {
				this.messages["password"] =
					"Senha deve ter ao menos 8 caracteres";
				this.haveError = true;
			}
		},

		passwordConfirmation(password_confirm) {
			if (this.user.password != password_confirm) {
				this.messages["password_confirm"] = "Senhas não são iguais";
				this.haveError = true;
			} else {
				this.messages["password_confirm"] = null;
				this.haveError = false;
			}
		},

		format(cpf) {
			if (cpf.length == 14) {
				cpf = cpf.replace(".", "");
				cpf = cpf.replace(".", "");
				cpf = cpf.replace("-", "");
			}

			return cpf;
		},

		sortStatus(x, y) {
			if (!(typeof x === "boolean")) {
				return x < y ? -1 : x > y ? 1 : 0;
			}
			this.fields[4].sortFn = null;
		},

		editModal(user) {
			this.editMode = true;
			this.fill_roles(user);

			this.user.id = user.id;
			this.user.name = user.name;
			this.user.email = user.email;
			this.user.phone = user.phone;
			this.user.cpf = user.cpf;
			this.user.password = null;
			this.user.roles = user.roles;
			this.user.sampling_points = user.sampling_points;
			this.user.homogeneous_areas = user.homogeneous_areas;

			$("#modalUsers").modal("show");
		},

		editModalRoles(user) {
			this.editMode = true;
			this.fill_roles(user);

			this.user.id = user.id;
			this.user.name = user.name;
			this.user.email = user.email;
			this.user.phone = user.phone;
			this.user.cpf = user.cpf;
			this.user.password = null;
			this.user.roles = user.roles;
			this.user.sampling_points = user.sampling_points;
			this.user.homogeneous_areas = user.homogeneous_areas;

			if (this.user.homogeneous_areas?.length) {
				const selectedPropertyIds = this.user.homogeneous_areas.map(ha => ha.property.id);
				this.user.properties = this.properties.filter(p => selectedPropertyIds.includes(p.id));
			} else {
				this.user.properties = [];
			}

			if (this.user.sampling_points?.length) {
				const uniqueStrataMap = new Map();

				this.user.sampling_points.forEach(point => {
					const stratumId = point.stratum.id;
					if (!uniqueStrataMap.has(stratumId)) {
						uniqueStrataMap.set(stratumId, point);
					}
				});

				this.user.strata = Array.from(uniqueStrataMap.values());
			} else {
				this.user.strata = [];
			}

			console.log(user.homogeneous_areas);
			$("#modalUsersRoles").modal("show");
		},

		addModal() {
			this.clearForm();
			this.editMode = false;

			$("#modalUsers").modal("show");
		},
		addModalRoles() {
			this.clearRolesForm();
			this.editMode = false;

			$("#modalUsersRoles").modal("show");
		},

		allHomogeneousAreas() {
			axios
				.get("/api/v1/homogeneous-area", {
					headers: { authorization: `bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						this.homogeneous_areas = response.data.data;
					} else {
						console.log(response);
					}
				})
				.catch((err) => {
					console.log(err);
				});
		},

		allProperties() {
			axios
				.get("/api/v1/properties", {
					headers: { authorization: `bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						this.properties = response.data.data;
					} else {
						console.log(response);
					}
				})
				.catch((err) => {
					console.log(err);
				});
				console.log(this.properties);
		},

		allSamplingPoints() {
			axios
				.get("/api/v1/sampling-points", {
					headers: { authorization: `bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						this.sampling_points = response.data.data;
					} else {
						console.log(response);
					}
				})
				.catch((err) => {
					console.log(err);
				});
		},

		allRoles() {
			const roles = window.Roles;

			axios
				.get(`/api/v1/roles`, {
					headers: { authorization: `bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						const currentRole = roles[0].label;

						this.roles = response.data.data.filter((role) => {
							if (currentRole === "admin") {
								return (
									role.label === "admin" ||
									role.label === "pre-registered" ||
									role.label === "collector"
								);
							}

							if (currentRole === "pre-registered") {
								return (
									role.label === "pre-registered" ||
									role.label === "collector"
								);
							}

							// Comportamento padrão para outros papéis (mantido o original)
							if (currentRole === "users-manager") {
								return role.label !== "pre-registered";
							}

							return false; // Bloqueia tudo para papéis não previstos
						});
					} else {
						console.log(response);
					}
				})
				.catch((err) => {
					console.log(err);
				});
		},

		allPermissions() {
			axios
				.get(`/api/v1/permissions`, {
					headers: { authorization: `bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						this.permissions = response.data.data;
					} else {
						console.log(response);
					}
				})
				.catch((err) => {
					console.log(err);
				});
		},

		getHomogeneousAreaID(homogeneous_areas) {
			homogeneous_areas.forEach((homogeneous_area) => {
				this.homogeneousAreasIds.push(homogeneous_area.id);
			});
		},

		getSamplingPointID(sampling_points) {
			sampling_points.forEach((sampling_point) => {
				this.samplingPointsIds.push(sampling_point.id);
			});
		},

		getRoleId(roles) {
			roles.forEach((role) => {
				this.rolesId.push(role.id);
			});
		},

		// getPermissionId(permissions) {
		// 	permissions.forEach(permission => {
		// 		this.permissionsId.push(permission.id);
		// 	});
		// },

		index() {
			this.loading = true;

			axios
				.get("/api/v1/users", {
					headers: { authorization: `bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						this.users = response.data.data;
					} else {
						console.log(response);
					}
					this.loading = false;
				})
				.catch((err) => {
					this.loading = false;
				});
		},

		store() {
			this.getRoleId(this.currentRoles);
			this.getSamplingPointID(this.user.sampling_points);
			this.getHomogeneousAreaID(this.user.homogeneous_areas);
			this.user.roles = this.rolesId;
			this.user.sampling_points_id = this.samplingPointsIds;
			this.user.homogeneous_areas_id = this.homogeneousAreasIds;

			if (!this.haveError) {
				axios
					.post(`/api/v1/users`, this.user, {
						headers: { authorization: `bearer ${this.authToken}` },
					})
					.then((response) => {
						if (response.status === 201 && response.data.success) {
							Swal.fire({
								title: response.data.message,
								icon: "success",
								showConfirmButton: false,
								timer: 1500,
							});
						} else {
							console.log(response);
						}
						this.clearForm();
						this.index();
					})
					.catch((err) => {
						Swal.fire({
							title: err.response.data.message,
							text: err.response.data.detail,
							icon: "error",
							confirmButtonText: "Fechar",
						});
					});
			}
		},

		update() {
			if (this.user.strata?.length) {
				const selectedStrataIds = this.user.strata.map(s => s.stratum.id);
				this.user.sampling_points = this.sampling_points.filter(point =>
					selectedStrataIds.includes(point.stratum.id)
				);
			} else {
				this.user.sampling_points = [];
			}

			this.getRoleId(this.currentRoles);
			this.getSamplingPointID(this.user.sampling_points);
			this.getHomogeneousAreaID(this.user.homogeneous_areas);

			this.user.roles = this.rolesId;
			this.user.sampling_points_id = this.samplingPointsIds;
			this.user.homogeneous_areas_id = this.homogeneousAreasIds;

			if (!this.haveError) {
				axios
					.put(`/api/v1/users/${this.user.id}`, this.user, {
						headers: { authorization: `bearer ${this.authToken}` },
					})
					.then((response) => {
						if (response.status === 200 && response.data.success) {
							Swal.fire({
								title: response.data.message,
								icon: "success",
								showConfirmButton: false,
								timer: 1500,
							});
						} else {
							console.log(response);
						}
						this.clearRolesForm();
						this.index();
					})
					.catch((err) => {
						Swal.fire({
							title: err.response.data.message,
							text: err.response.data.detail,
							icon: "error",
							confirmButtonText: "Fechar",
						});
					});
			}
		},

		changeStatus(user_id, status = true) {
			let status_message = "";
			this.user_status = status;

			status
				? (status_message = "ativar")
				: (status_message = "desativar");

			Swal.fire({
				title: "Deseja " + status_message + " este usuário?",
				text: "",
				icon: "warning",
				showCancelButton: true,
				confirmButtonColor: "#3085d6",
				cancelButtonColor: "#d33",
				confirmButtonText: "Sim",
				cancelButtonText: "Cancelar",
			}).then((result) => {
				if (result.value) {
					axios
						.put(
							`/api/v1/users/${user_id}/status`,
							{
								status: this.user_status,
							},
							{
								headers: {
									authorization: `bearer ${this.authToken}`,
								},
							}
						)
						.then((response) => {
							if (response.status === 200) {
								Swal.fire({
									title: response.data.message,
									icon: "success",
									showConfirmButton: false,
									timer: 1500,
								});
							} else {
								console.log(response);
							}
							this.index();
						})
						.catch((err) => {
							console.log(err);
						});
				}
			});
		},

		clearForm() {
			this.user.id = null;
			this.user.name = "";
			this.user.email = "";
			this.user.phone = "";
			this.email_confirm = "";
			this.user.password = null;
			this.password_confirm = "";
			this.user.cpf = "";
			this.user.status = true;
			this.user.roles = [];
			this.userIsCollector = false;
			this.currentRoles = [];
			this.samplingPointsIds = [];
			this.homogeneousAreasIds = [];
			this.rolesId = [];
			this.messages = [];

			$("#modalUsers").modal("hide");
		},

		fill_roles: function (user) {
			let roles = this.roles;

			this.currentUser = user;
			this.currentRoles = _.map(user.roles, function (roles) {
				return roles;
			});
		},

		fill_permissions: function (user) {
			let permissions = this.permissions;

			this.currentUser = user;
			this.currentPermissions = _.map(
				user.permissions,
				function (permissions) {
					return permissions;
				}
			);
		},

		clearRolesForm() {
			this.user.id = null;
			this.user.name = "";
			this.user.email = "";
			this.user.phone = "";
			this.email_confirm = "";
			this.user.password = null;
			this.password_confirm = "";
			this.user.cpf = "";
			this.user.status = true;
			this.user.roles = [];
			this.userIsCollector = false;
			this.currentRoles = [];
			this.samplingPointsIds = [];
			this.homogeneousAreasIds = [];
			this.rolesId = [];
			this.messages = [];

			$("#modalUsersRoles").modal("hide");
		},

		fill_roles: function (user) {
			let roles = this.roles;

			this.currentUser = user;
			this.currentRoles = _.map(user.roles, function (roles) {
				return roles;
			});
		},

		fill_permissions: function (user) {
			let permissions = this.permissions;

			this.currentUser = user;
			this.currentPermissions = _.map(
				user.permissions,
				function (permissions) {
					return permissions;
				}
			);
		},

		downloadFile(response, filename, fileformat) {
			let type = "";
			switch (fileformat) {
				case "pdf":
					type = "application/pdf";
					break;
				case "xls":
					type = "application/vnd.ms-excel";
					break;
				case "csv":
					type = "text/csv";
					break;
				default:
					console.log("error - file format!");
			}

			var newBlob = new Blob([response.data], {
				type: type,
			});

			const data = window.URL.createObjectURL(newBlob);
			var link = document.createElement("a");

			if (window.navigator && window.navigator.msSaveOrOpenBlob) {
				window.navigator.msSaveOrOpenBlob(newBlob);
			}

			link.href = data;
			link.download = `${filename}.${fileformat}`;
			link.click();
			// For Firefox
			setTimeout(function () {
				window.URL.revokeObjectURL(data);
			}, 100);
		},
	},
};
