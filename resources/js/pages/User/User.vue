<template>
	<div id="users" class="mt-5 usersWrapper">
		<div class="admin-content">
			<div
				class="admin-header d-flex justify-content-between align-items-center p-2"
			>
				<h1>Usu<PERSON>rios</h1>
				<div class="button-group d-flex">
					<form @submit.prevent="exportUserPDF()">
						<button
							title="Exportar PDF"
							type="submit"
							class="btn btn-md btn-danger"
							style="margin: 16px 4px 16px 0px; padding: 6px 12px; height: 2.5rem; width: 2.5rem;"
							:disabled="
								pdfLoading === true
							"
						>
							<span
									v-if="
										pdfLoading === true
									"
										class="spinner-border spinner-border-sm"
										role="status"
										aria-hidden="true"
							></span>
							<i
								v-else
								class="fas fa-file-pdf"
							></i>
						</button>
					</form>

					<button
						type="button"
						class="btn btn-agro btn-add"
						style="height: 2.5rem; width: 2.5rem;"
						@click="addModal"
					>
						<i class="fas fa-plus"></i>
					</button>
				</div>
			</div>
			<br />

			<div v-if="loading" class="loader-overlay">
				<loader :loading="loading"></loader>
			</div>

			<div class="users-table" v-else>
				<vue-good-table
					title="Usuários"
					:columns="fields"
					:rows="users"
					:search-options="{
						enabled: true,
						placeholder: 'Pesquisar...',
					}"
					:pagination-options="paginationOptions"
					:sort-options="{
						enabled: true,
						initialSortBy: [
							{ field: 'status', type: 'desc' },
							{ field: 'id', type: 'asc' },
						],
					}"
					compactMode
				>
					<div slot="emptystate">
						Não há registros para esta pesquisa
					</div>
					<template slot="table-row" slot-scope="props">
						<span v-if="props.column.field == 'cpf'">{{
							props.row.cpf | VMask("###.###.###-##")
						}}</span>

						<span v-else-if="props.column.field == 'roles'">
							<span
								v-for="role in props.row.roles"
								v-bind:key="role.id"
							>
								{{ role.title }}
								<br />
							</span>
						</span>

						<div
							v-else-if="props.column.field == 'status'"
							class="custom-control custom-switch"
						>
							<input
								type="checkbox"
								class="custom-control-input"
								v-bind:id="`status_${props.row.id}`"
								v-model="props.row.status"
								@click.prevent="
									props.row.status
										? changeStatus(
												props.row.id,
												(status = false)
										  )
										: changeStatus(props.row.id)
								"
							/>
							<label
								class="custom-control-label"
								v-bind:for="`status_${props.row.id}`"
								>{{
									props.row.status ? "Desativar" : "Ativar"
								}}</label
							>
						</div>
						<div v-else-if="props.column.field == 'actions'">
							<button
							title="Editar"
							class="btn btn-secondary"
							@click.prevent="editModal(props.row)"
						>
							<i class="fas fa-cog fa-lg"></i>
							</button>
							<button
								title="Editar Papeis"
								class="btn btn-secondary"
								@click.prevent="editModalRoles(props.row)"
							>
								<i class="fas fa-user-tag fa-lg"></i>
							</button>
						</div>

					</template>
				</vue-good-table>
			</div>

			<div
				class="modal fade"
				id="modalUsers"
				ref="modal"
				tabindex="-1"
				role="dialog"
				aria-labelledby="modalUsersLabel"
				aria-hidden="true"
			>
				<div
					class="modal-dialog modal-dialog-centered modal-lg"
					role="document"
				>
					<div class="modal-content">
						<div class="modal-header">
							<h5
								class="modal-title"
								id="modalUsersLabelAdd"
								v-show="!editMode"
							>
								ADICIONAR NOVO USUÁRIO
							</h5>
							<h5
								class="modal-title"
								id="modalUsersLabelEdit"
								v-show="editMode"
							>
								{{
									`ATUALIZAR USUÁRIO: ${user.name.toUpperCase()}`
								}}
							</h5>
							<button
								type="button"
								class="close"
								data-dismiss="modal"
								aria-label="Close"
							>
								<span aria-hidden="true">&times;</span>
							</button>
						</div>
						<div class="modal-body">
							<form
								@submit.prevent="editMode ? update() : store()"
							>
								<div class="form-group">
									<label for="name">Nome Completo</label>
									<input
										id="name"
										type="text"
										class="form-control"
										v-model="user.name"
										required
									/>
								</div>

								<div class="form-group">
									<label for="phone">Telefone</label>
									<input
										id="phone"
										type="phone"
										class="form-control"
										aria-describedby="phoneHelp"
										v-model="user.phone"
										v-mask="[
											'(##) ####-####',
											'(##) #####-####',
										]"
										placeholder="(99) 9999-9999"
										pattern="(\([0-9]{2}\))\s([9]{1})?([0-9]{4})-([0-9]{4})"
										required
									/>
									<small
										v-if="messages.phone"
										id="phoneHelp"
										class="form-text text-danger"
										>{{ messages.phone }}</small
									>
								</div>

								<div class="form-group">
									<label for="cpf">CPF</label>
									<input
										id="cpf"
										type="text"
										class="form-control"
										aria-describedby="cpfHelp"
										v-model="user.cpf"
										v-mask="'###.###.###-##'"
										required
									/>
									<small
										v-if="messages.cpf"
										id="cpfHelp"
										class="form-text text-danger"
										>{{ messages.cpf }}</small
									>
								</div>

								<div class="form-group">
									<label for="email">E-mail</label>
									<input
										id="email"
										type="email"
										class="form-control"
										aria-describedby="emailHelp"
										v-model="user.email"
										required
									/>
									<small
										v-if="messages.email"
										id="emailHelp"
										class="form-text text-danger"
										>{{ messages.email }}</small
									>
								</div>

								<div v-if="!editMode" class="form-group">
									<label for="email_confirmation"
										>Confirmação de e-mail</label
									>
									<input
										id="email_confirmation"
										type="email"
										class="form-control"
										aria-describedby="emailConfirmHelp"
										v-model="email_confirm"
										required
									/>
									<small
										v-if="messages.email_confirm"
										id="emailConfirmHelp"
										class="form-text text-danger"
										>{{ messages.email_confirm }}</small
									>
								</div>

								<div v-if="!editMode" class="form-group">
									<label for="password">Senha</label>
									<input
										id="password"
										type="password"
										class="form-control"
										aria-describedby="passwordHelp"
										v-model="user.password"
										required
									/>
									<small
										v-if="messages.password"
										id="passwordHelp"
										class="form-text text-danger"
										>{{ messages.password }}</small
									>
								</div>

								<div v-if="!editMode" class="form-group">
									<label for="password_confirmation"
										>Confimação de senha</label
									>
									<input
										id="password_confirmation"
										type="password"
										class="form-control"
										aria-describedby="passwordHelp"
										v-model="password_confirm"
										required
									/>
									<small
										v-if="messages.password_confirm"
										id="passwordHelp"
										class="form-text text-danger"
										>{{ messages.password_confirm }}</small
									>
								</div>

								<div class="modal-footer">
									<button
										type="button"
										class="btn btn-secondary"
										data-dismiss="modal"
									>
										Sair
									</button>
									<button
										type="submit"
										class="btn btn-primary"
										v-show="!editMode"
										:disabled="haveError"
									>
										Cadastrar
									</button>
									<button
										type="submit"
										class="btn btn-primary"
										v-show="editMode"
									>
										Atualizar
									</button>
								</div>
							</form>
						</div>
				</div>
			</div>
			<div
				class="modal fade"
				id="modalUsersRoles"
				ref="modal"
				tabindex="-1"
				role="dialog"
				aria-labelledby="modalUsersRolesLabel"
				aria-hidden="true"
			>
				<div
					class="modal-dialog modal-dialog-centered modal-lg"
					role="document"
				>
					<div class="modal-content">
						<div class="modal-header">
							<h5
								class="modal-title"
								id="modalUsersRolesLabel"
								v-show="editMode"
							>
								{{
									`ATUALIZAR PAPÉIS DO USUÁRIO: ${user.name.toUpperCase()}`
								}}
							</h5>
							<button
								type="button"
								class="close"
								data-dismiss="modal"
								aria-label="Close"
							>
								<span aria-hidden="true">&times;</span>
							</button>
						</div>
						<div class="modal-body">
							<form @submit.prevent="update()">
								<div class="form-row">
									<!-- Seleção de papéis -->
									<div class="col-md-6 col-sm-12 form-group">
										<label for="roles">Papéis</label>
										<div v-for="role in roles" :key="role.id">
											<div class="form-check">
												<label class="form-check-label" :for="'role_' + role.id">
													<input
														:id="'role_' + role.id"
														type="checkbox"
														name="roles[]"
														:value="role"
														v-model="currentRoles"
														:checked="user.roles"
													/>
													<span>{{ role.title }}</span>
												</label>
											</div>
										</div>
									</div>
								</div>

								<div class="form-row">
								<!-- Propriedades -->
								<div class="col-md-4 col-sm-12 form-group">
									<label for="properties">Propriedades</label>
									<multiselect
									v-model="user.properties"
									ref ="multiselectProperties"
									:placeholder="userIsCollector ? 'Selecione as propriedades' : 'Usuário não autorizado'"
									selectLabel="Pressione para selecionar"
									deselectLabel="Pressione para remover"
									selectedLabel="Selecionado"
									track-by="id"
									:custom-label="option => option.__isSpecial ? option.name : customPropertyLabel(option)"
									:options="getSelectableOptions('properties', properties)"
									:multiple="true"
									:close-on-select="false"
									:clear-on-select="false"
									:preserve-search="true"
									:preselect-first="true"
									:disabled="!userIsCollector"
									class="w-100 min-width-multiselect"
									@select="onOptionSelect($event, 'properties', properties)"
									>
									<template slot="option" slot-scope="props">
										<div
										class="option__desc"
										:class="{
											'option--select-all': props.option.__isSpecial && !props.option.__deselect,
											'option--deselect-all': props.option.__isSpecial && props.option.__deselect,
											[props.option.__class]: !!props.option.__class
										}"
										>
										<span class="option__title">
											{{ props.option.__isSpecial ? props.option.name : props.option.name }}
										</span>
										</div>
									</template>
									</multiselect>
								</div>

								<!-- Áreas Homogêneas -->
								<div class="col-md-4 col-sm-12 form-group">
									<label for="homogeneous_areas">Áreas Homogêneas</label>
									<multiselect
									v-model="user.homogeneous_areas"
									ref ="multiselectHomogeneousAreas"
									:placeholder="!userIsCollector ?  'Usuário não autorizado' : (user.properties.length === 0) ? 'Selecione uma propriedade primeiro' :'Selecione as Áreas Homogêneas'"
									selectLabel="Pressione para selecionar"
									deselectLabel="Pressione para remover"
									selectedLabel="Selecionado"
									track-by="id"
									:custom-label="option => option.__isSpecial ? option.name : customHomogeneousAreaLabel(option)"
									:options="getSelectableOptions('homogeneous_areas', filteredHomogeneousAreas)"
									:multiple="true"
									:close-on-select="false"
									:clear-on-select="false"
									:preserve-search="true"
									:preselect-first="true"
									:disabled="!userIsCollector || user.properties.length === 0"
									class="w-100 min-width-multiselect"
									@select="onOptionSelect($event, 'homogeneous_areas', filteredHomogeneousAreas)"
									>
									<template slot="option" slot-scope="props">
										<div
										class="option__desc"
										:class="{
											'option--select-all': props.option.__isSpecial && !props.option.__deselect,
											'option--deselect-all': props.option.__isSpecial && props.option.__deselect,
											[props.option.__class]: !!props.option.__class
										}"
										>
										<span class="option__title">
											{{ props.option.__isSpecial
											? props.option.name
											: `${props.option.label} - ${props.option.property.name}` }}
										</span>
										</div>
									</template>
									</multiselect>
								</div>

								<!-- Pontos Amostrais -->
								<div class="col-md-4 col-sm-12 form-group">
									<label for="strata">Unidades Operacionais</label>
									<multiselect
									v-model="user.strata"
									ref ="multiselectStrata"
									:placeholder="!userIsCollector ?  'Usuário não autorizado' : (user.properties.length === 0) ? 'Selecione uma propriedade primeiro' : (user.homogeneous_areas.length === 0) ? 'Selecione uma área homogênea primeiro' :'Selecione as Unidades Operacionais'"
									selectLabel="Pressione para selecionar"
									deselectLabel="Pressione para remover"
									selectedLabel="Selecionado"
									track-by="id"
									:custom-label="option => option.__isSpecial ? option.name : customStrataLabel(option)"
									:options="getSelectableOptions('strata', filteredStrata)"
									:multiple="true"
									:close-on-select="false"
									:clear-on-select="false"
									:preserve-search="true"
									:preselect-first="true"
									:disabled="!userIsCollector || user.properties.length === 0 || user.homogeneous_areas.length === 0"
									class="w-100 min-width-multiselect"
									@select="onOptionSelect($event, 'strata', filteredStrata)"
									>
									<template slot="option" slot-scope="props">
										<div
										class="option__desc"
										:class="{
											'option--select-all': props.option.__isSpecial && !props.option.__deselect,
											'option--deselect-all': props.option.__isSpecial && props.option.__deselect,
											[props.option.__class]: !!props.option.__class
										}"
										>
										<span class="option__title">
											{{ props.option.__isSpecial
											? props.option.name
											: `${props.option.stratum.label} - ${props.option.stratum.homogeneous_area.label} - ${props.option.stratum.homogeneous_area.property.name}` }}
										</span>
										</div>
									</template>
									</multiselect>
								</div>
								</div>

								<p>Ao selecionar uma Unidade Operacional, o coletor ficará responsável por todos os Pontos Amostrais associados a ela</p>
								<div class="modal-footer">
									<button type="button" class="btn btn-secondary" data-dismiss="modal">
										Sair
									</button>
									<button type="submit" class="btn btn-primary" v-show="editMode">
										Atualizar
									</button>
								</div>
							</form>
						</div>

					</div>
				</div>
			</div>
		</div>
	</div>
	</div>
</template>

<script src="./User.js"></script>
<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>
<style lang="scss" scoped>
.usersWrapper {
	background-color: #f5f8fd;
	border-radius: 20px;
	min-height: 90vh;
	padding: 1rem;
	font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

	.admin-content {
		.admin-header {
			background-color: white;
			border-radius: 15px;
			box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
			margin-bottom: 20px;
			padding: 1.5rem;

			h1 {
				color: #3d8160;
				font-family: "Lexend", sans-serif;
				font-weight: 600;
				font-size: 28px;
				margin: 0;
				background: linear-gradient(135deg, #3d8160 0%, #2d6047 100%);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
				background-clip: text;
			}

			.button-group {
				display: flex;
				gap: 8px;
				align-items: center;

				.btn-danger {
					background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
					border: none;
					border-radius: 8px;
					transition: all 0.3s ease;
					box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);

					&:hover:not(:disabled) {
						transform: translateY(-2px);
						box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
					}

					&:disabled {
						opacity: 0.7;
						cursor: not-allowed;
						transform: none;
					}
				}

				.btn-add {
					background: linear-gradient(135deg, #3d8160 0%, #2d6047 100%);
					border: none;
					color: white;
					border-radius: 8px;
					transition: all 0.3s ease;
					box-shadow: 0 4px 15px rgba(61, 129, 96, 0.3);

					&:hover {
						transform: translateY(-2px);
						box-shadow: 0 6px 20px rgba(61, 129, 96, 0.4);
					}
				}
			}
		}

		.users-table {
			background-color: white;
			border-radius: 20px;
			box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
			padding: 25px;
			margin: 20px 0;
			transition: transform 0.3s ease;

			&:hover {
				transform: translateY(-2px);
			}
		}
	}

	// Modal customizations
	.modal-dialog {
		max-width: 70%;
		width: auto;
	}

	// Modal customizations
	.modal-dialog {
		max-width: 70%;
		width: auto;
	}

	// Melhorias para botões de ação na tabela
	.btn {
		margin: 0 2px;
		min-width: 40px;
		height: 40px;
		display: inline-flex;
		align-items: center;
		justify-content: center;
	}
}
	::v-deep .multiselect__tags {
		display: block;
		width: 100%;
	}

	::v-deep .multiselect__tag {
		display: block;
		width: 100%;
		white-space: normal;
		margin-bottom: 4px;
		text-align: left;
	}

	::v-deep .multiselect__tag:last-child {
		margin-bottom: 12px; /* ou qualquer valor que você quiser */
	}


	// Responsividade para tablets
	@media (max-width: 992px) {
		.usersWrapper {
			.admin-header {
				flex-direction: column;
				align-items: stretch;

				h1 {
					padding: 16px 24px 8px 24px;
					text-align: center;
					font-size: 22px;
				}

				.button-group {
					justify-content: center;
					margin: 0 24px 16px 24px;
					align-items: center;

					form, button {
						flex: 1;
						max-width: 120px;
					}

					form {
						display: flex;
						align-items: center;
					}
				}
			}

			.users-table {
				padding: 0 16px 32px;
			}

			.modal-dialog {
				max-width: 90%;
				margin: 1rem;
			}
		}
	}

	// Responsividade para mobile
	@media (max-width: 768px) {
		.usersWrapper {
			height: auto;
			max-height: none;
			border-radius: 0;

			.admin-header {
				padding: 1rem;

				h1 {
					font-size: 20px;
					padding: 0;
					margin-bottom: 1rem;
				}

				.button-group {
					margin: 0;
					align-items: center;

					form, button {
						height: 44px;
						min-width: 44px;
					}

					form {
						display: flex;
						align-items: center;
					}

					.btn {
						padding: 8px 12px;
						font-size: 14px;
					}
				}
			}

			.users-table {
				padding: 0 8px 24px;
			}

			.modal-dialog {
				max-width: 95%;
				margin: 0.5rem;
			}

			.modal-content {
				border-radius: 12px;
			}

			.modal-header {
				padding: 1rem;

				.modal-title {
					font-size: 16px;
					line-height: 1.3;
				}
			}

			.modal-body {
				padding: 1rem;

				.form-group {
					margin-bottom: 1rem;
				}

				.form-row {
					margin: 0;

					[class*="col-"] {
						padding: 0 0.5rem;
						margin-bottom: 1rem;
					}
				}

				label {
					font-size: 14px;
					font-weight: 600;
					margin-bottom: 0.5rem;
				}

				.form-control {
					font-size: 16px;
					padding: 0.75rem;
					border-radius: 8px;
				}

				.multiselect {
					font-size: 14px;
				}
			}

			.modal-footer {
				padding: 1rem;
				flex-direction: column;
				gap: 0.5rem;

				.btn {
					width: 100%;
					padding: 0.75rem;
					font-size: 16px;
					border-radius: 8px;
				}

				.btn-secondary {
					order: 2;
				}

				.btn-primary {
					order: 1;
				}
			}
		}

		// Melhorias específicas para a tabela em mobile
		::v-deep .vgt-table {
			font-size: 14px;

			th, td {
				padding: 8px 4px;
				white-space: nowrap;
			}

			// Correção para botões de ação na tabela
			td:last-child {
				white-space: normal;
				min-width: 90px;
				padding: 8px 6px;
			}

			.btn {
				padding: 6px 8px;
				margin: 1px 2px;
				font-size: 12px;
				min-width: 36px;
				height: 36px;
				display: inline-block;
				vertical-align: top;

				i {
					font-size: 11px;
				}
			}
		}

		::v-deep .vgt-global-search {
			margin-bottom: 1rem;

			.vgt-search-box {
				width: 100%;
				padding: 0.75rem;
				font-size: 16px;
				border-radius: 8px;
			}
		}

		::v-deep .vgt-selection-info-row {
			font-size: 12px;
			padding: 0.5rem;
		}
	}

	// Responsividade para telas muito pequenas
	@media (max-width: 576px) {
		.usersWrapper {
			.admin-header {
				.button-group {
					flex-direction: column;
					gap: 0.5rem;

					form, button {
						max-width: none;
						width: 100%;
					}

					form {
						width: 100%;

						button {
							width: 100%;
						}
					}
				}
			}

			.modal-dialog {
				max-width: 100%;
				margin: 0;
				height: 100vh;
			}

			.modal-content {
				height: 100%;
				border-radius: 0;
				border: none;
			}

			.modal-body {
				flex: 1;
				overflow-y: auto;

				.form-row [class*="col-"] {
					flex: 0 0 100%;
					max-width: 100%;
				}
			}
		}

		::v-deep .vgt-table {
			th, td {
				padding: 6px 3px;
				font-size: 12px;
			}

			// Melhor configuração para botões de ação empilhados
			td:last-child {
				min-width: 80px;
				padding: 8px 4px;
				text-align: center;
			}

			.btn {
				min-width: 32px;
				height: 32px;
				padding: 4px 6px;
				margin: 1px auto;
				display: block;
				width: 90%;
				margin-bottom: 3px;
				border-radius: 4px;

				&:last-child {
					margin-bottom: 0;
				}

				i {
					font-size: 9px;
				}
			}
		}
	}

	// Responsividade intermediária para melhor transição
	@media (max-width: 640px) and (min-width: 577px) {
		::v-deep .vgt-table {
			td:last-child {
				min-width: 85px;
				padding: 8px 5px;
			}

			.btn {
				min-width: 34px;
				height: 34px;
				padding: 5px 7px;
				margin: 1px;

				i {
					font-size: 10px;
				}
			}
		}
	}

	// Melhorias para switch de status
	@media (max-width: 768px) {
		::v-deep .custom-control-label {
			font-size: 12px;
			padding-left: 1.5rem;
		}

		::v-deep .custom-control-input {
			transform: scale(0.8);
		}
	}

	// Responsividade
	@media (max-width: 768px) {
		.usersWrapper {
			padding: 0.5rem;
			min-height: 85vh;
		}

		.admin-header {
			flex-direction: column;
			text-align: center;
			gap: 1rem;

			h1 {
				font-size: 24px !important;
			}

			.button-group {
				justify-content: center;
				width: 100%;

				.btn-danger,
				.btn-add {
					width: 2.5rem;
					height: 2.5rem;
				}
			}
		}

		.users-table {
			padding: 15px !important;
			margin: 15px 0 !important;
		}

		// Melhorias para multiselect em mobile
		:deep(.multiselect) {
			.multiselect__tags {
				min-height: 44px;
				padding: 8px 40px 0 8px;
			}

			.multiselect__tag {
				font-size: 12px;
				padding: 4px 8px;
				margin: 2px 4px 2px 0;
			}

			.multiselect__option {
				font-size: 14px;
				padding: 12px;
				line-height: 1.4;
			}

			.multiselect__input {
				font-size: 16px;
				padding: 4px 8px;
			}
		}
	}

	@media (max-width: 480px) {
		.admin-header h1 {
			font-size: 20px !important;
		}
	}
</style>
