<template>
	<div id="user-profile" class="change-password-wrapper">
		<div class="change-password-container">
			<div class="page-header">
				<h1 class="page-title">
					<i class="fas fa-key"></i>
					Alteração de Senha
				</h1>
				<p class="page-subtitle">Mantenha sua conta segura atualizando sua senha regularmente</p>
			</div>

			<div class="password-form-card">
				<form @submit.prevent="validateForm()">
					<div class="form-section">
						<div class="modern-form-group">
							<label for="currentPassword" class="modern-label">
								<i class="fas fa-lock"></i>
								Senha Atual
							</label>
							<div class="modern-input-wrapper">
								<input
									id="currentPassword"
									:type="currentPasswordField"
									class="modern-input"
									name="currentPassword"
									placeholder="Digite sua senha atual"
									v-model="currentPassword"
									required
								/>
								<button
									type="button"
									class="password-toggle-btn"
									@click.prevent="switchVisibility('current')"
									:title="currentPasswordField === 'password' ? 'Mostrar senha' : 'Ocultar senha'"
								>
									<i
										:class="currentPasswordField === 'password' ? 'fas fa-eye' : 'fas fa-eye-slash'"
									></i>
								</button>
							</div>
						</div>

						<div class="modern-form-group">
							<label for="newPassword" class="modern-label">
								<i class="fas fa-key"></i>
								Nova Senha
							</label>
							<div class="modern-input-wrapper">
								<input
									id="newPassword"
									:type="newPasswordField"
									class="modern-input"
									name="newPassword"
									placeholder="Digite sua nova senha"
									v-model="newPassword"
									v-validate="'required'"
									ref="newPassword"
									required
								/>
								<button
									type="button"
									class="password-toggle-btn"
									@click.prevent="switchVisibility('new')"
									:title="newPasswordField === 'password' ? 'Mostrar senha' : 'Ocultar senha'"
								>
									<i
										:class="newPasswordField === 'password' ? 'fas fa-eye' : 'fas fa-eye-slash'"
									></i>
								</button>
							</div>
						</div>

						<div class="modern-form-group">
							<label for="newPasswordConfirmation" class="modern-label">
								<i class="fas fa-check-circle"></i>
								Confirmar Nova Senha
							</label>
							<div class="modern-input-wrapper">
								<input
									id="newPasswordConfirmation"
									:type="newPasswordConfirmationField"
									class="modern-input"
									name="newPasswordConfirmation"
									placeholder="Confirme sua nova senha"
									v-model="newPasswordConfirmation"
									v-validate="'required|confirmed:newPassword'"
									data-vv-as="newPassword"
									required
								/>
								<button
									type="button"
									class="password-toggle-btn"
									@click.prevent="switchVisibility('new_confirm')"
									:title="newPasswordConfirmationField === 'password' ? 'Mostrar senha' : 'Ocultar senha'"
								>
									<i
										:class="newPasswordConfirmationField === 'password' ? 'fas fa-eye' : 'fas fa-eye-slash'"
									></i>
								</button>
							</div>
							<div
								v-if="errors.has('newPasswordConfirmation')"
								class="error-message"
							>
								<i class="fas fa-exclamation-triangle"></i>
								As senhas devem ser iguais
							</div>
						</div>
					</div>

					<div class="form-actions">
						<button type="submit" class="btn-modern btn-primary">
							<i class="fas fa-save"></i>
							Atualizar Senha
						</button>
					</div>
				</form>
			</div>
		</div>
	</div>
</template>

<script src="./ChangePassword.js"></script>

<style lang="scss" scoped>
.change-password-wrapper {
	background-color: #f5f8fd;
	min-height: 100vh;
	padding: 2rem;
}

.change-password-container {
	max-width: 600px;
	margin: 0 auto;
}

.page-header {
	text-align: center;
	margin-bottom: 2.5rem;

	.page-title {
		color: #3d8160;
		font-family: "Lexend", sans-serif;
		font-weight: 600;
		font-size: 2rem;
		margin-bottom: 0.5rem;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 0.75rem;

		i {
			font-size: 1.8rem;
		}
	}

	.page-subtitle {
		color: #6c757d;
		font-family: "Lexend", sans-serif;
		font-size: 1rem;
		font-weight: 400;
		margin: 0;
	}
}

.password-form-card {
	background: white;
	border-radius: 20px;
	padding: 2.5rem;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
	border: 1px solid rgba(61, 129, 96, 0.1);
}

.form-section {
	margin-bottom: 2rem;
}

.modern-form-group {
	margin-bottom: 1.5rem;

	.modern-label {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		font-family: "Lexend", sans-serif;
		font-weight: 500;
		color: #2d3748;
		margin-bottom: 0.75rem;
		font-size: 0.95rem;

		i {
			color: #3d8160;
			font-size: 0.9rem;
		}
	}

	.modern-input-wrapper {
		position: relative;

		.modern-input {
			width: 100%;
			padding: 1rem 3rem 1rem 1rem;
			border: 2px solid #e2e8f0;
			border-radius: 12px;
			font-size: 1rem;
			font-family: "Lexend", sans-serif;
			background-color: #fafafa;
			transition: all 0.3s ease;

			&:focus {
				outline: none;
				border-color: #3d8160;
				background-color: white;
				box-shadow: 0 0 0 3px rgba(61, 129, 96, 0.1);
			}

			&::placeholder {
				color: #a0aec0;
			}
		}

		.password-toggle-btn {
			position: absolute;
			right: 1rem;
			top: 50%;
			transform: translateY(-50%);
			background: none;
			border: none;
			color: #718096;
			cursor: pointer;
			padding: 0.5rem;
			border-radius: 6px;
			transition: all 0.2s ease;

			&:hover {
				color: #3d8160;
				background-color: rgba(61, 129, 96, 0.1);
			}

			&:focus {
				outline: none;
				box-shadow: 0 0 0 2px rgba(61, 129, 96, 0.2);
			}

			i {
				font-size: 1rem;
			}
		}
	}

	.error-message {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		color: #e53e3e;
		font-size: 0.875rem;
		font-family: "Lexend", sans-serif;
		margin-top: 0.5rem;

		i {
			font-size: 0.8rem;
		}
	}
}

.form-actions {
	display: flex;
	justify-content: center;
	padding-top: 1rem;
	border-top: 1px solid #e2e8f0;

	.btn-modern {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		padding: 1rem 2rem;
		border: none;
		border-radius: 12px;
		font-family: "Lexend", sans-serif;
		font-weight: 500;
		font-size: 1rem;
		cursor: pointer;
		transition: all 0.3s ease;
		text-decoration: none;

		&.btn-primary {
			background: linear-gradient(135deg, #3d8160 0%, #2d6147 100%);
			color: white;

			&:hover {
				transform: translateY(-2px);
				box-shadow: 0 8px 25px rgba(61, 129, 96, 0.3);
			}

			&:active {
				transform: translateY(0);
			}
		}
	}
}

/* Responsividade */
@media (max-width: 768px) {
	.change-password-wrapper {
		padding: 1rem;
	}

	.page-header .page-title {
		font-size: 1.5rem;
		flex-direction: column;
		gap: 0.5rem;
	}

	.password-form-card {
		padding: 1.5rem;
	}

	.modern-form-group .modern-input-wrapper .modern-input {
		padding: 0.875rem 2.5rem 0.875rem 0.875rem;
	}
}
</style>
