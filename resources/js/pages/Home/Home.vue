<template>
	<div id="site-content">
		<div class="site-content">
			<section id="banner" class="banner">
				<div class="shapes-container">
					<div class="bg-shape"></div>
				</div>
				<div class="container h-100">
					<div class="row">
						<div
							class="col-lg-6 col-md-4 col-sm-12 mx-auto order-md-2"
						>
							<img
								src="/img/mockup_web_mobile.png"
								class="img-fluid"
								alt="Mockup do sistema web e app mobile"
							/>
						</div>

						<div
							class="col-lg-6 col-md-4 col-sm-12 order-md-1 text-center text-md-left pr-md-5"
						>
							<h1 class="mb-3">Plataforma Cacau</h1>
							<p class="lead mb-4 text-justify">
								É uma plataforma integrada com sistemas e
								aplicações que coletam dados durante o período
								do cultivo do cacau, servindo como ferramenta de
								análise e prospecção.
							</p>
						</div>
					</div>
				</div>
			</section>

			<section id="about" class="container about mb-4">
				<div class="row no-gutters">
					<div
						class="col-lg-6 col-md-6 col-sm-12 order-md-2 texto px-4"
					>
						<h2 class="mb-2 mt-2 text-center">Sobre</h2>
						<p class="text-justify">
							PlataformaCacau é um sistema computacional que tem
							por objetivo armazenar dados de coletas dos eventos
							fenológicos (lançamento foliar, floração, produção
							por planta) e dos fatores do componente ambiente do
							cultivo do cacau (manejo, pragas, doenças, chuva,
							temperatura do ar, vento) além de permitir a
							consulta destes dados mediante emissão de
							relatórios.
						</p>
						<p class="text-justify">
							É um projeto que surgiu na CEPLAC (Comissão
							Executiva do Plano da Lavoura Cacaueira) em parceria
							com a Universidade Estadual de Santa Cruz – UESC e
							tem por objetivos promover a valorização do setor da
							cacauicultura nacional e propiciar meios de mudanças
							de parâmetros do comportamento do agricultor,
							proporcionando dados para informações estatísticas e
							sendo fonte de pesquisas para a melhoria da
							agricultura cacaueira e por consequência do seu
							agricultor.
						</p>
					</div>

					<div class="col-lg-6 col-md-6 col-sm-12 order-md-1">
						<img
							src="/img/cacau.jpg"
							class="img-fluid"
							alt="Cacaueiro"
						/>
					</div>
				</div>
			</section>

			<section id="features-web" class="container pt-5 pb-5">
				<h2 class="text-center mb-2">
					Algumas Funcionalidades da PlataformaCacau
				</h2>
				<p class="text-center mb-5">
					Descubra algumas funcionalidades disponíveis na
					PlataformaCacau
				</p>
				<div class="row">
					<div class="col-lg-4 col-md-4 col-sm-12 single-feature">
						<div class="d-flex justify-content-center feature-icon">
							<i class="fas fa-map-marked-alt fa-1x"></i>
						</div>
						<div class="feature-content-text">
							<h3 class="text-center">Mapa em Tempo Real</h3>
							<p class="text-center">
								Tenha uma visão em tempo real de todos os pontos
								amostrais registrados.
							</p>
						</div>
					</div>

					<div class="col-lg-4 col-md-4 col-sm-12 single-feature">
						<div class="d-flex justify-content-center feature-icon">
							<i class="fas fa-globe fa-1x"></i>
						</div>
						<div class="feature-content-text">
							<h3 class="text-center">Registro de Estratos</h3>
							<p class="text-center">
								Registre os Estratos por rótulo e tenha um
								melhor controle.
							</p>
						</div>
					</div>

					<div class="col-lg-4 col-md-4 col-sm-12 single-feature">
						<div class="d-flex justify-content-center feature-icon">
							<i class="fas fa-map-marker-alt fa-1x"></i>
						</div>
						<div class="feature-content-text">
							<h3 class="text-center">
								Registro de Ponto Amostral
							</h3>
							<p class="text-center">
								Durante o registro de um Ponto Amostral, você
								tem a possibilidade de vincular a área a um
								Período Agrícola, Propriedade e muito mais.
							</p>
						</div>
					</div>

					<div class="col-lg-4 col-md-4 col-sm-12 single-feature">
						<div class="d-flex justify-content-center feature-icon">
							<i class="fas fa-home fa-1x"></i>
						</div>
						<div class="feature-content-text">
							<h3 class="text-center">
								Cadastro de Propriedades
							</h3>
							<p class="text-center">
								Cadastre e gerencie suas propriedades de forma
								simplificada e intuitiva.
							</p>
						</div>
					</div>

					<div class="col-lg-4 col-md-4 col-sm-12 single-feature">
						<div class="d-flex justify-content-center feature-icon">
							<i class="fas fa-users fa-1x"></i>
						</div>
						<div class="feature-content-text">
							<h3 class="text-center">Cadastro de Usuários</h3>
							<p class="text-center">
								A plataforma AgroCacau possui uma ferramenta de
								cadastro de usuário e controle de acesso, dando
								mais segurança aos seus dados.
							</p>
						</div>
					</div>

					<div class="col-lg-4 col-md-4 col-sm-12 single-feature">
						<div class="d-flex justify-content-center feature-icon">
							<i class="fas fa-clipboard 1x"></i>
						</div>
						<div class="feature-content-text">
							<h3 class="text-center">Gerar Relatórios</h3>
							<p class="text-center">
								Tenha acesso a relatórios em arquivo Excel ou
								PDF, a partir dos dados registrados na
								plataforma.
							</p>
						</div>
					</div>
				</div>
			</section>

			<section id="features-mobile" class="container pb-5">
				<div class="row justify-content-between align-items-center">
					<div class="col-12 col-lg-6 order-2 order-lg-1">
						<div class="mx-auto text-center mt-5 mt-lg-0">
							<img
								src="/img/mockup_mobile.png"
								width="200"
								class="img-fluid"
								alt="Mockup do app mobile"
							/>
						</div>
					</div>

					<div class="col-12 col-lg-6 order-1 order-lg-2">
						<div class="px-0 px-lg-4 pt-4 pt-lg-0">
							<h2 class="text-center pb-4">Aplicação Mobile</h2>
							<p class="text-justify">
								O nosso aplicativo mobile se conecta com a
								PlataformaCacau com a finalidade de receber
								dados do desenvolvimento de frutos de cacau em
								roças e enviar estes dados para o
								PlataformaCacau. Esses dados, posteriormente,
								podem ser usados na previsão de safras de cacau,
								alerta fitossanitário, etc.
							</p>
							<ul>
								<div>
									<li class="py-2">
										<div class="list-box media">
											<span
												class="icon align-self-center"
											>
												<i class="fas fa-check"></i>
											</span>
											<span class="media-body pl-2">
												Coleta de dados no contexto da
												interação planta, solo e clima.
											</span>
										</div>
									</li>
								</div>

								<div>
									<li class="py-2">
										<div class="list-box media">
											<span
												class="icon align-self-center"
											>
												<i class="fas fa-check"></i>
											</span>
											<span class="media-body pl-2">
												Validação da contagem de frutos
												da coleta.
											</span>
										</div>
									</li>
								</div>

								<div>
									<li class="py-2">
										<div class="list-box media">
											<span
												class="icon align-self-center"
											>
												<i class="fas fa-check"></i>
											</span>
											<span class="media-body pl-2">
												Matriz de Transição, onde se faz
												possível registrar o
												desenvolvimento do fruto ao
												longo do tempo.
											</span>
										</div>
									</li>
								</div>
							</ul>
						</div>
					</div>
				</div>
			</section>

			<section
				id="howitworks"
				class="container-fluid work-area bg-img py-5"
			>
				<div class="container">
					<div class="row justify-content-center pt-5">
						<div class="col-12 col-lg-6">
							<div class="work-content text-center">
								<h2 class="text-white mb-sm-5">
									Como a PlataformaCacau e o aplicativo se
									integram?
								</h2>
							</div>
						</div>
					</div>

					<div class="row">
						<div class="col-12 col-md-4">
							<div class="single-work text-center p-3">
								<div>
									<i
										class="fas fa-mobile-alt fa-3x text-white"
									></i>
								</div>
								<h3 class="text-white py-3">Coleta de dados</h3>
								<p class="text-white">
									O aplicativo, composto de um formulário
									matriz, é estruturado para coleta dos dados
									com base em amostragem estratificada. Os
									dados capturados compõem-se de diversas
									variáveis, como por exemplo, condições do
									tempo e nível de infecção dos frutos.
								</p>
							</div>
						</div>

						<div class="col-12 col-md-4">
							<div class="single-work text-center p-3">
								<div>
									<i
										class="fas fa-database text-white fa-3x"
									></i>
								</div>
								<h3 class="text-white py-3">
									Filtragem de Dados
								</h3>
								<p class="text-white">
									A partir da coleta, os dados passam por uma
									crítica, usando como regra um cálculo
									desenvolvido por nosso time de
									especialistas. Trazendo maior confiabilidade
									aos resultados que serão apresentados
									posteriormente.
								</p>
							</div>
						</div>

						<div class="col-12 col-md-4">
							<div class="single-work text-center p-3">
								<div>
									<i
										class="far fa-chart-bar text-white fa-3x"
									></i>
								</div>
								<h3 class="text-white py-3">Resultados</h3>
								<p class="text-white">
									Por fim, o aplicativo gera resultados
									organizados no painel do sistema web, que
									podem ser exportados no formato de arquivo
									desejado (como PDF ou Excel).
								</p>
							</div>
						</div>
					</div>
				</div>
			</section>

			<section id="team" class="container team">
				<div class="row justify-content-center">
					<div class="col-lg-7 col-md-8">
						<div class="section-heading text-center mb-4">
							<h2>Nossos Membros</h2>
							<p class="lead">
								Conheça as pessoas que fazem possível a
								plataforma PlataformaCacau ganhar vida.
							</p>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-lg-3 col-md-6 col-sm-6 mb-5">
						<div
							class="single-team-member position-relative my-lg-3 my-md-3 my-sm-0"
						>
							<div class="team-image">
								<img
									src="/img/team-2.png"
									alt="Membro do Time"
									class="img-fluid rounded"
								/>
							</div>
							<div
								class="team-info text-white rounded d-flex flex-column align-items-center justify-content-center px-2"
							>
								<h5 class="mb-0 py-1">Lindolfo Pereira</h5>
								<h6 class="text-white text-center">
									CEPLAC (Comissão Executiva do Plano da
									Lavoura Cacaueira)
								</h6>
							</div>
						</div>
					</div>

					<div class="col-lg-3 col-md-6 col-sm-6 mb-5">
						<div
							class="single-team-member position-relative my-lg-3 my-md-3 my-sm-0"
						>
							<div class="team-image">
								<img
									src="/img/team-2.png"
									alt="Membro do Time"
									class="img-fluid rounded"
								/>
							</div>
							<div
								class="team-info text-white rounded d-flex flex-column align-items-center justify-content-center px-2"
							>
								<h5 class="mb-0 py-1">Jauberth Abijaude</h5>
								<h6 class="text-white text-center">
									Departamento de Ciências Exatas e Tecnológicas (DEC)
								</h6>
							</div>
						</div>
					</div>

					<div class="col-lg-3 col-md-6 col-sm-6 mb-5">
						<div
							class="single-team-member position-relative my-lg-3 my-md-3 my-sm-0"
						>
							<div class="team-image">
								<img
									src="/img/team-2.png"
									alt="Membro do Time"
									class="img-fluid rounded"
								/>
							</div>
							<div
								class="team-info text-white rounded d-flex flex-column align-items-center justify-content-center"
							>
								<h5 class="mb-0 py-1">Christian Menezes</h5>
								<h6 class="text-white text-center px-2">
									Aluno do curso de Ciência da Computação da
									UESC
								</h6>
								<ul
									class="list-inline team-social my-4 text-white"
								>
									<li class="list-inline-item">
										<a href="#!">
											<i class="fab fa-linkedin-in"></i>
										</a>
									</li>
									<li class="list-inline-item">
										<a href="#!">
											<i class="fab fa-github"></i>
										</a>
									</li>
								</ul>
							</div>
						</div>
					</div>

					<div class="col-lg-3 col-md-6 col-sm-6 mb-5">
						<div
							class="single-team-member position-relative my-lg-3 my-md-3 my-sm-0"
						>
							<div class="team-image">
								<img
									src="/img/team-1.png"
									alt="Membro do Time"
									class="img-fluid rounded"
								/>
							</div>
							<div
								class="team-info text-white rounded d-flex flex-column align-items-center justify-content-center"
							>
								<h5 class="mb-0 py-1">Marta Magda Dornelles</h5>
								<h6 class="text-white text-center">
									Departamento de Ciências Exatas e Tecnológicas (DEC)
								</h6>
							</div>
						</div>
					</div>

					<div class="col-lg-3 col-md-6 col-sm-6 mb-5">
						<div
							class="single-team-member position-relative my-lg-3 my-md-3 my-sm-0"
						>
							<div class="team-image">
								<img
									src="/img/team-2.png"
									alt="Membro do Time"
									class="img-fluid rounded"
								/>
							</div>
							<div
								class="team-info text-white rounded d-flex flex-column align-items-center justify-content-center"
							>
								<h5 class="mb-0 py-1">Adriel Fabrício</h5>
								<h6 class="text-white text-center px-2">
									Aluno do curso de Ciência da Computação da
									UESC
								</h6>
								<ul
									class="list-inline team-social my-4 text-white"
								>
									<li class="list-inline-item">
										<a href="#!">
											<i class="fab fa-linkedin-in"></i>
										</a>
									</li>
									<li class="list-inline-item">
										<a href="#!">
											<i class="fab fa-github"></i>
										</a>
									</li>
								</ul>
							</div>
						</div>
					</div>

					<div class="col-lg-3 col-md-6 col-sm-6 mb-5">
						<div
							class="single-team-member position-relative my-lg-3 my-md-3 my-sm-0"
						>
							<div class="team-image">
								<img
									src="/img/team-2.png"
									alt="Membro do Time"
									class="img-fluid rounded"
								/>
							</div>
							<div
								class="team-info text-white rounded d-flex flex-column align-items-center justify-content-center"
							>
								<h5 class="mb-0 py-1">Henrique Serra</h5>
								<h6 class="text-white text-center px-2">
									Aluno do curso de Ciência da Computação da
									UESC
								</h6>
								<ul
									class="list-inline team-social my-4 text-white"
								>
									<li class="list-inline-item">
										<a href="#!">
											<i class="fab fa-linkedin-in"></i>
										</a>
									</li>
									<li class="list-inline-item">
										<a href="#!">
											<i class="fab fa-github"></i>
										</a>
									</li>
								</ul>
							</div>
						</div>
					</div>

					<div class="col-lg-3 col-md-6 col-sm-6 mb-5">
						<div
							class="single-team-member position-relative my-lg-3 my-md-3 my-sm-0"
						>
							<div class="team-image">
								<img
									src="/img/team-2.png"
									alt="Membro do Time"
									class="img-fluid rounded"
								/>
							</div>
							<div
								class="team-info text-white rounded d-flex flex-column align-items-center justify-content-center"
							>
								<h5 class="mb-0 py-1">Lucas Ferreira</h5>
								<h6 class="text-white text-center px-2">
									Aluno do curso de Ciência da Computação da
									UESC
								</h6>
								<ul
									class="list-inline team-social my-4 text-white"
								>
									<li class="list-inline-item">
										<a href="#!">
											<i class="fab fa-linkedin-in"></i>
										</a>
									</li>
									<li class="list-inline-item">
										<a href="#!">
											<i class="fab fa-github"></i>
										</a>
									</li>
								</ul>
							</div>
						</div>
					</div>
					<div class="col-lg-3 col-md-6 col-sm-6 mb-5">
						<div
							class="single-team-member position-relative my-lg-3 my-md-3 my-sm-0"
						>
							<div class="team-image">
								<img
									src="/img/team-1.png"
									alt="Membro do Time"
									class="img-fluid rounded"
								/>
							</div>
							<div
								class="team-info text-white rounded d-flex flex-column align-items-center justify-content-center"
							>
								<h5 class="mb-0 py-1">Maria Clara Simões</h5>
								<h6 class="text-white text-center">
									Aluna do curso de Ciência da Computação da
									UESC
								</h6>
								<ul
									class="list-inline team-social my-4 text-white"
								>
									<li class="list-inline-item">
										<a href="#!">
											<i class="fab fa-linkedin-in"></i>
										</a>
									</li>
									<li class="list-inline-item">
										<a href="#!">
											<i class="fab fa-github"></i>
										</a>
									</li>
								</ul>
							</div>
						</div>
					</div>					
				</div>
			</section>

			<section id="contact" class="container contact-area mb-5">
				<div class="row justify-content-center">
					<div class="col-12 col-md-10 col-lg-6">
						<div class="section-heading text-center">
							<h2 class="text-capitalize">Contato</h2>
							<p class="d-none d-sm-block mt-4">
								Nos contate para saber mais sobre o sistema ou
								para pedir ajuda.
							</p>
							<p class="d-block d-sm-none mt-4">
								Nos contate para saber mais sobre o sistema ou
								para pedir ajuda.
							</p>
						</div>
					</div>
				</div>
				<div class="row justify-content-center">
					<div class="col-12 col-md-5">
						<div class="contact-us">
							<p class="mb-3">
								Preencha o formulário a seguir e o nosso time
								entrará em contato dentro de 24 horas. Se
								preferir, você pode nos contatar também através
								dos meios listados abaixo:
							</p>
							<ul>
								<!-- <li class="py-2">
									<a href="#!" class="media">
										<div class="mr-3 social-icon">
											<i class="fas fa-phone"></i>
										</div>
										<span class="media-body align-self-center">
											****** 456 789-************
										</span>
									</a>
								</li> -->
								<li class="py-2">
									<a href="#!" class="media">
										<div class="mr-3 social-icon">
											<i class="fas fa-envelope"></i>
										</div>
										<a
											class="media-body align-self-center"
											href="mailto:<EMAIL>"
										>
											<EMAIL>
										</a>
									</a>
								</li>
							</ul>
						</div>
					</div>
					<div class="col-12 col-md-6 pt-4 pt-md-0">
						<div class="contact-box text-center">
							<form
								class="contact-form"
								@submit.prevent="sendMail()"
							>
								<div class="row">
									<div class="col-12">
										<div class="form-group">
											<input
												type="text"
												class="form-control"
												placeholder="Nome"
												required
												v-model="formData.name"
											/>
										</div>
										<div class="form-group">
											<input
												type="email"
												class="form-control"
												placeholder="Email"
												required
												v-model="formData.email"
											/>
										</div>
										<div class="form-group">
											<input
												type="text"
												class="form-control"
												placeholder="Assunto"
												required
												v-model="formData.subject"
											/>
										</div>
										<div class="form-group">
											<input
												type="text"
												class="form-control"
												placeholder="Telefone"
												required
												v-model="formData.phone"
											/>
										</div>
									</div>
									<div class="col-12">
										<div class="form-group">
											<textarea
												class="form-control"
												cols="30"
												rows="10"
												placeholder="Mensagem"
												v-model="formData.message"
											></textarea>
										</div>
									</div>
									<div class="col-12">
										<button
											type="submit"
											class="btn btn-lg btn-block mt-3"
											:disabled="loading"
										>
											<span class="text-white pr-3">
												<i
													class="far fa-paper-plane"
												></i>
											</span>
											Enviar Mensagem
										</button>
									</div>
								</div>
							</form>
						</div>
					</div>
				</div>
			</section>
		</div>
	</div>
</template>

<script>
export default {
	data() {
		return {
			formData: {
				name: "",
				email: "",
				subject: "",
				phone: "",
				message: "",
			},
			loading: false,
		};
	},

	methods: {
		sendMail() {
			this.loading = true;
			axios
				.post("/api/v1/contact", this.formData)
				.then((response) => {
					Swal.fire({
						icon: "success",
						title: "Mensagem enviada com sucesso",
						text: "Entraremos em contato dentro das próximas 24h. Fique atento à sua caixa de email!",
						confirmButtonText: "Certo",
					});
					this.clearForm();
					this.loading = false;
				})
				.catch((err) => {
					console.log(err);
					Swal.fire({
						title: "Erro no envio da mensagem",
						text: "Ocorreu um problema durante o envio da mensagem. Tente novamente mais tarde!",
						icon: "error",
						confirmButtonText: "Fechar",
					});
					this.loading = false;
				});
		},

		clearForm() {
			this.formData.name = "";
			this.formData.email = "";
			this.formData.subject = "";
			this.formData.phone = "";
			this.formData.message = "";
		},
	},
};
</script>

<style lang="scss" scoped>
ul {
	margin: 0;
	padding: 0;
}

ol li,
ul li {
	list-style: none;
}

p {
	font-family: "Poppins", sans-serif;
	font-size: 14px;
}

.position-relative {
	position: relative !important;
}

.list-inline {
	padding-left: 0;
	list-style: none;
}

.list-inline-item {
	display: inline-block !important;
}

.section-heading {
	margin-bottom: 80px;
}

.media {
	display: -ms-flexbox;
	display: flex;
	-ms-flex-align: start;
	align-items: flex-start;
}

.media-body {
	-ms-flex: 1;
	flex: 1;
}

.site-content {
	overflow: hidden;
	background-color: #fff;
}

section {
	// margin-bottom: 1.5%;
	background-color: #fff;

	.lead {
		font-size: 16px;
		font-weight: 400;
	}
}

// Banner
.banner {
	padding: 12em 0;

	h1 {
		color: #135d2a;
		font-size: 56px;
		font-weight: bold;
	}

	.shapes-container {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		overflow-x: hidden !important;

		.bg-shape {
			position: absolute;
			height: 190%;
			width: 100%;
			display: block;
			border-radius: 120px;
			background: linear-gradient(-47deg, #25661a 0%, #135d2a 100%);
			bottom: 0;
			right: 0;
			-webkit-transform: translate(35%, -28%) rotate(-35deg);
			transform: translate(35%, -28%) rotate(-35deg);
			z-index: 0;
		}
	}
}

@media (min-width: 320px) and (max-width: 414px) {
	.banner {
		padding: 12em 0 0 0;

		.shapes-container {
			.bg-shape {
				width: 180% !important;
			}
		}
	}
}

.about .texto {
	-webkit-box-shadow: 0px 6px 10px 0px rgba(0, 0, 0, 0.16);
	-moz-box-shadow: 0px 6px 10px 0px rgba(0, 0, 0, 0.16);
	box-shadow: 0px 6px 10px 0px rgba(0, 0, 0, 0.16);
}

// Features web
.single-feature {
	-webkit-transition: 0.5s;
	-o-transition: 0.5s;
	transition: 0.5s;
	margin-bottom: 50px;
	position: relative;
	z-index: 1;
	background-color: #fff;
	padding: 40px;
	border: 1px solid #ebebeb;
	-webkit-box-shadow: 0px 6px 10px 0px rgba(0, 0, 0, 0.02);
	box-shadow: 0px 6px 10px 0px rgba(0, 0, 0, 0.02);
	border-radius: 5px;
}

.single-feature::before {
	content: "";
	width: 100%;
	height: 3px;
	position: absolute;
	left: 0;
	bottom: 0;
	z-index: -1;
	border-radius: 5px;
	-webkit-transition: 0.5s;
	-o-transition: 0.5s;
	transition: 0.5s;
	background: #135d2a;
}

.single-feature .feature-icon {
	margin-bottom: 30px;
	font-size: 50px;
	line-height: 50px;
	color: #135d2a;
	-webkit-transition: 0.5s;
	-o-transition: 0.5s;
	transition: 0.5s;
}

.single-feature:hover .feature-icon {
	font-family: "Roboto" !important;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	color: #fff !important;
}

.feature-content-text p {
	-webkit-transition: 0.5s;
	-o-transition: 0.5s;
	transition: 0.5s;
}

.feature-content-text p {
	position: relative;
	top: 30px;
	transition: 0.5s;
}

.feature-content-text h3 {
	-webkit-transition: 0.5s;
	-o-transition: 0.5s;
	transition: 0.5s;
}

.single-feature:hover .feature-content-text h3,
.single-feature:hover .feature-content-text p {
	color: #fff;
}

.single-feature:hover::before {
	height: 100%;
}

// Mobile Features
.list-box .icon {
	color: #135d2a;
}

// How it works area
.work-area {
	margin: 100px 0px;

	h2 {
		font-size: 36px;
		font-weight: bold;
	}

	.single-work {
		position: relative;
	}

	.single-work::after {
		position: absolute;
		content: "\f054";
		font-size: 45px;
		font-weight: 900;
		color: white;
		font-family: "Font Awesome 5 Free";
		top: 50%;
		right: 0;
		margin-right: -30px;
		-webkit-transform: translateY(-50%);
		transform: translateY(-50%);
		-webkit-transition: transform 0.3s;
		transition: transform 0.3s;
	}

	.col-12:last-of-type .single-work::after {
		display: none;
	}

	.single-work:hover::after {
		-webkit-transform: translateX(5px) translateY(-50%);
		transform: translateX(5px) translateY(-50%);
	}

	@media (max-width: 767px) {
		.single-work::after {
			display: none;
		}
	}
}

.bg-img {
	background: linear-gradient(223deg, #318023, #135d2a);
}

// Envolvidos
.single-team-member .team-info {
	background-color: #135d2a;
	opacity: 0.9 !important;
	visibility: hidden;
	-webkit-transition: opacity 0.3s, visibility 0.3s;
	transition: opacity 0.3s, visibility 0.3s;
}

.team-image img {
	display: block;
	height: 100%;
	width: 100%;
	overflow: hidden;
}

.team-info {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.team-info h5 {
	-webkit-transition-delay: 0.2s;
	transition-delay: 0.2s;
	color: inherit;
}

.team-info h6 {
	-webkit-transition-delay: 0.1s;
	transition-delay: 0.1s;
}

.team-info h5,
.team-info h6,
.team-info .team-social {
	-webkit-transform: translateY(25px);
	transform: translateY(25px);
	opacity: 0;
	-webkit-transition: opacity 0.45s cubic-bezier(0.19, 1, 0.22, 1),
		-webkit-transform 0.45s cubic-bezier(0.19, 1, 0.22, 1);
	transition: opacity 0.45s cubic-bezier(0.19, 1, 0.22, 1),
		-webkit-transform 0.45s cubic-bezier(0.19, 1, 0.22, 1);
	transition: transform 0.45s cubic-bezier(0.19, 1, 0.22, 1),
		opacity 0.45s cubic-bezier(0.19, 1, 0.22, 1);
	transition: transform 0.45s cubic-bezier(0.19, 1, 0.22, 1),
		opacity 0.45s cubic-bezier(0.19, 1, 0.22, 1),
		-webkit-transform 0.45s cubic-bezier(0.19, 1, 0.22, 1);
}

.single-team-member:hover .team-info {
	opacity: 1;
	visibility: visible;
}

.single-team-member:hover .team-info h6 {
	opacity: 1;
}

.single-team-member:hover .team-info h5,
.single-team-member:hover .team-info h6,
.single-team-member:hover .team-info .team-social {
	opacity: 1;
	-webkit-transform: translateY(0);
	transform: translateY(0);
}

.team-social li a {
	text-decoration: none;
	color: inherit;
	opacity: 0.8;
}

.team-social li a:hover {
	opacity: 1;
}

.team-social li a i {
	font-size: 20px;
	padding: 6px;
}

// Contato
.contact-us .social-icon {
	display: inline-block;
	height: 45px;
	width: 45px;
	line-height: 45px;
	font-size: 18px;
	text-align: center;
	background-color: #135d2a;
	color: #fff;
	border-radius: 4px;
}

.contact-area {
	left: 0;
	transform: inherit;
}

.contact-box .form-group input,
.contact-box .form-group textarea {
	font-size: 14px;
	height: 50px;
	padding: 0 15px;
	border: none;
	-webkit-box-shadow: none;
	box-shadow: none;
	border-radius: 0;
	-webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
	box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
	-webkit-transition: 0.5s;
	transition: 0.5s;
}

.contact-box .form-group textarea {
	height: 160px;
	padding: 15px;
}

.contact-box .form-group input::-webkit-input-placeholder,
.contact-box .form-group textarea::-webkit-input-placeholder {
	color: rgba(68, 68, 68, 0.6);
}

.contact-box .form-group input:-ms-input-placeholder,
.contact-box .form-group textarea:-ms-input-placeholder {
	color: rgba(68, 68, 68, 0.6);
}

.contact-box .form-group input::placeholder,
.contact-box .form-group textarea::placeholder {
	color: rgba(68, 68, 68, 0.6);
}

.contact-box button {
	color: white;
	height: 60px;
	border-radius: 8px;
	background: #135d2a !important;
}

.contact-box button:hover {
	background: #0f421f !important;
}
</style>
