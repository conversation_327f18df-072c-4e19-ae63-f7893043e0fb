<template>
	<div class="signup-wrapper">
		<div class="container-fluid">
			<div class="row min-vh-100">
				<!-- Se<PERSON> Esquerda - Logo -->
				<div class="col-lg-6 info-section d-flex align-items-center justify-content-center">
					<div class="logo-content text-center">
						<div class="logo-icon">
							<img
								class="img-fluid logo-image"
								:src="'/img/agrocacau_logo.png'"
								alt="Logo PlataformaCacau"
							/>
						</div>
						<h1 class="platform-title">PlataformaCacau</h1>
						<p class="platform-subtitle">Tecnologia e inovação para a cacauicultura brasileira</p>
					</div>
				</div>

				<!-- Seção Direita - Formulário -->
				<div class="col-lg-6 form-section d-flex align-items-center">
					<div class="form-container">
						<div class="form-header text-center mb-4">
							<h2><PERSON><PERSON><PERSON></h2>
							<p>Preencha os dados abaixo para começar</p>
						</div>

						<form @submit.prevent="signup" class="signup-form">
							<!-- Informações Pessoais -->
							<div class="form-group-section">
								<h3 class="section-title">
									<i class="fas fa-user"></i>
									Informações Pessoais
								</h3>

								<div class="row">
									<div class="col-12 mb-3">
										<label for="name" class="form-label">Nome Completo</label>
										<input
											type="text"
											class="form-control"
											id="name"
											v-model="name"
											placeholder="Digite seu nome completo"
											:class="{
												'is-invalid': (!name || !checkName(name)) && formErrors.name,
												'is-valid': checkName(name) && name
											}"
											required
										/>
										<div class="invalid-feedback" v-if="formErrors.name">
											{{ formErrors.name }}
										</div>
									</div>

									<div class="col-12 mb-3">
										<label for="phone" class="form-label">Telefone</label>
										<input
											type="tel"
											v-mask="['(##) ####-####', '(##) #####-####']"
											class="form-control"
											id="phone"
											v-model="phone"
											placeholder="(00) 90000-0000"
											:class="{
												'is-invalid': (!phone || !checkPhone(phone)) && formErrors.phone,
												'is-valid': checkPhone(phone) && phone
											}"
											required
										/>
										<div class="invalid-feedback" v-if="formErrors.phone">
											{{ formErrors.phone }}
										</div>
									</div>
								</div>
							</div>

							<!-- Informações de Acesso -->
							<div class="form-group-section">
								<h3 class="section-title">
									<i class="fas fa-lock"></i>
									Informações de Acesso
								</h3>

								<div class="row">
									<div class="col-12 mb-3">
										<label for="email" class="form-label">Email</label>
										<input
											type="email"
											class="form-control"
											id="email"
											v-model="email"
											placeholder="<EMAIL>"
											:class="{
												'is-invalid': (!email || !checkEmail(email)) && formErrors.email,
												'is-valid': checkEmail(email) && email
											}"
											required
										/>
										<div class="invalid-feedback" v-if="formErrors.email">
											{{ formErrors.email }}
										</div>
									</div>

									<div class="col-12 mb-3">
										<label for="password" class="form-label">Senha</label>
										<input
											type="password"
											class="form-control"
											id="password"
											v-model="password"
											placeholder="Mínimo 8 caracteres"
											:class="{
												'is-invalid': (!password || !checkPassword(password)) && formErrors.password,
												'is-valid': checkPassword(password) && password
											}"
											required
										/>
										<div class="invalid-feedback" v-if="formErrors.password">
											{{ formErrors.password }}
										</div>
										<small class="form-text text-muted">
											Deve conter: 8+ caracteres, 1 maiúscula, 1 minúscula e 1 caractere especial
										</small>
									</div>
								</div>
							</div>

							<!-- Termos e Condições -->
							<div class="form-group-section">
								<h3 class="section-title">
									<i class="fas fa-shield-alt"></i>
									Termos e Condições
								</h3>

								<div class="terms-container">
									<div class="form-check mb-3">
										<input
											type="checkbox"
											class="form-check-input"
											id="termsAndConditions"
											v-model="isTermsAccepted"
											required
										/>
										<label class="form-check-label" for="termsAndConditions">
											Eu concordo com os <a href="#" class="terms-link">Termos e Condições</a>
										</label>
									</div>

									<div class="form-check mb-4">
										<input
											type="checkbox"
											class="form-check-input"
											id="privacyPolicy"
											v-model="isPoliticsAccepted"
											required
										/>
										<label class="form-check-label" for="privacyPolicy">
											Eu concordo com as <a href="#" class="terms-link">Políticas de Privacidade</a>
										</label>
									</div>
								</div>
							</div>

							<!-- Botões -->
							<div class="form-actions">
								<button
									type="submit"
									class="btn btn-primary btn-signup"
									:disabled="!canSubmit || isSubmitting"
								>
									<span v-if="isSubmitting">
										<i class="fas fa-spinner fa-spin me-2"></i>
										Criando conta...
									</span>
									<span v-else>
										<i class="fas fa-user-plus me-2"></i>
										Criar Conta
									</span>
								</button>

								<div class="login-link text-center mt-3">
									<p>Já tem uma conta? <router-link to="/login" class="link-primary">Fazer login</router-link></p>
								</div>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import axios from 'axios';
import Swal from 'sweetalert2';

export default {
	data() {
		return {
			// Dados do formulário
			name: '',
			phone: '',
			email: '',
			password: '',

			// Termos
			isTermsAccepted: false,
			isPoliticsAccepted: false,

			// Estados
			isSubmitting: false,
			formErrors: {}
		};
	},

	computed: {
		canSubmit() {
			return (
				this.checkName(this.name) &&
				this.checkPhone(this.phone) &&
				this.checkEmail(this.email) &&
				this.checkPassword(this.password) &&
				this.isTermsAccepted &&
				this.isPoliticsAccepted
			);
		}
	},

	methods: {
		checkName(name) {
			if (!name) return false;
			if (!/^[a-zA-ZÀ-ÿ\s]+$/.test(name)) return false;
			if (name.trim() !== name) return false;
			return name.trim().length >= 2;
		},

		checkPhone(phone) {
			if (!phone) return false;
			const clearPhone = phone.replace(/\D/g, "");
			if (clearPhone.length !== 11) return false;
			if (clearPhone.charAt(2) !== "9") return false;
			return true;
		},

		checkEmail(email) {
			const regexEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
			return regexEmail.test(email);
		},

		checkPassword(password) {
			if (!password) return false;
			return (
				password.length >= 8 &&
				/[A-Z]/.test(password) &&
				/[a-z]/.test(password) &&
				/[!@#$%^&*()_+{}\[\]:;<>,.?~\\-]/.test(password)
			);
		},

		validateForm() {
			this.formErrors = {};

			if (!this.checkName(this.name)) {
				this.formErrors.name = "Nome deve ter pelo menos 2 caracteres e conter apenas letras";
			}

			if (!this.checkPhone(this.phone)) {
				this.formErrors.phone = "Telefone deve estar no formato (00) 90000-0000";
			}

			if (!this.checkEmail(this.email)) {
				this.formErrors.email = "Email deve ter um formato válido";
			}

			if (!this.checkPassword(this.password)) {
				this.formErrors.password = "Senha deve ter 8+ caracteres, 1 maiúscula, 1 minúscula e 1 caractere especial";
			}

			return Object.keys(this.formErrors).length === 0;
		},

		async signup() {
			if (!this.validateForm() || !this.canSubmit) {
				return;
			}

			this.isSubmitting = true;

			try {
				const response = await axios.post("/signup", {
					name: this.name,
					password: this.password,
					email: this.email,
					phone: this.phone,
				});

				if (response.status === 200) {
					Swal.fire({
						icon: "success",
						title: "Cadastro realizado com sucesso!",
						text: "Você será redirecionado para a tela de login.",
						showConfirmButton: false,
						timer: 2000
					});

					setTimeout(() => {
						this.$router.push('/login');
					}, 2000);
				}
			} catch (error) {
				console.error('Erro no cadastro:', error);

				if (error.response?.data?.message) {
					let errorMessage = '';

					if (Array.isArray(error.response.data.message)) {
						const errList = error.response.data.message[0];
						errorMessage = Object.values(errList).join('<br>');
					} else {
						errorMessage = error.response.data.message;
					}

					Swal.fire({
						title: "Erro no cadastro",
						html: errorMessage,
						icon: "error",
						confirmButtonText: "Tentar novamente"
					});
				} else {
					Swal.fire({
						icon: "error",
						title: "Erro inesperado",
						text: "Ocorreu um erro ao criar sua conta. Tente novamente.",
						confirmButtonText: "OK"
					});
				}
			} finally {
				this.isSubmitting = false;
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.signup-wrapper {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	font-family: "Lexend", sans-serif;
}

.info-section {
	background: linear-gradient(135deg, #3d8160 0%, #135D2A 100%);
	color: white;
	padding: 2rem;
}

.logo-content {
	max-width: 400px;

	.logo-icon {
		width: 120px;
		height: 120px;
		background: rgba(255, 255, 255, 0.2);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 auto 2rem;
		backdrop-filter: blur(10px);
		border: 3px solid rgba(255, 255, 255, 0.3);
		padding: 15px;

		.logo-image {
			max-width: 100%;
			max-height: 100%;
			object-fit: contain;
		}

		i {
			font-size: 3.5rem;
			color: white;
		}
	}

	.platform-title {
		font-size: 3.5rem;
		font-weight: 800;
		margin-bottom: 1rem;
		color: white;
		letter-spacing: -0.02em;
		line-height: 1.1;
	}

	.platform-subtitle {
		font-size: 1.3rem;
		opacity: 0.95;
		margin-bottom: 2rem;
		line-height: 1.4;
		font-weight: 300;
		color: white;
	}

	.platform-badge {
		display: inline-block;
		background: rgba(255, 255, 255, 0.2);
		padding: 0.75rem 1.5rem;
		border-radius: 25px;
		border: 1px solid rgba(255, 255, 255, 0.3);
		backdrop-filter: blur(10px);

		span {
			font-size: 0.9rem;
			font-weight: 600;
			color: white;
			text-transform: uppercase;
			letter-spacing: 0.5px;
		}
	}
}



.form-section {
	background: white;
	padding: 2rem;
	display: flex;
	align-items: center;

	.form-container {
		width: 100%;
		max-width: 500px;
		margin: 0 auto;
	}
}

.form-header {
	h2 {
		color: #135D2A;
		font-size: 2rem;
		font-weight: 700;
		margin-bottom: 0.5rem;
	}

	p {
		color: #6c757d;
		font-size: 1rem;
		margin-bottom: 0;
	}
}

.form-group-section {
	background: #f8f9fa;
	border-radius: 15px;
	padding: 1.5rem;
	margin-bottom: 1.5rem;
	border: 1px solid #e9ecef;

	.section-title {
		color: #135D2A;
		font-size: 1.1rem;
		font-weight: 600;
		margin-bottom: 1rem;
		display: flex;
		align-items: center;
		gap: 0.5rem;

		i {
			color: #3d8160;
		}
	}
}

.form-label {
	color: #495057;
	font-weight: 500;
	margin-bottom: 0.5rem;
	font-size: 0.9rem;
}

.form-control {
	border: 2px solid #e9ecef;
	border-radius: 10px;
	padding: 0.75rem 1rem;
	font-size: 1rem;
	transition: all 0.3s ease;

	&:focus {
		border-color: #3d8160;
		box-shadow: 0 0 0 0.2rem rgba(61, 129, 96, 0.25);
	}

	&.is-valid {
		border-color: #28a745;

		&:focus {
			border-color: #28a745;
			box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
		}
	}

	&.is-invalid {
		border-color: #dc3545;

		&:focus {
			border-color: #dc3545;
			box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
		}
	}
}

.terms-container {
	.form-check {
		.form-check-input {
			margin-top: 0.25rem;

			&:checked {
				background-color: #3d8160;
				border-color: #3d8160;
			}

			&:focus {
				box-shadow: 0 0 0 0.2rem rgba(61, 129, 96, 0.25);
			}
		}

		.form-check-label {
			font-size: 0.9rem;
			color: #495057;
			line-height: 1.4;
		}
	}

	.terms-link {
		color: #3d8160;
		text-decoration: none;
		font-weight: 500;

		&:hover {
			color: #135D2A;
			text-decoration: underline;
		}
	}
}

.form-actions {
	text-align: center;

	.btn-signup {
		background: linear-gradient(135deg, #3d8160 0%, #135D2A 100%);
		border: none;
		color: white;
		padding: 0.75rem 2rem;
		font-size: 1.1rem;
		font-weight: 600;
		border-radius: 10px;
		transition: all 0.3s ease;
		min-width: 200px;

		&:hover:not(:disabled) {
			background: linear-gradient(135deg, #135D2A 0%, #0d4221 100%);
			transform: translateY(-2px);
			box-shadow: 0 8px 25px rgba(61, 129, 96, 0.3);
		}

		&:disabled {
			opacity: 0.6;
			cursor: not-allowed;
			transform: none;
		}
	}

	.login-link {
		margin-top: 1rem;

		p {
			color: #6c757d;
			margin-bottom: 0;
		}

		.link-primary {
			color: #3d8160;
			text-decoration: none;
			font-weight: 500;

			&:hover {
				color: #135D2A;
				text-decoration: underline;
			}
		}
	}
}

// Responsividade
@media (max-width: 991.98px) {
	.info-section {
		padding: 2rem 1rem;

		.logo-content {
			.logo-icon {
				width: 80px;
				height: 80px;
				margin-bottom: 1rem;

				i {
					font-size: 2.5rem;
				}
			}

			.platform-title {
				font-size: 2.5rem;
				margin-bottom: 0.5rem;
			}

			.platform-subtitle {
				font-size: 1.1rem;
				margin-bottom: 1rem;
			}

			.platform-badge {
				padding: 0.5rem 1rem;

				span {
					font-size: 0.8rem;
				}
			}
		}
	}

	.form-section {
		padding: 1rem;
	}

	.form-container {
		max-width: 100%;
	}
}

@media (max-width: 576px) {
	.info-section {
		padding: 1rem;
		min-height: auto;

		.logo-content {
			.logo-icon {
				width: 60px;
				height: 60px;

				i {
					font-size: 2rem;
				}
			}

			.platform-title {
				font-size: 2rem;
			}

			.platform-subtitle {
				font-size: 1rem;
			}
		}
	}

	.form-group-section {
		padding: 1rem;
	}

	.form-header h2 {
		font-size: 1.5rem;
	}

	.btn-signup {
		width: 100%;
		min-width: auto;
	}
}
</style>
