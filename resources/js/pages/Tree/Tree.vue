<template>
	<div class="tree-container">
		<!-- Header Principal -->
		<div class="page-header">
			<div class="header-content">
				<div class="header-navigation">
					<back-button
						title="Voltar para pontos amostrais"
					></back-button>
				</div>
				<div class="header-icon">
					<i class="fas fa-tree"></i>
				</div>
				<div class="header-text">
					<h1 class="page-title">Árvor<PERSON></h1>
					<p class="page-subtitle">
						{{ stratum.homogeneous_area?.property?.name ?
							`${stratum.homogeneous_area.property.name} / Área Homogênea ${stratum.homogeneous_area.label} / Unidade Operacional ${stratum.label} / Ponto Amostral ${this.splabel}` :
							'<PERSON><PERSON><PERSON><PERSON> as árvores do ponto amostral' }}
					</p>
				</div>
				<div class="header-actions" v-show="active_trees < 7">
					<button
						type="button"
						class="btn-primary"
						@click.prevent="store()"
					>
						<i class="fas fa-plus"></i>
						<span>Ad<PERSON><PERSON><PERSON></span>
					</button>
				</div>
			</div>
		</div>

		<!-- Conteúdo Principal -->
		<div class="main-content">
			<!-- Loading State -->
			<div v-if="loading" class="loading-section">
				<div class="loading-card">
					<loader :loading="loading"></loader>
					<p>Carregando árvores...</p>
				</div>
			</div>

			<!-- Trees Table - Mantendo estrutura original -->
			<div v-else class="trees-table-modern">
				<vue-good-table
					title="Árvores"
					:columns="fields"
					:rows="trees"
					:pagination-options="paginationOptions"
					:fixed-header="true"
					:row-style-class="rowStyleClassFn"
					:search-options="{
						enabled: true,
						placeholder: 'Pesquisar...',
					}"
					:sort-options="{
						enabled: true,
						initialSortBy: [
							{ field: 'status', type: 'desc' },
							{ field: 'label', type: 'asc' },
						],
					}"
					compactMode
				>
					<div slot="emptystate">
						Não há registros para esta pesquisa
					</div>
					<template slot="table-row" slot-scope="props">
						<div v-if="props.column.field === 'label'">
							{{ props.row.label }}
						</div>
						<div v-if="props.column.field === 'rfid.code'">
							{{ props.row.rfid ? props.row.rfid.code : "N/A" }}
						</div>
						<div
							v-if="props.column.field === 'status'"
							class="custom-control custom-switch"
						>
							<div v-if="props.row.status">
								<input
									type="checkbox"
									class="custom-control-input"
									v-bind:id="`status_${props.row.id}`"
									v-model="props.row.status"
									@click.prevent="deactivate(props.row.id)"
									:disabled="!props.row.status"
								/>
								<label
									class="custom-control-label"
									v-bind:for="`status_${props.row.id}`"
									>{{ "Desativar" }}</label
								>
							</div>
							<div v-else>
								{{
									`Árvore desativada em ${convertDate(
										props.row.updated_at
									)}`
								}}
							</div>
						</div>

						<div v-if="props.column.field == 'actions'">
							<div class="d-flex" style="gap: 0 8px">
								<router-link
									tag="button"
									class="btn btn-success"
									v-bind:to="{
										path: '/panel/tree-visits',
										query: {
											trid: props.row.id,
											splabel: splabel,
											stlabel: stratum.label,
											pname: stratum.homogeneous_area.property.name,
											hal: stratum.homogeneous_area.label,
										},
									}"
									>Coletas</router-link
								>

								<button
									title="Editar árvore"
									class="btn btn-md btn-info"
									@click="editModal(props.row)"
									:disabled="props.row.status === 0"
								>
									<i class="fas fa-edit fa-sm"></i>
								</button>
							</div>
						</div>
					</template>
				</vue-good-table>
			</div>
		</div>

		<!-- Modais mantidos exatamente como estão -->
		<div
			class="modal fade"
			id="modalTrees"
				ref="modal"
				tabindex="-1"
				role="dialog"
				aria-labelledby="modalTreesLabel"
				aria-hidden="true"
			>
				<div class="modal-dialog modal-dialog-centered" role="document">
					<div class="modal-content">
						<div class="modal-header">
							<h5
								class="modal-title"
								id="modalTreesLabel"
								v-show="editMode"
							>
								ATUALIZAR ÁRVORE
							</h5>
							<button
								type="button"
								class="close"
								data-dismiss="modal"
								aria-label="Close"
							>
								<span aria-hidden="true">&times;</span>
							</button>
						</div>
						<div class="modal-body">
							<form @submit.prevent="update()">
								<div class="form-row">
									<div class="form-group col-md-12">
										<label for="treeLabel">
											Label da árvore
										</label>
										<input
											id="treeLabel"
											type="text"
											class="form-control"
											v-model="tree.label"
											disabled
										/>
									</div>
									<div class="form-group col-md-12">
										<label for="alternativeLabel">
											Label alternativa
										</label>
										<input
											id="alternativeLabel"
											type="text"
											class="form-control"
											v-model="tree.alternative_label"
										/>
									</div>
									<div class="form-group col-md-12">
										<label for="treeRfid"> RFID </label>
										<select
											id="treeRfid"
											type="text"
											class="form-control"
											v-model="tree.rfid_id"
										>
											<option
												v-for="rfid in availableRfids"
												:key="rfid.id"
												:value="rfid.id"
											>
												{{ rfid.code }}
											</option>
										</select>
									</div>
								</div>

								<div class="modal-footer">
									<button
										type="button"
										class="btn btn-secondary"
										data-dismiss="modal"
									>
										Sair
									</button>
									<button
										type="submit"
										class="btn btn-primary"
									>
										Atualizar
									</button>
								</div>
							</form>
						</div>
					</div>
				</div>
		</div>
	</div>
</template>

<script src="./Tree.js"></script>

<style lang="scss" scoped>
// Variáveis
$primary-color: #3d8160;
$primary-dark: #2d6047;
$text-color: #2c3e50;
$text-light: #6c757d;
$white: #ffffff;
$background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
$shadow: rgba(0, 0, 0, 0.1);
$shadow-hover: rgba(0, 0, 0, 0.15);

// Container Principal
.tree-container {
	min-height: 100vh;
	background: $background;
	padding: 2rem;
	font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

	@media (max-width: 768px) {
		padding: 1rem;
	}
}

// Header Section
.page-header {
	background: $white;
	border-radius: 20px;
	padding: 2.5rem;
	margin-bottom: 2rem;
	box-shadow: 0 8px 25px $shadow;
	border: 1px solid rgba(255, 255, 255, 0.2);
	backdrop-filter: blur(10px);
	transition: transform 0.3s ease, box-shadow 0.3s ease;

	&:hover {
		transform: translateY(-2px);
		box-shadow: 0 12px 35px $shadow-hover;
	}

	.header-content {
		display: flex;
		align-items: center;
		gap: 2rem;

		@media (max-width: 768px) {
			flex-direction: column;
			text-align: center;
			gap: 1.5rem;
		}
	}

	.header-navigation {
		display: flex;
		align-items: center;
	}

	.header-icon {
		width: 70px;
		height: 70px;
		background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
		border-radius: 20px;
		display: flex;
		align-items: center;
		justify-content: center;
		color: $white;
		font-size: 2rem;
		box-shadow: 0 8px 25px rgba($primary-color, 0.3);
		transition: transform 0.3s ease;

		&:hover {
			transform: scale(1.05);
		}
	}

	.header-text {
		flex: 1;

		.page-title {
			font-size: 2.5rem;
			font-weight: 700;
			color: $primary-color;
			margin: 0 0 0.5rem 0;
		}

		.page-subtitle {
			font-size: 1.1rem;
			color: $text-light;
			margin: 0;
			font-weight: 400;
		}
	}

	.header-actions {
		display: flex;
		gap: 1rem;

		@media (max-width: 768px) {
			flex-direction: column;
			width: 100%;
		}
	}
}

// Main Content
.main-content {
	display: flex;
	flex-direction: column;
	gap: 2rem;
}

// Loading Section
.loading-section {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 400px;

	.loading-card {
		background: $white;
		border-radius: 20px;
		padding: 3rem;
		text-align: center;
		box-shadow: 0 8px 25px $shadow;
		border: 1px solid rgba(255, 255, 255, 0.2);

		p {
			margin-top: 1rem;
			color: $text-light;
			font-size: 1.1rem;
		}
	}
}

// Tabela - Mantendo funcionalidades, modernizando visual
.trees-table-modern {
	background: $white;
	border-radius: 20px;
	box-shadow: 0 8px 25px $shadow;
	padding: 2rem;
	transition: transform 0.3s ease;

	&:hover {
		transform: translateY(-2px);
	}

	// Mantendo estilos originais para estados especiais
	:deep(.unselectable) {
		background-color: #ddd;
		cursor: not-allowed;
	}

	:deep(.deactivate-tree) {
		background-color: #818181a6;
		background: repeating-linear-gradient(
			45deg,
			#e7e7e7be,
			#e7e7e7be 10px,
			#d4d4d4a2 10px,
			#d4d4d4a2 20px
		);
	}
}

// Botões
.btn-primary {
	background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
	border: none;
	border-radius: 12px;
	padding: 0.75rem 1.5rem;
	font-weight: 600;
	font-size: 0.95rem;
	color: $white;
	display: flex;
	align-items: center;
	gap: 0.5rem;
	transition: all 0.3s ease;
	box-shadow: 0 4px 15px rgba($primary-color, 0.3);
	text-decoration: none;

	&:hover {
		transform: translateY(-2px);
		box-shadow: 0 6px 20px rgba($primary-color, 0.4);
		color: $white;
		text-decoration: none;
	}

	&:active {
		transform: translateY(0);
	}

	i {
		font-size: 1rem;
	}
}

// Responsividade
@media (max-width: 768px) {
	.tree-container {
		padding: 1rem;
	}

	.page-header {
		padding: 1.5rem;
		margin-bottom: 1rem;

		.header-content {
			flex-direction: column;
			text-align: center;
			gap: 1rem;
		}

		.header-text .page-title {
			font-size: 2rem;
		}

		.header-actions {
			width: 100%;
			flex-direction: column;
		}
	}

	.trees-table-modern {
		padding: 1rem;
		margin: 10px 0;
	}

	.btn-primary {
		width: 100%;
		justify-content: center;
	}
}

@media (max-width: 480px) {
	.page-header .header-text .page-title {
		font-size: 1.75rem;
	}

	.loading-card {
		padding: 2rem 1rem;
	}
}
</style>
