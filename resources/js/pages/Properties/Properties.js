import { Fields, PaginationOptions } from "./TableOptions";

export default {
	data() {
		return {
			authToken: null,
			target: null,
			userRoles: null,
			userRole: "",
			properties: [],
			property: {
				id: "",
				name: "",
				owner_name: "",
				description: "",
				city: "",
				uf: "",
				area_name: "",
				area: null,
				status: null,
				total_homogeneous_areas: null,
				geolocation: {
					latitude: 0,
					longitude: 0,
					ratio: 2.0,
				},
			},
			exportProperty: {
				id: "",
				name: "",
				startDate: "",
				endDate: "",
			},
			city: {},
			cities: {},
			state: {},
			states: [],
			property_status: null,
			editMode: false,
			loading: false,
			loadingCities: false,
			fields: Fields,
			paginationOptions: PaginationOptions,
			selectedDatePeriod: null,
			datePeriods: [],
			xlsLoading: null,
			pdfLoading: null,
			startDate: "",
			endDate: "",
			startDates: [],
			endDates: [],
			fileFormat: null,
		};
	},

	computed: {
		totalProperties() {
			return this.properties.length > 0;
		},

		filteredStartDates() {
			if (!this.endDate) {
				return this.startDates;
			}
			const endYear = parseInt(this.endDate.split("/")[1], 10);
			return this.startDates.filter((date) => {
				const startYear = parseInt(date.split("/")[1], 10);
				return startYear < endYear;
			});
		},

		filteredEndDates() {
			if (!this.startDate) {
				return this.endDates;
			}
			const startYear = parseInt(this.startDate.split("/")[1], 10);
			return this.endDates.filter((date) => {
				const endYear = parseInt(date.split("/")[1], 10);
				return endYear > startYear;
			});
		},
	},

	created() {
		this.authToken = window.token;
		this.fields[4].sortFn = this.sortStatus;
		this.userRole = window.Roles[0].label;

		this.index();
	},

	async mounted() {
		await this.fetchVisitDates();
		await this.getStates();
	},

	methods: {
		sortStatus(x, y) {
			if (!(typeof x === "boolean")) {
				return x < y ? -1 : x > y ? 1 : 0;
			}
			this.fields[4].sortFn = null;
		},

		editModal(property) {
			this.editMode = true;

			this.property.id = property.id;
			this.property.name = property.name;
			this.property.area = property.area;
			this.property.owner_name = property.owner_name;
			this.property.description = property.description;
			this.property.city = property.city;
			this.property.uf = property.uf;
			this.property.status = property.status;
			this.property.geolocation.latitude = Number(
				property?.geolocation?.latitude ?? 0
			).toFixed(7);
			this.property.geolocation.longitude = Number(
				property?.geolocation?.longitude ?? 0
			).toFixed(7);
			this.city = property.city;

			$("#modalProperties").modal("show");
		},

		addModal() {
			let _this = this;
			_this.clearForm();
			this.editMode = false;
			$("#modalProperties").modal("show");
		},

		async addModalExport(property) {
			this.clearFormExport();
			this.exportProperty.id = property.id;
			this.exportProperty.name = property.name;
			$("#modalPropertiesExport").modal("show");
		},

		async getStates() {
			await axios
				.get(
					"https://servicodados.ibge.gov.br/api/v1/localidades/estados"
				)
				.then((response) => {
					this.states = response.data;
				})
				.catch((err) => {
					console.log(err);
				});
		},

		async getCities(event) {
			let state = event.target.value;
			this.loadingCities = true;

			await axios
				.get(
					`https://servicodados.ibge.gov.br/api/v1/localidades/estados/${state}/municipios`
				)
				.then((response) => {
					this.cities = response.data;
					this.loadingCities = false;
				})
				.catch((err) => {
					this.loadingCities = false;
					console.log(err);
				});
		},

		index() {
			this.loading = true;

			axios
				.get("/api/v1/properties", {
					headers: { authorization: `bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						this.properties = response.data.data;
					} else {
						console.log(response);
					}
					this.loading = false;
				})
				.catch((err) => {
					console.log(err);
					this.loading = false;
				});
		},

		store() {
			axios
				.post("/api/v1/properties", this.property, {
					headers: { authorization: `bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 201 && response.data.success) {
						Swal.fire({
							title: response.data.message,
							icon: "success",
							showConfirmButton: false,
							timer: 1500,
						});
					}
					this.clearForm();
					this.index();
				})
				.catch((err) => {
					Swal.fire({
						title: err.response.data.message,
						text: err.response.data.detail,
						icon: "error",
						confirmButtonText: "Fechar",
					});
				});
		},

		update() {
			axios
				.put(`/api/v1/properties/${this.property.id}`, this.property, {
					headers: { authorization: `bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200 && response.data.success) {
						Swal.fire({
							title: response.data.message,
							icon: "success",
							showConfirmButton: false,
							timer: 1500,
						});
					} else {
						console.log(response);
					}
					this.clearForm();
					this.index();
				})
				.catch((err) => {
					Swal.fire({
						title: err.response.data.message,
						text: err.response.data.detail,
						icon: "error",
						confirmButtonText: "Fechar",
					});
				});
		},

		checkStatus(status) {
			let message = "";

			switch (status) {
				case 0:
					return `<h6><span class="badge badge-pill badge-danger">Desativada</span></h6>`;
				case 1:
					return `<h6><span class="badge badge-pill badge-info text-light">Ativada</span></h6>`;
				case 2:
					return `<h6><span class="badge badge-pill badge-success">Ativada e Compartilhada</span></h6>`;
				default:
					break;
			}

			return message;
		},

		changeStatus(property_id, status = true) {
			let status_message = "";
			this.property_status = status;

			status
				? (status_message = "ativar")
				: (status_message = "desativar");

			Swal.fire({
				title: "Deseja " + status_message + " esta propriedade?",
				icon: "warning",
				showCancelButton: true,
				confirmButtonColor: "#3085d6",
				cancelButtonColor: "#d33",
				confirmButtonText: "Sim",
				cancelButtonText: "Cancelar",
			}).then((result) => {
				if (result.value) {
					axios
						.put(
							`/api/v1/properties/${property_id}/status`,
							{
								status: this.property_status,
							},
							{
								headers: {
									authorization: `bearer ${this.authToken}`,
								},
							}
						)
						.then((response) => {
							if (response.status === 200) {
								Swal.fire({
									title: response.data.message,
									icon: "success",
									showConfirmButton: false,
									timer: 1500,
								});
							} else {
								console.log(response);
							}
							this.index();
						})
						.catch((err) => {
							console.log(err);
						});
				}
			});
		},

		clearForm() {
			this.editMode = false;
			this.property.id = null;
			this.property.name = "";
			this.property.area = null;
			this.property.owner_name = "";
			this.property.description = "";
			this.property.city = "";
			this.property.uf = "";
			this.property.status = true;
			this.property.geolocation.latitude = null;
			this.property.geolocation.longitude = null;
			this.city = {};

			$("#modalProperties").modal("hide");
		},

		clearFormExport() {
			(this.startDate = ""),
				(this.endDate = ""),
				$("#modalPropertiesExport").modal("hide");
		},

		exportPropertyXls() {
			this.xlsLoading = this.exportProperty.id;
			const fileFormat = "xls";
			const { id, name, startDate, endDate } = this.exportProperty; // Pega os dados da exportação
			alert("Aguarde alguns minutos, pois o processo pode ser demorado");
			axios
				.post(
					"/api/v1/exportProperty-xls",
					{ propertyId: id, startDate, endDate },
					{
						responseType: "arraybuffer",
						headers: { authorization: `bearer ${this.authToken}` },
					}
				)
				.then((response) => {
					this.downloadFile(response, name, fileFormat);
					this.xlsLoading = null;
				})
				.catch((err) => {
					console.log(err.response);
					this.xlsLoading = null;
				});
		},

		exportPropertyPdf(propertyId) {
			this.pdfLoading = propertyId;
			const fileFormat = "pdf";
			let data = {
				property_id: propertyId,
			};

			axios
				.post("/api/v1/export-pdf2", data, {
					responseType: "arraybuffer",
					headers: { authorization: `bearer ${this.authToken}` },
				})
				.then((response) => {
					this.downloadFile(
						response,
						"property_" + propertyId,
						fileFormat
					);
					this.pdfLoading = null;
				})
				.catch((err) => {
					console.log(err.response);
					this.pdfLoading = null;
				});
		},

		downloadFile(response, filename, fileformat) {
			let type = "";
			switch (fileformat) {
				case "pdf":
					type = "application/pdf";
					break;
				case "xls":
					type = "application/vnd.ms-excel";
					break;
				case "csv":
					type = "text/csv";
					break;
				default:
					console.log("error - file format!");
			}

			var newBlob = new Blob([response.data], { type: type });
			const data = window.URL.createObjectURL(newBlob);
			var link = document.createElement("a");

			if (window.navigator && window.navigator.msSaveOrOpenBlob) {
				window.navigator.msSaveOrOpenBlob(newBlob);
			}

			link.href = data;
			link.download = `${filename}.${fileformat}`;
			link.click();
			// For Firefox
			setTimeout(function () {
				window.URL.revokeObjectURL(data);
			}, 100);
		},

		async handleExport() {
			console.log(this.selectedDatePeriod);

			try {
				this.parseSelectedPeriod();

				if (!this.startDate || !this.endDate) {
					return;
				}
				console.log(this.startDate);
				console.log(this.endDate);
				this.exportProperty.startDate = this.startDate;
				this.exportProperty.endDate = this.endDate;

				this.exportPropertyXls(
					this.property.id,
					this.property.name,
					this.startDate,
					this.endDate
				);
			} catch (error) {
				console.error(error);
			}
		},

		async fetchVisitDates() {
			try {
				const response = await axios.get("/api/v1/get-visit-dates", {
					headers: { authorization: `bearer ${this.authToken}` },
				});
				const { oldestVisit, latestVisit } = response.data;

				if (oldestVisit && latestVisit) {
					this.generateDateOptions(oldestVisit, latestVisit);
				} else {
					this.datePeriods = [];
				}
			} catch (error) {
				if (error.response && error.response.status === 404) {
					this.datePeriods = [];
				} else {
					console.error(error);
				}
			}
		},

		generateDateOptions(oldestVisit, latestVisit) {
			const oldestYear = new Date(oldestVisit).getFullYear();
			const latestYear = new Date(latestVisit).getFullYear();
			const currentYear = new Date().getFullYear();
			const currentMonth = new Date().getMonth();
			const limitYear = currentMonth < 9 ? currentYear - 1 : currentYear;

			this.datePeriods = [];

			for (
				let year = oldestYear;
				year <= latestYear && year <= limitYear;
				year++
			) {
				const nextYear = year + 1;
				this.datePeriods.push(`Outubro/${year} - Setembro/${nextYear}`);
			}
		},

		parseSelectedPeriod() {
			if (!this.selectedDatePeriod) {
				return;
			}

			const periodParts = this.selectedDatePeriod.split(" - ");
			const startYear = periodParts[0].split("/")[1];
			const endYear = periodParts[1].split("/")[1];

			this.startDate = `Outubro/${startYear}`;
			this.endDate = `Setembro/${endYear}`;
		},

		exportPractice(propertyId) {
			this.xlsLoading = propertyId;
			this.fileFormat = "xls";

			axios
				.post(
					"/api/v1/export-practice",
					{ property: propertyId },
					{
						responseType: "arraybuffer",
						headers: { authorization: `bearer ${this.authToken}` },
					}
				)
				.then((response) => {
					this.downloadFile(
						response,
						"plataforma_cacau_dados_da_pratica",
						this.fileFormat
					);
					this.xlsLoading = null;
				})
				.catch((err) => {
					console.log(err.response);
					this.xlsLoading = null;
				});
		},

		deleteProperty(property) {
			Swal.fire({
				title: 'Tem certeza?',
				html: `
					<p>Esta ação irá excluir permanentemente a propriedade <strong>"${property.name}"</strong> e todos os dados relacionados:</p>
					<ul style="text-align: left; margin: 20px 0;">
						<li>Áreas homogêneas</li>
						<li>Estratos</li>
						<li>Pontos de amostragem</li>
						<li>Árvores</li>
						<li>Visitas e coletas</li>
						<li>Dados de frutos e doenças</li>
						<li>Geolocalizações</li>
					</ul>
					<p style="color: #d33; font-weight: bold;">Esta ação não pode ser desfeita!</p>
				`,
				icon: 'warning',
				showCancelButton: true,
				confirmButtonColor: '#d33',
				cancelButtonColor: '#3085d6',
				confirmButtonText: 'Sim, excluir!',
				cancelButtonText: 'Cancelar',
				width: '500px'
			}).then((result) => {
				if (result.isConfirmed) {
					// Mostrar loading
					Swal.fire({
						title: 'Excluindo propriedade...',
						text: 'Por favor, aguarde. Esta operação pode levar alguns minutos.',
						allowOutsideClick: false,
						allowEscapeKey: false,
						showConfirmButton: false,
						didOpen: () => {
							Swal.showLoading();
						}
					});

					axios
						.delete(`/api/v1/properties/${property.id}`, {
							headers: { authorization: `bearer ${this.authToken}` },
						})
						.then((response) => {
							if (response.status === 200) {
								Swal.fire({
									title: 'Excluída!',
									text: response.data.message,
									icon: 'success',
									showConfirmButton: false,
									timer: 2000,
								});
								// Recarregar a lista de propriedades
								this.index();
							}
						})
						.catch((err) => {
							let errorMessage = 'Erro interno do servidor';
							if (err.response && err.response.data && err.response.data.message) {
								errorMessage = err.response.data.message;
							}

							Swal.fire({
								title: 'Erro ao excluir',
								text: errorMessage,
								icon: 'error',
								confirmButtonText: 'Fechar',
							});
						});
				}
			});
		},
	},
};
