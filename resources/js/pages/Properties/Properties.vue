<template>
	<div class="properties-container">
		<!-- Header Principal -->
		<div class="page-header">
			<div class="header-content">
				<div class="header-icon">
					<i class="fas fa-warehouse"></i>
				</div>
				<div class="header-text">
					<h1 class="page-title">Propriedades</h1>
					<p class="page-subtitle">Gere<PERSON>ie suas propriedades rurais e acompanhe o desenvolvimento</p>
				</div>
				<div class="header-actions">
					<div v-if="userRole === 'pre-registered'">
						<router-link
							tag="button"
							class="btn-primary"
							v-bind:to="{
								path: '/panel/add-property',
								query: { nextArea: `${totalProperties}` },
							}"
						>
							<i class="fas fa-plus"></i>
							<span>Registrar Propriedade</span>
						</router-link>
					</div>
					<div v-else>
						<button
							class="btn-primary"
							@click="addModal()"
						>
							<i class="fas fa-plus"></i>
							<span>Registrar Propriedade</span>
						</button>
					</div>
				</div>
			</div>
		</div>

		<!-- <PERSON><PERSON><PERSON><PERSON> Principal -->
		<div class="main-content">
			<!-- Loading State -->
			<div v-if="loading" class="loading-section">
				<div class="loading-card">
					<loader :loading="loading"></loader>
					<p>Carregando propriedades...</p>
				</div>
			</div>

			<!-- Empty State -->
			<div v-else-if="!loading && !properties.length > 0" class="empty-state">
				<div class="empty-card">
					<div class="empty-icon">
						<i class="fas fa-warehouse"></i>
					</div>
					<div class="empty-content">
						<h3>Nenhuma propriedade encontrada</h3>
						<p>Registre sua primeira propriedade para começar a gerenciar seus dados rurais</p>
						<router-link
							class="btn-primary large"
							tag="button"
							to="/panel/add-property"
						>
							<i class="fas fa-plus"></i>
							<span>Registrar Primeira Propriedade</span>
						</router-link>
					</div>
				</div>
			</div>

			<!-- Properties Table - Estilo Original -->
			<div v-else class="properties-table-original">
				<vue-good-table
					title="Propriedades"
					:columns="fields"
					:rows="properties"
					:search-options="{
						enabled: true,
						placeholder: 'Pesquisar...',
					}"
					:pagination-options="paginationOptions"
					:sort-options="{
						enabled: true,
						initialSortBy: { field: 'name', type: 'desc' },
					}"
				>
					<template slot="table-row" slot-scope="props">
						<div v-if="props.column.field == 'actions'">
							<div class="btn-group" role="group">
								<form @submit.prevent="exportPropertyPDF(props.row)">
									<button
										title="Exportar PDF"
										type="submit"
										class="btn btn-sm btn-danger"
										style="margin: 1px"
										:disabled="
											pdfLoading === true &&
											pdfLoading === props.row.id
										"
									>
										<span
											v-if="
												pdfLoading === true &&
												pdfLoading === props.row.id
											"
											class="spinner-border spinner-border-sm"
											role="status"
											aria-hidden="true"
										></span>
										<i
											v-else
											class="fas fa-file-pdf"
										></i>
									</button>
								</form>

								<router-link
									v-if="
										props.row.total_homogeneous_areas > 0
									"
									:to="{
										name: 'homogeneous-areas',
										params: { id: props.row.id },
									}"
									class="btn btn-sm btn-info"
									style="margin: 1px"
								>
									<i class="fas fa-layer-group fa-sm"> </i>
								</router-link>
							</div>
						</div>
						<div v-else-if="props.column.field == 'name'">
							<router-link
								:to="{
									name: 'property-details',
									params: { id: props.row.id },
								}"
								class="property-link"
							>
								{{ props.row.name }}
							</router-link>
						</div>
						<span v-else>
							{{ props.formattedRow[props.column.field] }}
						</span>
					</template>
				</vue-good-table>
			</div>
		</div>

		<!-- Modal de Adicionar Propriedade -->
		<div
			class="modal fade"
			id="modalAddProperty"
			ref="modal"
			tabindex="-1"
			role="dialog"
		>
			<div class="modal-dialog modal-lg" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title">Registrar Propriedade</h5>
						<button
							type="button"
							class="close"
							data-dismiss="modal"
							aria-label="Close"
						>
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
					<div class="modal-body">
						<form @submit.prevent="addProperty()">
							<div class="form-group">
								<label for="name">Nome da Propriedade</label>
								<input
									type="text"
									class="form-control"
									id="name"
									v-model="newProperty.name"
									required
								/>
							</div>
							<div class="form-group">
								<label for="location">Localização</label>
								<input
									type="text"
									class="form-control"
									id="location"
									v-model="newProperty.location"
									required
								/>
							</div>
							<div class="form-group">
								<label for="area">Área (hectares)</label>
								<input
									type="number"
									step="0.01"
									class="form-control"
									id="area"
									v-model="newProperty.area"
									required
								/>
							</div>
							<div class="modal-footer">
								<button
									type="button"
									class="btn btn-secondary"
									data-dismiss="modal"
								>
									Cancelar
								</button>
								<button
									type="submit"
									class="btn btn-primary"
									:disabled="addLoading"
								>
									<span
										v-if="addLoading"
										class="spinner-border spinner-border-sm"
										role="status"
										aria-hidden="true"
									></span>
									<span v-if="!addLoading">Registrar</span>
									<span v-else>Registrando...</span>
								</button>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script src="./Properties.js"></script>

<style lang="scss" scoped>
// Variáveis
$primary-color: #3d8160;
$primary-dark: #2d6047;
$text-color: #2c3e50;
$text-light: #6c757d;
$white: #ffffff;
$background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
$shadow: rgba(0, 0, 0, 0.1);
$shadow-hover: rgba(0, 0, 0, 0.15);

.properties-container {
	min-height: 100vh;
	background: $background;
	padding: 1rem;
	font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

	// Header Principal
	.page-header {
		background: $white;
		border-radius: 20px;
		padding: 2rem;
		margin-bottom: 2rem;
		box-shadow: 0 10px 30px $shadow;
		border: 1px solid rgba(255, 255, 255, 0.2);
		backdrop-filter: blur(10px);

		.header-content {
			display: flex;
			align-items: center;
			justify-content: space-between;
			gap: 1.5rem;
		}

		.header-icon {
			width: 70px;
			height: 70px;
			background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
			border-radius: 20px;
			display: flex;
			align-items: center;
			justify-content: center;
			color: $white;
			font-size: 2rem;
			box-shadow: 0 8px 25px rgba($primary-color, 0.3);
			transition: transform 0.3s ease;

			&:hover {
				transform: scale(1.05);
			}
		}

		.header-text {
			flex: 1;

			.page-title {
				font-size: 2.5rem;
				font-weight: 700;
				color: $primary-color;
				margin: 0 0 0.5rem 0;
			}

			.page-subtitle {
				font-size: 1.1rem;
				color: $text-light;
				margin: 0;
				font-weight: 400;
			}
		}

		.header-actions {
			.btn-primary {
				background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
				color: $white;
				border: none;
				padding: 0.75rem 1.5rem;
				border-radius: 12px;
				font-weight: 600;
				cursor: pointer;
				transition: all 0.3s ease;
				display: flex;
				align-items: center;
				gap: 0.5rem;
				box-shadow: 0 4px 15px rgba($primary-color, 0.3);
				text-decoration: none;

				&:hover {
					transform: translateY(-2px);
					box-shadow: 0 6px 20px rgba($primary-color, 0.4);
					color: $white;
				}

				i {
					font-size: 1rem;
				}
			}
		}
	}

	// Conteúdo Principal
	.main-content {
		display: flex;
		flex-direction: column;
		gap: 2rem;
	}

	// Loading State
	.loading-section {
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 400px;

		.loading-card {
			background: $white;
			border-radius: 16px;
			padding: 3rem;
			box-shadow: 0 4px 20px $shadow;
			text-align: center;

			p {
				margin-top: 1rem;
				color: $text-light;
				font-size: 1.1rem;
			}
		}
	}

	// Empty State
	.empty-state {
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 500px;

		.empty-card {
			background: $white;
			border-radius: 20px;
			padding: 4rem 3rem;
			box-shadow: 0 4px 20px $shadow;
			text-align: center;
			max-width: 500px;

			.empty-icon {
				width: 100px;
				height: 100px;
				background: linear-gradient(135deg, rgba($primary-color, 0.1) 0%, rgba($primary-color, 0.05) 100%);
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0 auto 2rem;

				i {
					font-size: 3rem;
					color: $primary-color;
				}
			}

			.empty-content {
				h3 {
					font-size: 1.5rem;
					font-weight: 600;
					color: $text-color;
					margin-bottom: 1rem;
				}

				p {
					color: $text-light;
					margin-bottom: 2rem;
					line-height: 1.6;
				}

				.btn-primary {
					background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
					color: $white;
					border: none;
					padding: 1rem 2rem;
					border-radius: 12px;
					font-weight: 600;
					cursor: pointer;
					transition: all 0.3s ease;
					display: inline-flex;
					align-items: center;
					gap: 0.75rem;
					box-shadow: 0 4px 15px rgba($primary-color, 0.3);
					text-decoration: none;
					font-size: 1.1rem;

					&:hover {
						transform: translateY(-2px);
						box-shadow: 0 6px 20px rgba($primary-color, 0.4);
						color: $white;
					}

					&.large {
						padding: 1.25rem 2.5rem;
						font-size: 1.2rem;
					}

					i {
						font-size: 1.2rem;
					}
				}
			}
		}
	}

	// Tabela Original - Estilo Preservado
	.properties-table-original {
		background: $white;
		border-radius: 20px;
		box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
		padding: 25px;
		margin: 20px 0;
		transition: transform 0.3s ease;

		&:hover {
			transform: translateY(-2px);
		}

		.property-link {
			color: $primary-color;
			text-decoration: none;
			font-weight: 500;

			&:hover {
				color: $primary-dark;
				text-decoration: underline;
			}
		}
	}

	// Responsividade
	@media (max-width: 768px) {
		.properties-container {
			padding: 0.5rem;
		}

		.page-header {
			padding: 1.5rem;
			margin-bottom: 1.5rem;

			.header-content {
				flex-direction: column;
				text-align: center;
				gap: 1rem;
			}

			.header-icon {
				width: 60px;
				height: 60px;
				font-size: 1.8rem;
			}

			.page-title {
				font-size: 2rem;
			}

			.page-subtitle {
				font-size: 1rem;
			}

			.header-actions .btn-primary {
				padding: 0.6rem 1.2rem;
				font-size: 0.9rem;
			}
		}

		.table-section {
			padding: 1.5rem;

			.table-wrapper {
				:deep(.vgt-table) {
					font-size: 0.9rem;

					th, td {
						padding: 0.75rem 0.5rem;
					}
				}
			}
		}

		.empty-card {
			padding: 3rem 2rem !important;

			.empty-icon {
				width: 80px !important;
				height: 80px !important;

				i {
					font-size: 2.5rem !important;
				}
			}

			.empty-content {
				h3 {
					font-size: 1.25rem !important;
				}

				.btn-primary.large {
					padding: 1rem 1.5rem !important;
					font-size: 1rem !important;
				}
			}
		}
	}

	@media (max-width: 480px) {
		.page-header .page-title {
			font-size: 1.75rem;
		}

		.table-section {
			padding: 1rem;

			.table-wrapper {
				:deep(.vgt-table) {
					font-size: 0.8rem;

					th, td {
						padding: 0.5rem 0.25rem;
					}
				}
			}
		}
	}
}
</style>
