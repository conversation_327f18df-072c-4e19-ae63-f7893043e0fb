<template>
	<div class="properties-container">
		<!-- Header Principal -->
		<div class="page-header">
			<div class="header-content">
				<div class="header-info">
					<div class="header-icon">
						<i class="fas fa-warehouse"></i>
					</div>
					<div class="header-text">
						<h1>Propriedades</h1>
						<p>Gere<PERSON>ie suas propriedades rurais e acompanhe o desenvolvimento</p>
					</div>
				</div>
				<div class="header-actions" v-if="properties.length > 0">
					<div v-if="userRole === 'pre-registered'">
						<router-link
							tag="button"
							class="btn-primary"
							v-bind:to="{
								path: '/panel/add-property',
								query: { nextArea: `${totalProperties}` },
							}"
						>
							<i class="fas fa-plus"></i>
							<span>Registrar Propriedade</span>
						</router-link>
					</div>
					<div v-else>
						<button
							class="btn-primary"
							@click="addModal()"
						>
							<i class="fas fa-plus"></i>
							<span>Registrar Propriedade</span>
						</button>
					</div>
				</div>
			</div>
		</div>

		<!-- Conteúdo Principal -->
		<div class="main-content">
			<!-- Loading State -->
			<div v-if="loading" class="loading-section">
				<div class="loading-card">
					<loader :loading="loading"></loader>
					<p>Carregando propriedades...</p>
				</div>
			</div>

			<!-- Empty State -->
			<div v-else-if="!loading && !properties.length > 0" class="empty-state">
				<div class="empty-card">
					<div class="empty-icon">
						<i class="fas fa-warehouse"></i>
					</div>
					<div class="empty-content">
						<h3>Nenhuma propriedade encontrada</h3>
						<p>Registre sua primeira propriedade para começar a gerenciar seus dados rurais</p>
						<router-link
							class="btn-primary large"
							tag="button"
							to="/panel/add-property"
						>
							<i class="fas fa-plus"></i>
							<span>Registrar Primeira Propriedade</span>
						</router-link>
					</div>
				</div>
			</div>

			<!-- Properties Table -->
			<div v-else class="table-section">
				<div class="table-card">

				<div class="table-header">
					<div class="table-title">
						<h2>Lista de Propriedades</h2>
						<p>{{ properties.length }} propriedade{{ properties.length !== 1 ? 's' : '' }} encontrada{{ properties.length !== 1 ? 's' : '' }}</p>
					</div>
				</div>

				<div class="table-wrapper">
					<vue-good-table
						:columns="fields"
						:rows="properties"
						:search-options="{
							enabled: true,
							placeholder: 'Pesquisar propriedades...',
						}"
						:pagination-options="paginationOptions"
						:sort-options="{
							enabled: true,
							initialSortBy: { field: 'name', type: 'desc' },
						}"
						styleClass="modern-table"
					>
					<div slot="emptystate">
						Não há propriedades registradas.
					</div>
					<template slot="table-row" slot-scope="props">
						<span v-if="props.column.field == 'name'">
							{{ props.row.name }}
						</span>
						<span v-if="props.column.field == 'owner_name'">
							{{ props.row.owner_name }}
						</span>
						<span v-if="props.column.field == 'city'">
							{{ props.row.city }}
						</span>
						<span v-if="props.column.field == 'uf'">
							{{ props.row.uf }}
						</span>
						<span v-if="props.column.field == 'description'">
							{{ props.row.description || "-" }}
						</span>
						<span
							v-if="props.column.field == 'status'"
							v-html="checkStatus(props.row.status)"
						>
						</span>
						<div v-if="props.column.field == 'actions'">
							<div class="d-flex">
								<router-link
									v-if="userRole === 'pre-registered'"
									title="Resumo"
									tag="button"
									class="btn btn-md btn-secondary"
									style="margin: 0.2rem"
									v-bind:to="{
										path: '/panel/overview',
										query: { propid: props.row.id },
									}"
									:disabled="props.row.status === 0"
								>
									<i class="fas fa-file fa-sm"> </i>
								</router-link>

								<button
									title="Editar propriedade"
									class="btn btn-md btn-info"
									style="margin: 0.2rem"
									@click="editModal(props.row)"
									:disabled="props.row.status === 0"
								>
									<i class="fas fa-edit fa-sm"></i>
								</button>

								<button
									title="Excluir propriedade"
									class="btn btn-md btn-danger"
									style="margin: 0.2rem"
									@click="deleteProperty(props.row)"
									:disabled="props.row.status === 0"
								>
									<i class="fas fa-trash fa-sm"></i>
								</button>

								<button
									title="Exportar Coletas"
									class="btn btn-md btn-warning"
									style="
										margin: 0.2rem;
										background-color: #55a97f;
										border-color: #55a97f;
									"
									@click="addModalExport(props.row)"
								>
									<i class="fas fa-file-excel"></i>
								</button>

								<button
									title="Exportar Dados da Prática"
									class="btn btn-md btn-success"
									style="
										margin: 0.2rem;
										background-color: #65350F;
										border-color: #65350F;
									"
									@click="exportPractice(props.row.id)"
									:disabled="xlsLoading === props.row.id"
								>
									<span
										v-if="xlsLoading === props.row.id"
										class="spinner-border spinner-border-sm"
										role="status"
										aria-hidden="true"
									></span>
									<i
										v-else
										class="fas fa-file-excel"
									></i>
								</button>

								<template >
									<form
										@submit.prevent="
											exportPropertyPdf(props.row.id)
										"
									>
										<button
											title="Exportar Dados da Propriedade"
											type="submit"
											class="btn btn-md btn-danger"
											style="
													margin: 0.2rem;
													background-color: #b31a38;
													border-color: #b31a38;
												"
											:disabled="
												pdfLoading === props.row.id
											"
										>
											<span
												v-if="
													pdfLoading === props.row.id
												"
												class="spinner-border spinner-border-sm"
												role="status"
												aria-hidden="true"
											></span>
											<i
												v-else
												class="fas fa-file-pdf"
											></i>
										</button>
									</form>
								</template>
								<!--
								<router-link
									v-if="userRole === 'pre-registered'"
									:title="props.row.area_name"
									tag="button"
									class="btn btn-md btn-success"
									style="margin: 0.2rem"
									v-bind:to="{
										path: '/panel/blocks',
										query: { propid: props.row.id },
									}"
									:disabled="props.row.status === 0"
								>
									<i class="fas fa-eye fa-sm"> </i>
								</router-link>
								-->
								<router-link
									v-if="
										userRole === 'pre-registered' &&
										props.row.total_homogeneous_areas > 0
									"
									title="Áreas Homogêneas"
									tag="button"
									class="btn btn-md btn-success"
									style="margin: 0.2rem"
									v-bind:to="{
										path: '/panel/homogeneous-area',
										query: { propid: props.row.id },
									}"
									:disabled="props.row.status === 0"
								>
									<i class="fas fa-layer-group fa-sm"> </i>
								</router-link>

								<router-link
									v-if="userRole !== 'pre-registered'"
									:title="props.row.area_name"
									tag="button"
									class="btn btn-md btn-success"
									style="margin: 0.2rem"
									v-bind:to="{
										path: '/panel/homogeneous-area',
										query: { propid: props.row.id },
									}"
									:disabled="props.row.status === 0"
								>
									<i class="fas fa-layer-group fa-sm"> </i>
								</router-link>
							</div>
						</div>
					</template>
				</vue-good-table>
			</div>

			<div
				class="modal fade"
				id="modalProperties"
				ref="modal"
				tabindex="-1"
				role="dialog"
				aria-labelledby="modalPropertiesLabel"
				aria-hidden="true"
			>
				<div class="modal-dialog modal-dialog-centered" role="document">
					<div class="modal-content">
						<div class="modal-header">
							<h5
								class="modal-title"
								id="modalPropertiesLabel"
								v-show="!editMode"
							>
								ADICIONAR PROPRIEDADE
							</h5>
							<h5
								class="modal-title"
								id="modalPropertiesLabel"
								v-show="editMode"
							>
								ATUALIZAR PROPRIEDADE
							</h5>
							<button
								type="button"
								class="close"
								data-dismiss="modal"
								aria-label="Close"
							>
								<span aria-hidden="true">&times;</span>
							</button>
						</div>
						<div class="modal-body">
							<form
								@submit.prevent="editMode ? update() : store()"
							>
								<div class="form-row">
									<div class="form-group col-md-6">
										<label for="propertyName">
											Nome da Propriedade
										</label>
										<input
											id="propertyName"
											type="text"
											class="form-control"
											v-model="property.name"
										/>
									</div>
									<div class="form-group col-md-6">
										<label for="propertyArea">
											Área em hectares
										</label>
										<input
											id="propertyArea"
											type="number"
											class="form-control"
											v-model="property.area"
										/>
									</div>
								</div>

								<div class="form-group">
									<label for="propertyOwner">
										Nome do Proprietário
									</label>
									<input
										type="text"
										class="form-control"
										v-model="property.owner_name"
									/>
								</div>

								<div v-if="!editMode" class="form-row">
									<div class="form-group col-md-4">
										<label for="state">Estado</label>
										<select
											id="state"
											name="selectState"
											class="form-control"
											v-model="property.uf"
											@change="getCities($event)"
										>
											<option
												v-for="state in states"
												:value="state.sigla"
												:key="state.id"
											>
												{{ state.nome }}
											</option>
										</select>
									</div>

									<div class="form-group col-md-8">
										<label for="propertyCity">
											Município
										</label>
										<div
											v-if="
												!cities.length || loadingCities
											"
										>
											<span
												v-if="!loadingCities"
												class="text-danger"
											>
												Selecione um Estado
											</span>
											<span v-else>
												Carregando cidades...
											</span>
										</div>
										<div v-else>
											<select
												:id="'propertyCity'"
												name="city"
												class="form-control"
												v-model="property.city"
												:disabled="editMode"
											>
												<option
													v-for="city in cities"
													v-bind:value="city.nome"
													v-bind:key="city.id"
												>
													{{ city.nome }}
												</option>
											</select>
										</div>
									</div>
								</div>

								<div v-else class="form-row">
									<div class="form-group col-md-4">
										<label for="state">Estado</label>
										<input
											id="state"
											type="text"
											class="form-control"
											v-model="property.uf"
											disabled
										/>
									</div>
									<div class="form-group col-md-8">
										<label for="city">Munícipio</label>
										<input
											id="city"
											type="text"
											class="form-control"
											v-model="property.city"
											disabled
										/>
									</div>
								</div>

								<div
									class="form-row"
									v-if="property.geolocation !== null"
								>
									<div class="form-group col-md-6">
										<label for="latitude">Latitude</label>
										<input
											type="number"
											class="form-control"
											id="latitude"
											step="0.0000001"
											pattern="^(\+|-)?(?:100(?:(?:\.0{1,7})?)|(?:[0-9]|[1-8][0-9])(?:(?:\.[0-9]{1,7})?))$"
											v-model="
												property.geolocation.latitude
											"
											required
										/>
									</div>
									<div class="form-group col-md-6">
										<label for="longitude">Longitude</label>
										<input
											type="number"
											class="form-control"
											id="longitude"
											step="0.0000001"
											pattern="^(\+|-)?(?:180(?:(?:\.0{1,7})?)|(?:[0-9]|[1-9][0-9]|1[0-7][0-9])(?:(?:\.[0-9]{1,7})?))$"
											v-model="
												property.geolocation.longitude
											"
											required
										/>
									</div>
								</div>

								<div class="form-group">
									<label for="propertyDescript"
										>Descrição</label
									>
									<textarea
										id="propertyDescript"
										row="2"
										class="form-control"
										v-model="property.description"
									></textarea>
								</div>

								<div class="modal-footer">
									<button
										type="button"
										class="btn btn-secondary"
										data-dismiss="modal"
									>
										Sair
									</button>
									<button
										type="submit"
										class="btn btn-primary"
										v-show="!editMode"
									>
										Cadastrar
									</button>
									<button
										type="submit"
										class="btn btn-primary"
										v-show="editMode"
									>
										Atualizar
									</button>
								</div>
							</form>
						</div>
					</div>
				</div>
			</div>

			<template>
				<div
					class="modal fade"
					id="modalPropertiesExport"
					ref="modal"
					tabindex="-1"
					role="dialog"
				>
					<div
						class="modal-dialog modal-dialog-centered"
						role="document"
					>
						<div class="modal-content">
							<div class="modal-header">
								<h5 class="modal-title">EXPORTAÇÃO DE DADOS</h5>
								<button
									type="button"
									class="close"
									data-dismiss="modal"
									aria-label="Close"
								>
									<span aria-hidden="true">&times;</span>
								</button>
							</div>
							<div class="modal-body">
								<form @submit.prevent="handleExport">
									<div class="form-row">
										<div class="form-group col-md-12">
											<label for="datePeriod"
												>Selecione o período</label
											>
											<select
												id="datePeriod"
												class="form-control"
												v-model="selectedDatePeriod"
											>
												<option
													v-if="
														datePeriods.length === 0
													"
												>
													Não há visitas cadastradas
												</option>
												<option
													v-for="date in datePeriods"
													:key="date"
													:value="date"
												>
													{{ date }}
												</option>
											</select>
										</div>
									</div>
									<div class="modal-footer">
										<button
											type="button"
											class="btn btn-secondary"
											data-dismiss="modal"
										>
											Sair
										</button>
										<button
											type="submit"
											class="btn btn-primary"
											:disabled="
												datePeriods.length === 0 ||
												xlsLoading === exportProperty.id
											"
										>
											<span
												v-if="
													xlsLoading &&
													xlsLoading ===
														exportProperty.id
												"
												class="spinner-border spinner-border-sm"
												role="status"
												aria-hidden="true"
											></span>
											<span
												v-if="
													xlsLoading &&
													xlsLoading ===
														exportProperty.id
												"
												>Carregando...</span
											>
											<span v-else
												>Exportar para XLS</span
											>
										</button>
									</div>
								</form>
							</div>
						</div>
					</div>
				</div>
			</template>
				</div>
			</div>
		</div>
	</div>
</template>

<script src="./Properties.js"></script>

<style lang="scss" scoped>
// Variáveis
$primary-color: #3d8160;
$primary-dark: #2d6047;
$text-color: #2c3e50;
$text-light: #6c757d;
$white: #ffffff;
$background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
$shadow: rgba(0, 0, 0, 0.1);
$shadow-hover: rgba(0, 0, 0, 0.15);

.properties-container {
	min-height: 100vh;
	background: $background;
	padding: 1rem;
	font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

	// Header Principal
	.page-header {
		background: $white;
		border-radius: 20px;
		padding: 2rem;
		margin-bottom: 2rem;
		box-shadow: 0 10px 30px $shadow;
		border: 1px solid rgba(255, 255, 255, 0.2);
		backdrop-filter: blur(10px);

		.header-content {
			display: flex;
			align-items: center;
			justify-content: space-between;
			gap: 1.5rem;
		}

		.header-info {
			display: flex;
			align-items: center;
			gap: 1.5rem;
			flex: 1;
		}

		.header-icon {
			width: 70px;
			height: 70px;
			background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
			border-radius: 20px;
			display: flex;
			align-items: center;
			justify-content: center;
			color: $white;
			font-size: 2rem;
			box-shadow: 0 8px 25px rgba($primary-color, 0.3);
			transition: transform 0.3s ease;

			&:hover {
				transform: scale(1.05);
			}
		}

		.header-text {
			h1 {
				font-size: 2.5rem;
				font-weight: 700;
				color: $text-color;
				margin: 0 0 0.5rem 0;
				background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
				background-clip: text;
			}

			p {
				font-size: 1.1rem;
				color: $text-light;
				margin: 0;
				font-weight: 400;
			}
		}

		.header-actions {
			.btn-primary {
				background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
				color: $white;
				border: none;
				padding: 0.75rem 1.5rem;
				border-radius: 12px;
				font-weight: 600;
				cursor: pointer;
				transition: all 0.3s ease;
				display: flex;
				align-items: center;
				gap: 0.5rem;
				box-shadow: 0 4px 15px rgba($primary-color, 0.3);
				text-decoration: none;

				&:hover {
					transform: translateY(-2px);
					box-shadow: 0 6px 20px rgba($primary-color, 0.4);
					color: $white;
				}

				i {
					font-size: 1rem;
				}
			}
		}
	}

	// Conteúdo Principal
	.main-content {
		display: flex;
		flex-direction: column;
		gap: 2rem;
	}

	// Loading State
	.loading-section {
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 400px;

		.loading-card {
			background: $white;
			border-radius: 16px;
			padding: 3rem;
			box-shadow: 0 4px 20px $shadow;
			text-align: center;

			p {
				margin-top: 1rem;
				color: $text-light;
				font-size: 1.1rem;
			}
		}
	}

	// Empty State
	.empty-state {
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 500px;

		.empty-card {
			background: $white;
			border-radius: 20px;
			padding: 4rem 3rem;
			box-shadow: 0 4px 20px $shadow;
			text-align: center;
			max-width: 500px;

			.empty-icon {
				width: 100px;
				height: 100px;
				background: linear-gradient(135deg, rgba($primary-color, 0.1) 0%, rgba($primary-color, 0.05) 100%);
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0 auto 2rem;

				i {
					font-size: 3rem;
					color: $primary-color;
				}
			}

			.empty-content {
				h3 {
					font-size: 1.5rem;
					font-weight: 600;
					color: $text-color;
					margin-bottom: 1rem;
				}

				p {
					color: $text-light;
					margin-bottom: 2rem;
					line-height: 1.6;
				}

				.btn-primary {
					background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
					color: $white;
					border: none;
					padding: 1rem 2rem;
					border-radius: 12px;
					font-weight: 600;
					cursor: pointer;
					transition: all 0.3s ease;
					display: inline-flex;
					align-items: center;
					gap: 0.75rem;
					box-shadow: 0 4px 15px rgba($primary-color, 0.3);
					text-decoration: none;
					font-size: 1.1rem;

					&:hover {
						transform: translateY(-2px);
						box-shadow: 0 6px 20px rgba($primary-color, 0.4);
						color: $white;
					}

					&.large {
						padding: 1.25rem 2.5rem;
						font-size: 1.2rem;
					}

					i {
						font-size: 1.2rem;
					}
				}
			}
		}
	}

	// Table Section
	.table-section {
		background: $white;
		border-radius: 16px;
		padding: 2rem;
		box-shadow: 0 4px 20px $shadow;
		border: 1px solid rgba(255, 255, 255, 0.2);

		.table-header {
			margin-bottom: 2rem;
			padding-bottom: 1rem;
			border-bottom: 1px solid rgba($primary-color, 0.1);

			.table-title {
				h2 {
					font-size: 1.5rem;
					font-weight: 600;
					color: $text-color;
					margin: 0 0 0.5rem 0;
				}

				p {
					color: $text-light;
					margin: 0;
					font-size: 1rem;
				}
			}
		}

		.table-wrapper {
			// Customização da tabela Vue Good Table
			:deep(.vgt-table) {
				border: none;
				border-radius: 8px;
				overflow: hidden;

				th {
					background: linear-gradient(135deg, rgba($primary-color, 0.1) 0%, rgba($primary-color, 0.05) 100%);
					color: $text-color;
					font-weight: 600;
					padding: 1rem;
					border: none;
				}

				td {
					padding: 1rem;
					border-bottom: 1px solid rgba($primary-color, 0.1);
					vertical-align: middle;
				}

				tbody tr {
					transition: background-color 0.2s ease;

					&:hover {
						background-color: rgba($primary-color, 0.05);
					}
				}
			}

			:deep(.vgt-global-search) {
				margin-bottom: 1.5rem;

				.vgt-global-search__input {
					border: 2px solid rgba($primary-color, 0.2);
					border-radius: 8px;
					padding: 0.75rem 1rem;
					font-size: 1rem;
					transition: border-color 0.3s ease;

					&:focus {
						border-color: $primary-color;
						outline: none;
						box-shadow: 0 0 0 3px rgba($primary-color, 0.1);
					}
				}
			}

			:deep(.vgt-pagination) {
				margin-top: 1.5rem;
				padding-top: 1rem;
				border-top: 1px solid rgba($primary-color, 0.1);

				.vgt-pagination__page {
					background: transparent;
					border: 1px solid rgba($primary-color, 0.2);
					color: $primary-color;
					margin: 0 2px;
					border-radius: 6px;
					transition: all 0.3s ease;

					&:hover {
						background: rgba($primary-color, 0.1);
					}

					&.active {
						background: $primary-color;
						color: $white;
						border-color: $primary-color;
					}
				}
			}
		}
	}

	// Responsividade
	@media (max-width: 768px) {
		.properties-container {
			padding: 0.5rem;
		}

		.page-header {
			padding: 1.5rem;
			margin-bottom: 1.5rem;

			.header-content {
				flex-direction: column;
				text-align: center;
				gap: 1rem;
			}

			.header-info {
				flex-direction: column;
				gap: 1rem;
			}

			.header-icon {
				width: 60px;
				height: 60px;
				font-size: 1.8rem;
			}

			.header-text h1 {
				font-size: 2rem;
			}

			.header-text p {
				font-size: 1rem;
			}

			.header-actions .btn-primary {
				padding: 0.6rem 1.2rem;
				font-size: 0.9rem;
			}
		}

		.table-section {
			padding: 1.5rem;

			.table-wrapper {
				:deep(.vgt-table) {
					font-size: 0.9rem;

					th, td {
						padding: 0.75rem 0.5rem;
					}
				}
			}
		}

		.empty-card {
			padding: 3rem 2rem !important;

			.empty-icon {
				width: 80px !important;
				height: 80px !important;

				i {
					font-size: 2.5rem !important;
				}
			}

			.empty-content {
				h3 {
					font-size: 1.25rem !important;
				}

				.btn-primary.large {
					padding: 1rem 1.5rem !important;
					font-size: 1rem !important;
				}
			}
		}
	}

	@media (max-width: 480px) {
		.page-header .header-text h1 {
			font-size: 1.75rem;
		}

		.table-section {
			padding: 1rem;

			.table-wrapper {
				:deep(.vgt-table) {
					font-size: 0.8rem;

					th, td {
						padding: 0.5rem 0.25rem;
					}
				}
			}
		}
	}
}
</style>
