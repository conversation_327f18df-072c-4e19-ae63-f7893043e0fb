<template>
	<div class="properties-container">
		<!-- Header Principal -->
		<!-- ... rest of your template ... -->
	</div>
</template>

<script src="./Properties.js"></script>

<style lang="scss" scoped>
// Variáveis
$primary-color: #3d8160;
$primary-dark: #2d6047;
$text-color: #2c3e50;
$text-light: #6c757d;
$white: #ffffff;
$background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
$shadow: rgba(0, 0, 0, 0.1);
$shadow-hover: rgba(0, 0, 0, 0.15);

.properties-container {
	min-height: 100vh;
	background: $background;
	padding: 1rem;
	font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

	// Header Principal
	.page-header {
		background: $white;
		border-radius: 20px;
		padding: 2rem;
		margin-bottom: 2rem;
		box-shadow: 0 10px 30px $shadow;
		border: 1px solid rgba(255, 255, 255, 0.2);
		backdrop-filter: blur(10px);

		.header-content {
			display: flex;
			align-items: center;
			justify-content: space-between;
			gap: 1.5rem;
		}

		.header-icon {
			width: 70px;
			height: 70px;
			background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
			border-radius: 20px;
			display: flex;
			align-items: center;
			justify-content: center;
			color: $white;
			font-size: 2rem;
			box-shadow: 0 8px 25px rgba($primary-color, 0.3);
			transition: transform 0.3s ease;

			&:hover {
				transform: scale(1.05);
			}
		}

		.header-text {
			flex: 1;

			.page-title {
				font-size: 2.5rem;
				font-weight: 700;
				color: $text-color;
				margin: 0 0 0.5rem 0;
				background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
				background-clip: text;
			}

			.page-subtitle {
				font-size: 1.1rem;
				color: $text-light;
				margin: 0;
				font-weight: 400;
			}
		}

		.header-actions {
			.btn-primary {
				background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
				color: $white;
				border: none;
				padding: 0.75rem 1.5rem;
				border-radius: 12px;
				font-weight: 600;
				cursor: pointer;
				transition: all 0.3s ease;
				display: flex;
				align-items: center;
				gap: 0.5rem;
				box-shadow: 0 4px 15px rgba($primary-color, 0.3);
				text-decoration: none;

				&:hover {
					transform: translateY(-2px);
					box-shadow: 0 6px 20px rgba($primary-color, 0.4);
					color: $white;
				}

				i {
					font-size: 1rem;
				}
			}
		}
	}

	// Conteúdo Principal
	.main-content {
		display: flex;
		flex-direction: column;
		gap: 2rem;
	}

	// Loading State
	.loading-section {
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 400px;

		.loading-card {
			background: $white;
			border-radius: 16px;
			padding: 3rem;
			box-shadow: 0 4px 20px $shadow;
			text-align: center;

			p {
				margin-top: 1rem;
				color: $text-light;
				font-size: 1.1rem;
			}
		}
	}

	// Empty State
	.empty-state {
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 500px;

		.empty-card {
			background: $white;
			border-radius: 20px;
			padding: 4rem 3rem;
			box-shadow: 0 4px 20px $shadow;
			text-align: center;
			max-width: 500px;

			.empty-icon {
				width: 100px;
				height: 100px;
				background: linear-gradient(135deg, rgba($primary-color, 0.1) 0%, rgba($primary-color, 0.05) 100%);
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0 auto 2rem;

				i {
					font-size: 3rem;
					color: $primary-color;
				}
			}

			.empty-content {
				h3 {
					font-size: 1.5rem;
					font-weight: 600;
					color: $text-color;
					margin-bottom: 1rem;
				}

				p {
					color: $text-light;
					margin-bottom: 2rem;
					line-height: 1.6;
				}

				.btn-primary {
					background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
					color: $white;
					border: none;
					padding: 1rem 2rem;
					border-radius: 12px;
					font-weight: 600;
					cursor: pointer;
					transition: all 0.3s ease;
					display: inline-flex;
					align-items: center;
					gap: 0.75rem;
					box-shadow: 0 4px 15px rgba($primary-color, 0.3);
					text-decoration: none;
					font-size: 1.1rem;

					&:hover {
						transform: translateY(-2px);
						box-shadow: 0 6px 20px rgba($primary-color, 0.4);
						color: $white;
					}

					&.large {
						padding: 1.25rem 2.5rem;
						font-size: 1.2rem;
					}

					i {
						font-size: 1.2rem;
					}
				}
			}
		}
	}

	// Table Section
	.table-section {
		background: $white;
		border-radius: 16px;
		padding: 2rem;
		box-shadow: 0 4px 20px $shadow;
		border: 1px solid rgba(255, 255, 255, 0.2);

		.table-header {
			margin-bottom: 2rem;
			padding-bottom: 1rem;
			border-bottom: 1px solid rgba($primary-color, 0.1);

			.table-title {
				h2 {
					font-size: 1.5rem;
					font-weight: 600;
					color: $text-color;
					margin: 0 0 0.5rem 0;
				}

				p {
					color: $text-light;
					margin: 0;
					font-size: 1rem;
				}
			}
		}

		.table-wrapper {
			// Customização da tabela Vue Good Table
			:deep(.vgt-table) {
				border: none;
				border-radius: 8px;
				overflow: hidden;

				th {
					background: linear-gradient(135deg, rgba($primary-color, 0.1) 0%, rgba($primary-color, 0.05) 100%);
					color: $text-color;
					font-weight: 600;
					padding: 1rem;
					border: none;
				}

				td {
					padding: 1rem;
					border-bottom: 1px solid rgba($primary-color, 0.1);
					vertical-align: middle;
				}

				tbody tr {
					transition: background-color 0.2s ease;

					&:hover {
						background-color: rgba($primary-color, 0.05);
					}
				}
			}

			:deep(.vgt-global-search) {
				margin-bottom: 1.5rem;

				.vgt-global-search__input {
					border: 2px solid rgba($primary-color, 0.2);
					border-radius: 8px;
					padding: 0.75rem 1rem;
					font-size: 1rem;
					transition: border-color 0.3s ease;

					&:focus {
						border-color: $primary-color;
						outline: none;
						box-shadow: 0 0 0 3px rgba($primary-color, 0.1);
					}
				}
			}

			:deep(.vgt-pagination) {
				margin-top: 1.5rem;
				padding-top: 1rem;
				border-top: 1px solid rgba($primary-color, 0.1);

				.vgt-pagination__page {
					background: transparent;
					border: 1px solid rgba($primary-color, 0.2);
					color: $primary-color;
					margin: 0 2px;
					border-radius: 6px;
					transition: all 0.3s ease;

					&:hover {
						background: rgba($primary-color, 0.1);
					}

					&.active {
						background: $primary-color;
						color: $white;
						border-color: $primary-color;
					}
				}
			}
		}
	}

	// Responsividade
	@media (max-width: 768px) {
		.properties-container {
			padding: 0.5rem;
		}

		.page-header {
			padding: 1.5rem;
			margin-bottom: 1.5rem;

			.header-content {
				flex-direction: column;
				text-align: center;
				gap: 1rem;
			}

			.header-icon {
				width: 60px;
				height: 60px;
				font-size: 1.8rem;
			}

			.page-title {
				font-size: 2rem;
			}

			.page-subtitle {
				font-size: 1rem;
			}

			.header-actions .btn-primary {
				padding: 0.6rem 1.2rem;
				font-size: 0.9rem;
			}
		}

		.table-section {
			padding: 1.5rem;

			.table-wrapper {
				:deep(.vgt-table) {
					font-size: 0.9rem;

					th, td {
						padding: 0.75rem 0.5rem;
					}
				}
			}
		}

		.empty-card {
			padding: 3rem 2rem !important;

			.empty-icon {
				width: 80px !important;
				height: 80px !important;

				i {
					font-size: 2.5rem !important;
				}
			}

			.empty-content {
				h3 {
					font-size: 1.25rem !important;
				}

				.btn-primary.large {
					padding: 1rem 1.5rem !important;
					font-size: 1rem !important;
				}
			}
		}
	}

	@media (max-width: 480px) {
		.page-header .page-title {
			font-size: 1.75rem;
		}

		.table-section {
			padding: 1rem;

			.table-wrapper {
				:deep(.vgt-table) {
					font-size: 0.8rem;

					th, td {
						padding: 0.5rem 0.25rem;
					}
				}
			}
		}
	}
}
</style>
