<template>
	<div class="properties-container">
		<!-- Header Principal -->
		<div class="page-header">
			<div class="header-content">
				<div class="header-icon">
					<i class="fas fa-warehouse"></i>
				</div>
				<div class="header-text">
					<h1 class="page-title">Propriedades</h1>
					<p class="page-subtitle">Gere<PERSON>ie suas propriedades rurais e acompanhe o desenvolvimento</p>
				</div>
				<div class="header-actions" v-if="properties.length > 0">
					<div v-if="userRole === 'pre-registered'">
						<router-link
							tag="button"
							class="btn-primary"
							v-bind:to="{
								path: '/panel/add-property',
								query: { nextArea: `${totalProperties}` },
							}"
						>
							<i class="fas fa-plus"></i>
							<span>Registrar Propriedade</span>
						</router-link>
					</div>
					<div v-else>
						<button
							class="btn-primary"
							@click="addModal()"
						>
							<i class="fas fa-plus"></i>
							<span>Registrar Propriedade</span>
						</button>
					</div>
				</div>
			</div>
		</div>

		<!-- Conteúdo Principal -->
		<div class="main-content">
			<!-- Loading State -->
			<div v-if="loading" class="loading-section">
				<div class="loading-card">
					<loader :loading="loading"></loader>
					<p>Carregando propriedades...</p>
				</div>
			</div>

			<!-- Empty State -->
			<div v-else-if="!loading && !properties.length > 0" class="empty-state">
				<div class="empty-card">
					<div class="empty-icon">
						<i class="fas fa-warehouse"></i>
					</div>
					<div class="empty-content">
						<h3>Nenhuma propriedade encontrada</h3>
						<p>Registre sua primeira propriedade para começar a gerenciar seus dados rurais</p>
						<router-link
							class="btn-primary large"
							tag="button"
							to="/panel/add-property"
						>
							<i class="fas fa-plus"></i>
							<span>Registrar Primeira Propriedade</span>
						</router-link>
					</div>
				</div>
			</div>

			<!-- Properties Table - Mantendo estrutura original -->
			<div v-else class="properties-table-modern">
				<vue-good-table
					title="Propriedades"
					:columns="fields"
					:rows="properties"
					:search-options="{
						enabled: true,
						placeholder: 'Pesquisar...',
					}"
					:pagination-options="paginationOptions"
					:sort-options="{
						enabled: true,
						initialSortBy: { field: 'name', type: 'desc' },
					}"
				>
					<div slot="emptystate">
						Não há propriedades registradas.
					</div>
					<template slot="table-row" slot-scope="props">
						<span v-if="props.column.field == 'name'">
							{{ props.row.name }}
						</span>
						<span v-if="props.column.field == 'owner_name'">
							{{ props.row.owner_name }}
						</span>
						<span v-if="props.column.field == 'city'">
							{{ props.row.city }}
						</span>
						<span v-if="props.column.field == 'uf'">
							{{ props.row.uf }}
						</span>
						<span v-if="props.column.field == 'description'">
							{{ props.row.description || "-" }}
						</span>
						<span
							v-if="props.column.field == 'status'"
							v-html="checkStatus(props.row.status)"
						>
						</span>
						<div v-if="props.column.field == 'actions'">
							<div class="d-flex">
								<router-link
									v-if="userRole === 'pre-registered'"
									title="Resumo"
									tag="button"
									class="btn btn-md btn-secondary"
									style="margin: 0.2rem"
									v-bind:to="{
										path: '/panel/overview',
										query: { propid: props.row.id },
									}"
									:disabled="props.row.status === 0"
								>
									<i class="fas fa-file fa-sm"> </i>
								</router-link>

								<button
									title="Editar propriedade"
									class="btn btn-md btn-info"
									style="margin: 0.2rem"
									@click="editModal(props.row)"
									:disabled="props.row.status === 0"
								>
									<i class="fas fa-edit fa-sm"></i>
								</button>

								<button
									title="Excluir propriedade"
									class="btn btn-md btn-danger"
									style="margin: 0.2rem"
									@click="deleteProperty(props.row)"
									:disabled="props.row.status === 0"
								>
									<i class="fas fa-trash fa-sm"></i>
								</button>

								<button
									title="Exportar Coletas"
									class="btn btn-md btn-warning"
									style="
										margin: 0.2rem;
										background-color: #55a97f;
										border-color: #55a97f;
									"
									@click="addModalExport(props.row)"
								>
									<i class="fas fa-file-excel"></i>
								</button>

								<button
									title="Exportar Dados da Prática"
									class="btn btn-md btn-success"
									style="
										margin: 0.2rem;
										background-color: #65350F;
										border-color: #65350F;
									"
									@click="exportPractice(props.row.id)"
									:disabled="xlsLoading === props.row.id"
								>
									<span
										v-if="xlsLoading === props.row.id"
										class="spinner-border spinner-border-sm"
										role="status"
										aria-hidden="true"
									></span>
									<i
										v-else
										class="fas fa-file-excel"
									></i>
								</button>

								<template >
									<form
										@submit.prevent="
											exportPropertyPdf(props.row.id)
										"
									>
										<button
											title="Exportar Dados da Propriedade"
											type="submit"
											class="btn btn-md btn-danger"
											style="
													margin: 0.2rem;
													background-color: #b31a38;
													border-color: #b31a38;
												"
											:disabled="
												pdfLoading === props.row.id
											"
										>
											<span
												v-if="
													pdfLoading === props.row.id
												"
												class="spinner-border spinner-border-sm"
												role="status"
												aria-hidden="true"
											></span>
											<i
												v-else
												class="fas fa-file-pdf"
											></i>
										</button>
									</form>
								</template>
								<!--
								<router-link
									v-if="userRole === 'pre-registered'"
									:title="props.row.area_name"
									tag="button"
									class="btn btn-md btn-success"
									style="margin: 0.2rem"
									v-bind:to="{
										path: '/panel/blocks',
										query: { propid: props.row.id },
									}"
									:disabled="props.row.status === 0"
								>
									<i class="fas fa-eye fa-sm"> </i>
								</router-link>
								-->
								<router-link
									v-if="
										userRole === 'pre-registered' &&
										props.row.total_homogeneous_areas > 0
									"
									title="Áreas Homogêneas"
									tag="button"
									class="btn btn-md btn-success"
									style="margin: 0.2rem"
									v-bind:to="{
										path: '/panel/homogeneous-area',
										query: { propid: props.row.id },
									}"
									:disabled="props.row.status === 0"
								>
									<i class="fas fa-layer-group fa-sm"> </i>
								</router-link>

								<router-link
									v-if="userRole !== 'pre-registered'"
									:title="props.row.area_name"
									tag="button"
									class="btn btn-md btn-success"
									style="margin: 0.2rem"
									v-bind:to="{
										path: '/panel/homogeneous-area',
										query: { propid: props.row.id },
									}"
									:disabled="props.row.status === 0"
								>
									<i class="fas fa-layer-group fa-sm"> </i>
								</router-link>
							</div>
						</div>
					</template>
				</vue-good-table>
			</div>
		</div>

		<!-- Modais mantidos exatamente como estão -->
		<div
			class="modal fade"
			id="modalProperties"
				ref="modal"
				tabindex="-1"
				role="dialog"
				aria-labelledby="modalPropertiesLabel"
				aria-hidden="true"
			>
				<div class="modal-dialog modal-dialog-centered" role="document">
					<div class="modal-content">
						<div class="modal-header">
							<h5
								class="modal-title"
								id="modalPropertiesLabel"
								v-show="!editMode"
							>
								ADICIONAR PROPRIEDADE
							</h5>
							<h5
								class="modal-title"
								id="modalPropertiesLabel"
								v-show="editMode"
							>
								ATUALIZAR PROPRIEDADE
							</h5>
							<button
								type="button"
								class="close"
								data-dismiss="modal"
								aria-label="Close"
							>
								<span aria-hidden="true">&times;</span>
							</button>
						</div>
						<div class="modal-body">
							<form
								@submit.prevent="editMode ? update() : store()"
							>
								<div class="form-row">
									<div class="form-group col-md-6">
										<label for="propertyName">
											Nome da Propriedade
										</label>
										<input
											id="propertyName"
											type="text"
											class="form-control"
											v-model="property.name"
										/>
									</div>
									<div class="form-group col-md-6">
										<label for="propertyArea">
											Área em hectares
										</label>
										<input
											id="propertyArea"
											type="number"
											class="form-control"
											v-model="property.area"
										/>
									</div>
								</div>

								<div class="form-group">
									<label for="propertyOwner">
										Nome do Proprietário
									</label>
									<input
										type="text"
										class="form-control"
										v-model="property.owner_name"
									/>
								</div>

								<div v-if="!editMode" class="form-row">
									<div class="form-group col-md-4">
										<label for="state">Estado</label>
										<select
											id="state"
											name="selectState"
											class="form-control"
											v-model="property.uf"
											@change="getCities($event)"
										>
											<option
												v-for="state in states"
												:value="state.sigla"
												:key="state.id"
											>
												{{ state.nome }}
											</option>
										</select>
									</div>

									<div class="form-group col-md-8">
										<label for="propertyCity">
											Município
										</label>
										<div
											v-if="
												!cities.length || loadingCities
											"
										>
											<span
												v-if="!loadingCities"
												class="text-danger"
											>
												Selecione um Estado
											</span>
											<span v-else>
												Carregando cidades...
											</span>
										</div>
										<div v-else>
											<select
												:id="'propertyCity'"
												name="city"
												class="form-control"
												v-model="property.city"
												:disabled="editMode"
											>
												<option
													v-for="city in cities"
													v-bind:value="city.nome"
													v-bind:key="city.id"
												>
													{{ city.nome }}
												</option>
											</select>
										</div>
									</div>
								</div>

								<div v-else class="form-row">
									<div class="form-group col-md-4">
										<label for="state">Estado</label>
										<input
											id="state"
											type="text"
											class="form-control"
											v-model="property.uf"
											disabled
										/>
									</div>
									<div class="form-group col-md-8">
										<label for="city">Munícipio</label>
										<input
											id="city"
											type="text"
											class="form-control"
											v-model="property.city"
											disabled
										/>
									</div>
								</div>

								<div
									class="form-row"
									v-if="property.geolocation !== null"
								>
									<div class="form-group col-md-6">
										<label for="latitude">Latitude</label>
										<input
											type="number"
											class="form-control"
											id="latitude"
											step="0.0000001"
											pattern="^(\+|-)?(?:100(?:(?:\.0{1,7})?)|(?:[0-9]|[1-8][0-9])(?:(?:\.[0-9]{1,7})?))$"
											v-model="
												property.geolocation.latitude
											"
											required
										/>
									</div>
									<div class="form-group col-md-6">
										<label for="longitude">Longitude</label>
										<input
											type="number"
											class="form-control"
											id="longitude"
											step="0.0000001"
											pattern="^(\+|-)?(?:180(?:(?:\.0{1,7})?)|(?:[0-9]|[1-9][0-9]|1[0-7][0-9])(?:(?:\.[0-9]{1,7})?))$"
											v-model="
												property.geolocation.longitude
											"
											required
										/>
									</div>
								</div>

								<div class="form-group">
									<label for="propertyDescript"
										>Descrição</label
									>
									<textarea
										id="propertyDescript"
										row="2"
										class="form-control"
										v-model="property.description"
									></textarea>
								</div>

								<div class="modal-footer">
									<button
										type="button"
										class="btn btn-secondary"
										data-dismiss="modal"
									>
										Sair
									</button>
									<button
										type="submit"
										class="btn btn-primary"
										v-show="!editMode"
									>
										Cadastrar
									</button>
									<button
										type="submit"
										class="btn btn-primary"
										v-show="editMode"
									>
										Atualizar
									</button>
								</div>
							</form>
						</div>
					</div>
				</div>
			</div>

			<div
					class="modal fade"
					id="modalPropertiesExport"
					ref="modal"
					tabindex="-1"
					role="dialog"
				>
					<div
						class="modal-dialog modal-dialog-centered"
						role="document"
					>
						<div class="modal-content">
							<div class="modal-header">
								<h5 class="modal-title">EXPORTAÇÃO DE DADOS</h5>
								<button
									type="button"
									class="close"
									data-dismiss="modal"
									aria-label="Close"
								>
									<span aria-hidden="true">&times;</span>
								</button>
							</div>
							<div class="modal-body">
								<form @submit.prevent="handleExport">
									<div class="form-row">
										<div class="form-group col-md-12">
											<label for="datePeriod"
												>Selecione o período</label
											>
											<select
												id="datePeriod"
												class="form-control"
												v-model="selectedDatePeriod"
											>
												<option
													v-if="
														datePeriods.length === 0
													"
												>
													Não há visitas cadastradas
												</option>
												<option
													v-for="date in datePeriods"
													:key="date"
													:value="date"
												>
													{{ date }}
												</option>
											</select>
										</div>
									</div>
									<div class="modal-footer">
										<button
											type="button"
											class="btn btn-secondary"
											data-dismiss="modal"
										>
											Sair
										</button>
										<button
											type="submit"
											class="btn btn-primary"
											:disabled="
												datePeriods.length === 0 ||
												xlsLoading === exportProperty.id
											"
										>
											<span
												v-if="
													xlsLoading &&
													xlsLoading ===
														exportProperty.id
												"
												class="spinner-border spinner-border-sm"
												role="status"
												aria-hidden="true"
											></span>
											<span
												v-if="
													xlsLoading &&
													xlsLoading ===
														exportProperty.id
												"
												>Carregando...</span
											>
											<span v-else
												>Exportar para XLS</span
											>
										</button>
									</div>
								</form>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script src="./Properties.js"></script>

<style lang="scss" scoped>
// Variáveis
$primary-color: #3d8160;
$primary-dark: #2d6047;
$text-color: #2c3e50;
$text-light: #6c757d;
$white: #ffffff;
$background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
$shadow: rgba(0, 0, 0, 0.1);
$shadow-hover: rgba(0, 0, 0, 0.15);

// Container Principal
.properties-container {
	min-height: 100vh;
	background: $background;
	padding: 2rem;
	font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

	@media (max-width: 768px) {
		padding: 1rem;
	}
}

// Header Section
.page-header {
	background: $white;
	border-radius: 20px;
	padding: 2.5rem;
	margin-bottom: 2rem;
	box-shadow: 0 8px 25px $shadow;
	border: 1px solid rgba(255, 255, 255, 0.2);
	backdrop-filter: blur(10px);
	transition: transform 0.3s ease, box-shadow 0.3s ease;

	&:hover {
		transform: translateY(-2px);
		box-shadow: 0 12px 35px $shadow-hover;
	}

	.header-content {
		display: flex;
		align-items: center;
		gap: 2rem;

		@media (max-width: 768px) {
			flex-direction: column;
			text-align: center;
			gap: 1.5rem;
		}
	}

	.header-icon {
		width: 70px;
		height: 70px;
		background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
		border-radius: 20px;
		display: flex;
		align-items: center;
		justify-content: center;
		color: $white;
		font-size: 2rem;
		box-shadow: 0 8px 25px rgba($primary-color, 0.3);
		transition: transform 0.3s ease;

		&:hover {
			transform: scale(1.05);
		}
	}

	.header-text {
		flex: 1;

		.page-title {
			font-size: 2.5rem;
			font-weight: 700;
			color: $primary-color;
			margin: 0 0 0.5rem 0;
		}

		.page-subtitle {
			font-size: 1.1rem;
			color: $text-light;
			margin: 0;
			font-weight: 400;
		}
	}

	.header-actions {
		display: flex;
		gap: 1rem;

		@media (max-width: 768px) {
			flex-direction: column;
			width: 100%;
		}
	}
}

// Main Content
.main-content {
	display: flex;
	flex-direction: column;
	gap: 2rem;
}

// Loading Section
.loading-section {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 400px;

	.loading-card {
		background: $white;
		border-radius: 20px;
		padding: 3rem;
		text-align: center;
		box-shadow: 0 8px 25px $shadow;
		border: 1px solid rgba(255, 255, 255, 0.2);

		p {
			margin-top: 1rem;
			color: $text-light;
			font-size: 1.1rem;
		}
	}
}

// Empty State
.empty-state {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 400px;

	.empty-card {
		background: $white;
		border-radius: 20px;
		padding: 3rem;
		text-align: center;
		box-shadow: 0 8px 25px $shadow;
		border: 1px solid rgba(255, 255, 255, 0.2);
		max-width: 500px;

		.empty-icon {
			width: 100px;
			height: 100px;
			background: linear-gradient(135deg, rgba($primary-color, 0.1) 0%, rgba($primary-color, 0.05) 100%);
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 0 auto 2rem;

			i {
				font-size: 3rem;
				color: $primary-color;
			}
		}

		.empty-content {
			h3 {
				font-size: 1.5rem;
				font-weight: 600;
				color: $text-color;
				margin: 0 0 1rem 0;
			}

			p {
				color: $text-light;
				margin: 0 0 2rem 0;
				font-size: 1rem;
				line-height: 1.6;
			}
		}
	}
}

// Tabela - Mantendo funcionalidades, modernizando visual
.properties-table-modern {
	background: $white;
	border-radius: 20px;
	box-shadow: 0 8px 25px $shadow;
	padding: 2rem;
	transition: transform 0.3s ease;

	&:hover {
		transform: translateY(-2px);
	}
}

// Botões
.btn-primary {
	background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
	border: none;
	border-radius: 12px;
	padding: 0.75rem 1.5rem;
	font-weight: 600;
	font-size: 0.95rem;
	color: $white;
	display: flex;
	align-items: center;
	gap: 0.5rem;
	transition: all 0.3s ease;
	box-shadow: 0 4px 15px rgba($primary-color, 0.3);
	text-decoration: none;

	&:hover {
		transform: translateY(-2px);
		box-shadow: 0 6px 20px rgba($primary-color, 0.4);
		color: $white;
		text-decoration: none;
	}

	&:active {
		transform: translateY(0);
	}

	&.large {
		padding: 1rem 2rem;
		font-size: 1.1rem;
		border-radius: 15px;
	}

	i {
		font-size: 1rem;
	}
}

// Responsividade
@media (max-width: 768px) {
	.properties-container {
		padding: 1rem;
	}

	.page-header {
		padding: 1.5rem;
		margin-bottom: 1rem;

		.header-content {
			flex-direction: column;
			text-align: center;
			gap: 1rem;
		}

		.header-text .page-title {
			font-size: 2rem;
		}

		.header-actions {
			width: 100%;
			flex-direction: column;
		}
	}

	.properties-table-modern {
		padding: 1rem;
		margin: 10px 0;
	}

	.btn-primary {
		width: 100%;
		justify-content: center;
	}
}

@media (max-width: 480px) {
	.page-header .header-text .page-title {
		font-size: 1.75rem;
	}

	.empty-card {
		padding: 2rem 1rem;
	}

	.loading-card {
		padding: 2rem 1rem;
	}
}
</style>
