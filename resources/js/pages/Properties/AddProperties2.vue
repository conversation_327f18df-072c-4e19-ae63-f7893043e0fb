<template>
	<div class="propertiesWrapper">
		<div class="container">
			<form @submit.prevent="onSubmit">
				<h1 class="text-center mb-2">Cadastro de {{ areaNameCapitalized }}s</h1>
				<p class="subtitle text-center mb-4">Informe os dados das {{ areaNameLowercase }}s da sua propriedade</p>

				<div class="form-section">
					<h2 class="section-title">Quantidade de {{ areaNameCapitalized }}s</h2>
					<div class="row">
						<div class="col-md-6 mb-3">
							<label class="form-label">Número de {{ areaNameCapitalized }}s a Cadastrar</label>
							<input
								type="number"
								class="form-control"
								v-model="numBlocks"
								min="1"
								max="10"
								placeholder="Ex: 3"
								required
							/>
						</div>
					</div>
				</div>

				<div v-if="formData.length > 0" class="properties-table px-5">
					<div class="table-card">
						<h2 class="table-title mb-3"><PERSON><PERSON><PERSON> das {{ areaNameCapitalized }}s</h2>

						<!-- <PERSON><PERSON> das Roças -->
						<div class="roca-tabs-container mb-4">
							<div class="roca-tabs">
								<button
									v-for="(roca, index) in formData"
									:key="index"
									class="roca-tab"
									:class="{ active: activeBlockIndex === index }"
									@click="setActiveRoca(index)"
								>
									<i class="fas fa-seedling"></i>
									{{ areaNameCapitalized }} {{ index + 1 }}
								</button>
							</div>
						</div>

						<!-- Conteúdo da Roça Ativa -->
						<div v-if="formData[activeBlockIndex]" class="roca-details">
							<div class="roca-header mb-3">
								<div class="roca-badge">{{ areaNameCapitalized }} {{ activeBlockIndex + 1 }}</div>
							</div>

							<!-- Seção 1: Informações Básicas -->
							<div class="form-section-inner mb-4">
								<h3 class="section-title">Informações Básicas</h3>
								<div class="row">
									<div class="col-md-6 mb-3">
										<label class="form-label">Área em hectares</label>
										<input
											type="number"
											class="form-control"
											v-model="formData[activeBlockIndex].area"
											min="1"
											step="0.01"
											placeholder="Ex: 2.5"
											required
										/>
									</div>
								</div>
							</div>

							<!-- Seção 2: Genótipos -->
							<div class="form-section-inner">
								<h3 class="section-title">Genótipos</h3>
								<p class="mb-3">Informe a participação de cada genótipo na área total da roça (a soma deve ser 100%)</p>

								<!-- Tabela de genótipos -->
								<div class="table-responsive">
									<table class="table table-borderless">
										<thead class="thead-light">
											<tr>
												<th>Genótipo</th>
												<th>Participação (%)</th>
												<th>Tipo</th>
												<th>Idade (anos)</th>
												<th>Densidade (plantas/ha)</th>
											</tr>
										</thead>
										<tbody>
											<!-- Comum -->
											<tr>
												<th class="genotype">Comum</th>
												<td>
													<input
														type="number"
														class="form-control"
														min="0"
														max="100"
														v-model="formData[activeBlockIndex].genotype.common.participation"
														@change="checkGenotypeParticipation(activeBlockIndex)"
													/>
												</td>
												<td>
													<select
														class="form-control"
														v-model="formData[activeBlockIndex].genotype.common.type"
														:disabled="!formData[activeBlockIndex].genotype.common.participation"
													>
														<option value="1">Forasteiro</option>
														<option value="2">Crioulo</option>
														<option value="3">Trinitário</option>
													</select>
												</td>
												<td>
													<input
														type="number"
														class="form-control"
														min="0"
														v-model="formData[activeBlockIndex].genotype.common.age"
														:disabled="!formData[activeBlockIndex].genotype.common.participation"
													/>
												</td>
												<td>
													<input
														type="number"
														class="form-control"
														min="0"
														v-model="formData[activeBlockIndex].genotype.common.density"
														:disabled="!formData[activeBlockIndex].genotype.common.participation"
													/>
												</td>
											</tr>

											<!-- Híbrido -->
											<tr>
												<th class="genotype">Híbrido</th>
												<td>
													<input
														type="number"
														class="form-control"
														min="0"
														max="100"
														v-model="formData[activeBlockIndex].genotype.hybrid.participation"
														@change="checkGenotypeParticipation(activeBlockIndex)"
													/>
												</td>
												<td>
													<span class="text-muted">N/A</span>
												</td>
												<td>
													<input
														type="number"
														class="form-control"
														min="0"
														v-model="formData[activeBlockIndex].genotype.hybrid.age"
														:disabled="!formData[activeBlockIndex].genotype.hybrid.participation"
													/>
												</td>
												<td>
													<input
														type="number"
														class="form-control"
														min="0"
														v-model="formData[activeBlockIndex].genotype.hybrid.density"
														:disabled="!formData[activeBlockIndex].genotype.hybrid.participation"
													/>
												</td>
											</tr>

											<!-- Clonado -->
											<tr>
												<th class="genotype">Clonado</th>
												<td>
													<input
														type="number"
														class="form-control"
														min="0"
														max="100"
														v-model="formData[activeBlockIndex].genotype.cloned.participation"
														@change="checkGenotypeParticipation(activeBlockIndex)"
													/>
												</td>
												<td>
													<select
														class="form-control"
														v-model="formData[activeBlockIndex].genotype.cloned.type"
														:disabled="!formData[activeBlockIndex].genotype.cloned.participation"
													>
														<option value="1">CCN-10</option>
														<option value="2">CCN-51</option>
														<option value="3">CEPEC 2204</option>
														<option value="4">PH 09</option>
														<option value="5">PH 16</option>
														<option value="6">IPIANGA 01</option>
														<option value="7">BN 34</option>
														<option value="8">PS 1319</option>
													</select>
												</td>
												<td>
													<input
														type="number"
														class="form-control"
														min="0"
														v-model="formData[activeBlockIndex].genotype.cloned.age"
														:disabled="!formData[activeBlockIndex].genotype.cloned.participation"
													/>
												</td>
												<td>
													<input
														type="number"
														class="form-control"
														min="0"
														v-model="formData[activeBlockIndex].genotype.cloned.density"
														:disabled="!formData[activeBlockIndex].genotype.cloned.participation"
													/>
												</td>
											</tr>
										</tbody>
									</table>
								</div>

								<div v-if="formData[activeBlockIndex].genotypeError" class="alert alert-danger mt-2">
									{{ formData[activeBlockIndex].genotypeError }}
								</div>
							</div>
						</div>

						<div class="action-buttons mt-5">
							<button type="button" class="btn btn-outline-secondary me-3" @click="cancelar">
								<i class="fas fa-times me-2"></i>Cancelar
							</button>
							<button
								type="submit"
								class="btn btn-agro btn-add"
								:disabled="isSubmitting"
							>
								<i class="fas fa-check me-2"></i>
								{{ isSubmitting ? 'Cadastrando...' : `Cadastrar ${areaNameCapitalized}s` }}
							</button>
						</div>
					</div>
				</div>
			</form>
		</div>
	</div>
</template>

<script>
export default {
	data() {
		return {
			authToken: null,
			numBlocks: 0,
			formData: [],
			isSubmitting: false,
			propertyId: this.$route.query.propid || null,
			areaName: this.$route.query.area_name || 'Bloco',
			activeBlockIndex: 0,
		};
	},

	computed: {
		areaNameCapitalized() {
			return this.areaName.charAt(0).toUpperCase() + this.areaName.slice(1);
		},
		areaNameLowercase() {
			return this.areaName.toLowerCase();
		},
	},

	created() {
		this.authToken = window.token;

		if (!this.propertyId) {
			console.error("ID da propriedade não encontrado na URL");
		}
	},

	watch: {
		numBlocks(newVal) {
			if (newVal > 0) {
				this.formData = Array.from({ length: newVal }, (_, index) => ({
					label: `${this.areaNameCapitalized} ${index + 1}`,
					area: null,

					// Genótipos preenchidos pelo usuário
					genotype: {
						common: { participation: 0, type: "1", age: 0, density: 0 },
						hybrid: { participation: 0, age: 0, density: 0 },
						cloned: { participation: 0, type: "1", age: 0, density: 0 },
					},
					genotypeError: null,

					// Campos fixos com valores padrão
					relief: "1",
					altitude: "1",
					production: { temple: 0, main: 0, total: 0, not_informed: 1 },
					handling: { temple: "1", main: "1" },
					soilUse: { type: "1", description: "" },
					rainfall: {
						january: "0", february: "0", march: "0", april: "0", may: "0",
						june: "0", july: "0", august: "0", september: "0", october: "0",
						november: "0", december: "0", unknown: "1"
					},
					geolocation: { latitude: null, longitude: null },
					soilClass: {
						lowland: { argisol: 0, latosol: 0, cambisol: 0, gleisol: 0, other: 100 },
						lowerThird: { argisol: 0, latosol: 0, cambisol: 0, gleisol: 0, other: 100 },
						middleThird: { argisol: 0, latosol: 0, cambisol: 0, gleisol: 0, other: 100 },
						upperThird: { argisol: 0, latosol: 0, cambisol: 0, gleisol: 0, other: 100 },
					}
				}));
				this.activeBlockIndex = 0;
			} else {
				this.formData = [];
				this.activeBlockIndex = 0;
			}
		}
	},

	methods: {
		setActiveBlock(index) {
			this.activeBlockIndex = index;
		},

		checkGenotypeParticipation(index) {
			const b = this.formData[index];
			const common = parseInt(b.genotype.common.participation) || 0;
			const hybrid = parseInt(b.genotype.hybrid.participation) || 0;
			const cloned = parseInt(b.genotype.cloned.participation) || 0;
			const total = common + hybrid + cloned;

			if (total !== 100) {
				b.genotypeError = `A soma das participações deve ser 100%. Atualmente: ${total}%`;
			} else {
				b.genotypeError = null;
			}

			// Resetar campos se participação for zero
			if (common === 0) b.genotype.common = { participation: 0, type: null, age: null, density: null };
			if (hybrid === 0) b.genotype.hybrid = { participation: 0, age: null, density: null };
			if (cloned === 0) b.genotype.cloned = { participation: 0, type: null, age: null, density: null };
		},

		validateForm() {
			let isValid = true;
			this.formData.forEach((b, i) => {
				if (!b.area) isValid = false;
				this.checkGenotypeParticipation(i);
				if (b.genotypeError) isValid = false;
			});
			return isValid;
		},

		async onSubmit() {
			if (!this.validateForm()) {
				Swal.fire({
					title: "Formulário inválido",
					text: "Verifique as áreas e genótipos.",
					icon: "error"
				});
				return;
			}

			if (!this.propertyId) {
				Swal.fire({
					title: "Erro",
					text: "ID da propriedade não encontrado.",
					icon: "error"
				});
				return;
			}

			this.isSubmitting = true;

			try {
				const blocksData = this.formData.map(b => ({
					label: b.label,
					area: b.area,
					relief: b.relief,
					altitude: b.altitude,
					propertyId: this.propertyId,
					production: b.production,
					handling: b.handling,
					soilUse: b.soilUse,
					rainfall: b.rainfall,
					geolocation: b.geolocation,
					soilClass: b.soilClass,
					genotypes: {
						common: {
							total_area_participation: parseInt(b.genotype.common.participation) || 0,
							type: b.genotype.common.type,
							age: parseInt(b.genotype.common.age) || 0,
							density: parseInt(b.genotype.common.density) || 0,
							main: 50, temple: 50, global: 100,
						},
						hybrid: {
							total_area_participation: parseInt(b.genotype.hybrid.participation) || 0,
							age: parseInt(b.genotype.hybrid.age) || 0,
							density: parseInt(b.genotype.hybrid.density) || 0,
							main: 50, temple: 50, global: 100,
						},
						cloned: {
							total_area_participation: parseInt(b.genotype.cloned.participation) || 0,
							type: b.genotype.cloned.type,
							age: parseInt(b.genotype.cloned.age) || 0,
							density: parseInt(b.genotype.cloned.density) || 0,
							main: 50, temple: 50, global: 100,
						}
					}
				}));

				// Enviar cada bloco individualmente
				await Promise.all(
					blocksData.map((block) =>
						axios.post("/api/v1/blocks", block, {
							headers: { Authorization: `Bearer ${this.authToken}` },
						})
					)
				);

				// Agora chamar a clusterização
				await axios.get(`/api/v1/blocks-clustering?propid=${this.propertyId}`, {
					headers: { Authorization: `Bearer ${this.authToken}` },
				});

				Swal.fire({
					title: "Blocos cadastrados e agrupados!",
					text: "As áreas homogêneas foram geradas com sucesso.",
					icon: "success",
					timer: 5000,
					showConfirmButton: false
				});

				setTimeout(() => {
					this.$router.push(`/panel/homogeneous-area?propid=${this.propertyId}`);
				}, 5000);

				this.numBlocks = 0;
				this.formData = [];
			} catch (error) {
				console.error("Erro:", error.response?.data || error.message);
				Swal.fire({
					title: "Erro no cadastro ou clusterização",
					text: "Tente novamente mais tarde.",
					icon: "error"
				});
			} finally {
				this.isSubmitting = false;
			}
		},


		cancelar() {
			Swal.fire({
				title: "Cancelar cadastro?",
				text: "Todos os dados preenchidos serão perdidos.",
				icon: "warning",
				showCancelButton: true,
				confirmButtonText: "Sim, cancelar",
				cancelButtonText: "Não, continuar"
			}).then((result) => {
				if (result.isConfirmed) {
					this.numBlocks = 0;
					this.formData = [];
					this.$router.go(-1);
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.propertiesWrapper {
	background-color: #f5f8fd;
	border-radius: 20px;
	height: auto;
	max-height: 90vh;
	overflow-y: auto;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);

	.admin-content {
		min-height: 80vh;
		height: auto;
		overflow: visible;
		padding: 20px 0;
	}

	.header-section {
		text-align: center;
		padding: 20px 20px 25px;
		border-bottom: 1px solid rgba(61, 129, 96, 0.2);
		background: linear-gradient(to right, rgba(61, 129, 96, 0.05), rgba(61, 129, 96, 0.1), rgba(61, 129, 96, 0.05));
	}

	h1 {
		color: #135D2A;
		font-family: "Lexend", sans-serif;
		font-weight: 600;
		font-size: 28px;
		margin-bottom: 10px;
		text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);
	}

	.subtitle {
		color: #476D4C;
		font-size: 16px;
		font-weight: 400;
	}

	.form-section {
		background-color: white;
		border-radius: 15px;
		padding: 30px;
		margin: 25px 50px;
		box-shadow: 0 3px 15px rgba(0, 0, 0, 0.05);
		border-left: 4px solid #3d8160;
	}

	.form-section-inner {
		background-color: #f9fbfd;
		border-radius: 12px;
		padding: 20px;
		border: 1px solid rgba(61, 129, 96, 0.15);
	}

	.section-title {
		color: #135D2A;
		font-family: "Lexend", sans-serif;
		font-weight: 600;
		font-size: 18px;
		margin-bottom: 15px;
		padding-bottom: 10px;
		border-bottom: 1px solid rgba(61, 129, 96, 0.2);
	}

	.form-label {
		color: #135D2A;
		font-family: "Lexend", sans-serif;
		font-weight: 600;
		font-size: 14px;
		margin-bottom: 8px;
	}

	.input-with-icon {
		position: relative;

		.input-icon {
			position: absolute;
			right: 15px;
			top: 50%;
			transform: translateY(-50%);
			color: #3d8160;
		}

		input {
			padding-right: 40px;
		}
	}

	.form-control {
		border: 1px solid #dce7e5;
		border-radius: 8px;
		padding: 10px 12px;
		height: auto;
		transition: all 0.3s;

		&:focus {
			border-color: #3d8160;
			box-shadow: 0 0 0 0.2rem rgba(61, 129, 96, 0.25);
		}

		&:disabled {
			background-color: #f2f2f2;
			cursor: not-allowed;
		}
	}

	.table-card {
		background-color: white;
		border-radius: 15px;
		padding: 30px;
		box-shadow: 0 3px 15px rgba(87, 80, 80, 0.05);
		border-top: 4px solid #3d8160;
	}

	.table-title {
		color: #135D2A;
		font-family: "Lexend", sans-serif;
		font-weight: 600;
		font-size: 22px;
		margin-bottom: 25px;
		padding-bottom: 15px;
		border-bottom: 1px solid rgba(61, 129, 96, 0.2);
		position: relative;

		&:after {
			content: '';
			position: absolute;
			bottom: -1px;
			left: 0;
			width: 80px;
			height: 3px;
			background-color: #3d8160;
			border-radius: 3px;
		}
	}

	.roca-tabs-container {
		border-bottom: 2px solid rgba(61, 129, 96, 0.1);
		margin-bottom: 25px;
	}

	.roca-tabs {
		display: flex;
		flex-wrap: wrap;
		gap: 5px;
		margin-bottom: -2px;
	}

	.roca-tab {
		background: transparent;
		border: none;
		border-bottom: 3px solid transparent;
		padding: 12px 20px;
		color: #6c757d;
		font-weight: 500;
		font-family: "Lexend", sans-serif;
		cursor: pointer;
		transition: all 0.3s ease;
		border-radius: 8px 8px 0 0;
		display: flex;
		align-items: center;
		gap: 8px;

		&:hover {
			background-color: rgba(61, 129, 96, 0.05);
			color: #3d8160;
		}

		&.active {
			background-color: white;
			color: #135D2A;
			border-bottom-color: #3d8160;
			font-weight: 600;
			box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);

			i {
				color: #3d8160;
			}
		}

		i {
			font-size: 0.9rem;
			color: #6c757d;
			transition: color 0.3s ease;
		}
	}

	.roca-details {
		margin-bottom: 30px;
		padding-bottom: 30px;
	}

	.roca-header {
		display: flex;
		align-items: center;
		margin-bottom: 15px;
	}

	.roca-badge {
		background: linear-gradient(135deg, #3d8160, #135D2A);
		color: white;
		font-weight: 600;
		padding: 8px 14px;
		border-radius: 20px;
		display: inline-block;
		box-shadow: 0 2px 5px rgba(19, 93, 42, 0.2);
		margin-bottom: 10px;
	}

	.genotype {
		color: #135D2A;
		font-weight: 600;
	}

	.table {
		th {
			font-size: 14px;
			color: #476D4C;
			font-weight: 600;
		}

		.thead-light th {
			background-color: rgba(61, 129, 96, 0.1);
			border-color: rgba(61, 129, 96, 0.2);
		}
	}

	.action-buttons {
		display: flex;
		justify-content: flex-end;
		gap: 15px;
		margin-top: 30px;
		padding-top: 20px;
		border-top: 1px solid rgba(61, 129, 96, 0.1);
	}

	.btn {
		height: auto;
		width: auto;
		padding: 12px 24px;
		border-radius: 8px;
		font-weight: 500;
		transition: all 0.3s;
		font-family: "Lexend", sans-serif;
	}

	.btn-outline-secondary {
		color: #dc3545;
		border-color: #dc3545;

		&:hover {
			background-color: #dc3545;
			color: white;
			transform: translateY(-2px);
			box-shadow: 0 4px 8px rgba(220, 53, 69, 0.2);
		}
	}

	.btn-add {
		margin: 0;
	}

	.btn-agro {
		background: linear-gradient(to right, #3d8160, #135D2A);
		color: white;
		font-weight: 500;
		font-size: 14px;
		border: none;
		box-shadow: 0 4px 10px rgba(19, 93, 42, 0.2);

		&:hover {
			background: linear-gradient(to right, #55AD6A, #3d8160) !important;
			transform: translateY(-2px);
			box-shadow: 0 6px 12px rgba(19, 93, 42, 0.3);
		}
	}

	.properties-table {
		padding: 0 31px 48px;
		overflow: hidden;
	}

	.alert-danger {
		background-color: rgba(220, 53, 69, 0.1);
		color: #dc3545;
		border-color: rgba(220, 53, 69, 0.2);
		border-radius: 8px;
		font-size: 14px;
	}

	::v-deep .vgt-wrap {
		.vgt-table.bordered td, .vgt-table.bordered th {
			border: none;
		}

		.vgt-table th.sortable:before {
			border-top-color: #3d8160;
		}

		.vgt-table th.sortable:after {
			border-bottom-color: #3d8160;
		}
	}

	.vgt-global-search__input .input__icon {
		background-color: #3d8160;
	}
}

@media (max-width: 768px) {
	.propertiesWrapper {
		height: auto;
		max-height: 100vh;

		.form-section, .table-card {
			margin: 15px;
			padding: 20px;
		}

		.roca-tabs {
			flex-direction: column;
			gap: 8px;

			.roca-tab {
				width: 100%;
				justify-content: center;
				border-radius: 8px;
				border-bottom: none;
				border-left: 3px solid transparent;

				&.active {
					border-left-color: #3d8160;
					border-bottom: none;
				}
			}
		}

		.action-buttons {
			flex-direction: column;

			.btn {
				width: 100%;
				margin-bottom: 10px;
			}
		}
	}
}

@media (max-width: 576px) {
	.propertiesWrapper {
		height: auto;
		max-height: 75vh;
		width: 90vw;
		max-width: 90vw;

		h1 {
			font-size: 22px;
		}

		.subtitle {
			font-size: 14px;
		}

		.table-title {
			font-size: 18px;
		}
	}
}
</style>
