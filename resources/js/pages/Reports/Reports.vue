<template>
	<div id="reports" class="mt-5 reportsWrapper">
		<div class="admin-content">
			<div class="admin-header d-flex">
				<h1 style="padding-right: 0.5% !important">Relatórios</h1>
			</div>
		</div>

		<div class="report-box">
			<div class="container">
				<!-- PDF Export -->
				<!-- <div class="d-flex flex-column">
					<h4>PDF</h4>
					<div class="w-100 p-2 align-self-start">
						<form @submit.prevent="exportPDF()">
							<div class="form-row">
								<div class="form-group col-md-4">
									<label for="entities">Entidades</label>
									<select
										id="entities"
										v-model="target"
										class="form-control"
										required
									>
										<option selected disabled value="">
											Escolha uma...
										</option>
										<option value="users">Usuários</option>
										<option value="homogeneous_areas">
											<PERSON><PERSON><PERSON>
										</option>
										<option value="properties">
											Propriedades
										</option>
										<option value="strata">
											Unidades Operacionais
										</option>
										<option value="sampling_points">
											Pontos Amostrais
										</option>
										<option value="visits">Visitas</option>
									</select>
								</div>
							</div>

							<button
								type="submit"
								class="btn btn-agro float-right"
								:disabled="this.pdfLoading || this.xlsLoading"
							>
								<span
									v-if="pdfLoading"
									class="spinner-border spinner-border-sm"
									role="status"
									aria-hidden="true"
								></span>
								<span v-if="pdfLoading">Carregando...</span>
								<span v-else>Exportar para PDF</span>
							</button>
						</form>
					</div>
				</div> -->
				<!-- ./ PDF Export -->

				<!-- XLS Exports -->
				<div class="d-flex flex-column">
					<div>
						<h4>Exportar coletas das propriedades (Excel)</h4>
						<div class="w-100 p-2 align-self-end">
							<div style="margin-bottom: 2%">
								<span>
									Ao exportar os dados, o resultado será uma
									planilha com todo os dados das coletas
									relacionadas à propriedade selecionada.
								</span>
							</div>
							<form @submit.prevent="exportXLS()">
								<div
									class="d-flex align-items-center"
									style="gap: 8px"
								>
									<select
										id="entities"
										v-model="propertyCollect"
										class="form-control w-50"
										required
									>
										<option selected disabled value="">
											Escolha uma...
										</option>
										<option
											v-for="property in properties"
											:key="property.id"
											:value="property.id"
										>
											{{ property.name }}
										</option>
										<!-- <option value="users">Usuários</option> -->
									</select>
									<button
										type="submit"
										class="btn btn-agro"
										:disabled="
											this.pdfLoading ||
											this.xlsLoading ||
											propertyCollect.id === null
										"
									>
										<span
											v-if="xlsLoading"
											class="spinner-border spinner-border-sm"
											role="status"
											aria-hidden="true"
										></span>
										<span v-if="xlsLoading"
											>Carregando...</span
										>
										<span v-else
											>Exportar dados das coletas</span
										>
									</button>
								</div>
							</form>
						</div>
					</div>
					<div class="mt-4">
						<h4>
							Exportar dados da prática das propriedades (Excel)
						</h4>
						<div class="w-100 p-2 align-self-end">
							<div style="margin-bottom: 2%">
								<span>
									Ao exportar os dados, o resultado será uma
									planilha com todo os dados da prática
									relacionadas à propriedade selecionada.
								</span>
							</div>
							<form @submit.prevent="exportPractice()">
								<div
									class="d-flex align-items-center"
									style="gap: 8px"
								>
									<select
										id="entities"
										v-model="propertyPractice"
										class="form-control w-50"
										required
									>
										<option selected disabled value="">
											Escolha uma...
										</option>
										<option
											v-for="property in properties"
											:key="property.id"
											:value="property.id"
										>
											{{ property.name }}
										</option>
										<!-- <option value="users">Usuários</option> -->
									</select>
									<button
										type="submit"
										class="btn btn-agro"
										:disabled="
											this.pdfLoading ||
											this.xlsLoading ||
											propertyPractice.id === null
										"
									>
										<span
											v-if="xlsLoading"
											class="spinner-border spinner-border-sm"
											role="status"
											aria-hidden="true"
										></span>
										<span v-if="xlsLoading"
											>Carregando...</span
										>
										<span v-else
											>Exportar dados da prática</span
										>
									</button>
								</div>
							</form>
						</div>
					</div>
				</div>
				<!-- ./ XLS Exports -->

				<!-- CSV Export -->
				<!-- <div class="d-flex flex-column">
					<h4>CSV (Texto)</h4>
					<div class="w-100 p-2 align-self-end">
						<div style="margin-bottom: 2%">
							<span>
								Ao exportar neste formato, o resultado será uma
								tabela com todo o conteúdo das coletas,
								relacionadas com cada entidade do banco de dados
							</span>
						</div>
						<form @submit.prevent="exportCSV()">
							<button
								type="submit"
								class="btn btn-agro float-right"
								:disabled="this.pdfLoading || this.xlsLoading || this.csvLoading"
							>
								<span
									v-if="csvLoading"
									class="spinner-border spinner-border-sm"
									role="status"
									aria-hidden="true"
								></span>
								<span v-if="csvLoading">Carregando...</span>
								<span v-else>Exportar para CSV</span>
							</button>
						</form>
					</div>
				</div> -->
				<!-- ./ CSV Export -->
			</div>
		</div>
	</div>
</template>

<script src="./Reports"></script>

<style lang="scss">
.reportsWrapper {
	background-color: #f5f8fd;
	border-radius: 20px;

	h1 {
		color: #3d8160;

		font-family: "Lexend", sans-serif;
		font-weight: 600;
		font-size: 24px;

		padding: 16px 0px 16px 48px;
	}

	.report-box {
		padding: 2.7%;
	}
}

@media (max-width: 576px) {
	.reportsWrapper {
		height: 75vh;
		max-height: 80vh;
	}
}
</style>
