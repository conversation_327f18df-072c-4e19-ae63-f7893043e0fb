import Vue from "vue";
import VueMask from "v-mask";
import VueTheMask from "vue-the-mask";
import { Fields, PaginationOptions } from "./TableOptions";

Vue.use(VueMask);
Vue.use(VueTheMask);

export default {
	data() {
		return {
			authToken: null,
			rfids: [],
			rfid: {
				id: "",
				code: "",
				property_id: "",
				status: true,
			},
			messages: [],
			haveError: false,
			editMode: false,
			loading: false,
			fields: Fields,
			paginationOptions: PaginationOptions,
		};
	},

	created() {
		this.authToken = window.token;
		this.index();
	},

	methods: {
		index() {
			this.loading = true;

			axios
				.get("/api/v1/rfids", {
					headers: { authorization: `bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						this.rfids = response.data.data;
					} else {
						console.log(response);
					}
					this.loading = false;
				})
				.catch((err) => {
					this.loading = false;
					console.log(err);
				});
		},

		addModal() {
			this.clearForm();
			this.editMode = false;
			$("#modalRFIDs").modal("show");
		},

		editModal(rfid) {
			this.editMode = true;
			this.rfid = { ...rfid };
			$("#modalRFIDs").modal("show");
		},

		confirmDelete(rfidId) {
			Swal.fire({
				title: "Tem certeza?",
				text: "Você não poderá reverter esta ação!",
				icon: "warning",
				showCancelButton: true,
				confirmButtonColor: "#d33",
				cancelButtonColor: "#3085d6",
				confirmButtonText: "Sim, excluir!",
				cancelButtonText: "Cancelar",
			}).then((result) => {
				if (result.value) {
					this.deleteRfid(rfidId);
				}
			});
		},

		store() {
			if (this.rfid.code.length > 10) {
				Swal.fire({
					title: "Erro!",
					text: "O código RFID não pode ter mais de 10 caracteres.",
					icon: "error",
					confirmButtonText: "Fechar",
				});
				return;
			}

			if (!this.haveError) {
				axios
					.post("/api/v1/rfids", this.rfid, {
						headers: { authorization: `bearer ${this.authToken}` },
					})
					.then((response) => {
						if (response.status === 201 && response.data.success) {
							Swal.fire({
								title: response.data.message,
								icon: "success",
								showConfirmButton: false,
								timer: 1500,
							});
							this.index();
						} else {
							console.log(response);
						}
						this.clearForm();
					})
					.catch((err) => {
						Swal.fire({
							title: err.response.data.message,
							text: err.response.data.detail,
							icon: "error",
							confirmButtonText: "Fechar",
						});
					});
			}
		},

		update() {
			if (this.rfid.code.length > 10) {
				Swal.fire({
					title: "Erro!",
					text: "O código RFID não pode ter mais de 10 caracteres.",
					icon: "error",
					confirmButtonText: "Fechar",
				});
				return;
			}

			if (!this.haveError) {
				axios
					.put(`/api/v1/rfids/${this.rfid.id}`, this.rfid, {
						headers: { authorization: `bearer ${this.authToken}` },
					})
					.then((response) => {
						if (response.status === 200 && response.data.success) {
							Swal.fire({
								title: response.data.message,
								icon: "success",
								showConfirmButton: false,
								timer: 1500,
							});
							this.index();
						} else {
							console.log(response);
						}
						this.clearForm();
					})
					.catch((err) => {
						Swal.fire({
							title: err.response.data.message,
							text: err.response.data.detail,
							icon: "error",
							confirmButtonText: "Fechar",
						});
					});
			}
		},

		changeStatus(rfid_id, status = true) {
			let status_message = status ? "ativar" : "desativar";

			Swal.fire({
				title: `Deseja ${status_message} este RFID?`,
				icon: "warning",
				showCancelButton: true,
				confirmButtonColor: "#3085d6",
				cancelButtonColor: "#d33",
				confirmButtonText: "Sim",
				cancelButtonText: "Cancelar",
			}).then((result) => {
				if (result.value) {
					axios
						.put(
							`/api/v1/rfids/${rfid_id}/status`,
							{ status },
							{
								headers: {
									authorization: `bearer ${this.authToken}`,
								},
							}
						)
						.then((response) => {
							if (response.status === 200) {
								console.error(response.data.message);
								Swal.fire({
									title: response.data.message,
									icon: "success",
									showConfirmButton: false,
									timer: 1500,
								});
								this.index();
							} else {
								console.log(response);
							}
						})
						.catch((err) => {
							console.log(err);
						});
				}
			});
		},

		deleteRfid(rfidId) {
			axios
				.delete(`/api/v1/rfids/${rfidId}`, {
					headers: { authorization: `bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200 && response.data.success) {
						Swal.fire({
							title: "Excluído!",
							text: response.data.message,
							icon: "success",
							showConfirmButton: false,
							timer: 1500,
						});
						this.index();
					} else {
						console.log(response);
					}
				})
				.catch((err) => {
					Swal.fire({
						title: "Erro!",
						text: "Não foi possível excluir o RFID.",
						icon: "error",
						confirmButtonText: "Fechar",
					});
				});
		},

		clearForm() {
			this.rfid.id = null;
			this.rfid.code = "";
			this.rfid.status = true;
			this.messages = [];
			$("#modalRFIDs").modal("hide");
		},
	},
};
