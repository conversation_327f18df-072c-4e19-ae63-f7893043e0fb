<template>
	<div id="rfids" class="mt-5 rfidsWrapper">
		<div class="admin-content">
			<div
				class="admin-header d-flex justify-content-between align-items-center p-2"
			>
				<h1>RFIDs</h1>
				<button
					type="button"
					class="btn btn-agro btn-add"
					@click="addModal"
				>
					<i class="fas fa-plus"></i>
				</button>
			</div>
			<br />

			<div v-if="loading" class="loader-overlay">
				<loader :loading="loading"></loader>
			</div>

			<div class="rfids-table" v-else>
				<vue-good-table
					title="RFIDs"
					:columns="fields"
					:rows="rfids"
					:search-options="{
						enabled: true,
						placeholder: 'Pesquisar...',
					}"
					:pagination-options="paginationOptions"
					compactMode
				>
					<div slot="emptystate">
						Não há registros para esta pesquisa
					</div>
					<template slot="table-row" slot-scope="props">
						<span v-if="props.column.field == 'code'">
							{{ props.row.code }}
						</span>

						<span v-if="props.column.field == 'user.name'">
							{{ props.row.user.name }}
						</span>

						<div
							v-else-if="props.column.field == 'status'"
							class="custom-control custom-switch"
						>
							<input
								type="checkbox"
								class="custom-control-input"
								v-bind:id="`status_${props.row.id}`"
								v-model="props.row.status"
								@click.prevent="
									props.row.status
										? changeStatus(
												props.row.id,
												(status = false)
										  )
										: changeStatus(props.row.id)
								"
							/>
							<label
								class="custom-control-label"
								v-bind:for="`status_${props.row.id}`"
								>{{
									props.row.status ? "Desativar" : "Ativar"
								}}</label
							>
						</div>

						<div v-else-if="props.column.field == 'actions'">
							<button
								title="Editar"
								class="btn btn-secondary"
								@click.prevent="editModal(props.row)"
							>
								<i class="fas fa-cog fa-lg"></i>
							</button>

							<button
								title="Excluir"
								class="btn btn-danger ml-2"
								@click.prevent="confirmDelete(props.row.id)"
							>
								<i class="fas fa-trash fa-lg"></i>
							</button>
						</div>
					</template>
				</vue-good-table>
			</div>

			<div
				class="modal fade"
				id="modalRFIDs"
				ref="modal"
				tabindex="-1"
				role="dialog"
				aria-labelledby="modalRFIDsLabel"
				aria-hidden="true"
			>
				<div
					class="modal-dialog modal-dialog-centered modal-lg"
					role="document"
				>
					<div class="modal-content">
						<div class="modal-header">
							<h5
								class="modal-title"
								id="modalRFIDsLabel"
								v-show="!editMode"
							>
								ADICIONAR NOVO RFID
							</h5>
							<h5
								class="modal-title"
								id="modalRFIDsLabel"
								v-show="editMode"
							>
								{{ `ATUALIZAR RFID: ${rfid.code}` }}
							</h5>
							<button
								type="button"
								class="close"
								data-dismiss="modal"
								aria-label="Close"
							>
								<span aria-hidden="true">&times;</span>
							</button>
						</div>
						<div class="modal-body">
							<form
								@submit.prevent="editMode ? update() : store()"
							>
								<div class="form-group">
									<label for="code">Código RFID</label>
									<input
										id="code"
										type="text"
										class="form-control"
										maxlength="10"
										v-model="rfid.code"
										required
									/>
								</div>

								<div class="modal-footer">
									<button
										type="button"
										class="btn btn-secondary"
										data-dismiss="modal"
									>
										Sair
									</button>
									<button
										type="submit"
										class="btn btn-primary"
										v-show="!editMode"
										:disabled="haveError"
									>
										Cadastrar
									</button>
									<button
										type="submit"
										class="btn btn-primary"
										v-show="editMode"
									>
										Atualizar
									</button>
								</div>
							</form>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script src="./Rfids.js"></script>

<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>
<style lang="scss" scoped>
.rfidsWrapper {
	background-color: #f5f8fd;
	border-radius: 20px;

	h1 {
		color: #3d8160;
		font-family: "Lexend", sans-serif;
		font-weight: 600;
		font-size: 24px;
		padding: 16px 0px 16px 48px;
	}

	.btn {
		height: auto;
		width: auto;
	}

	.btn-add {
		margin: 16px 48px 16px 0;
	}

	.rfids-table {
		padding: 0 31px 48px;
	}
}

@media (max-width: 768px) {
	.rfidsWrapper {
		height: 70vh;
		max-height: 80vh;
		overflow-y: scroll;
	}
}
</style>
