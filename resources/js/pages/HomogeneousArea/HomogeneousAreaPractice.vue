<template>
	<div class="homogeneous-area-practice-container">
		<!-- Header Principal -->
		<div class="page-header">
			<div class="header-content">
				<div class="header-navigation">
					<back-button
						title="Voltar para áreas homogêneas"
					></back-button>
				</div>
				<div class="header-icon">
					<i class="fas fa-seedling"></i>
				</div>
				<div class="header-text">
					<h1 class="page-title">{{ `Coletas da Prática da Área Homogênea ${halabel}` }}</h1>
					<p class="page-subtitle"><PERSON><PERSON><PERSON><PERSON> as pr<PERSON>ticas e coletas da área homogênea</p>
				</div>
			</div>
		</div>

		<!-- <PERSON><PERSON><PERSON><PERSON> Principal -->
		<div class="main-content">
			<!-- Loading State -->
			<div v-if="loading" class="loading-section">
				<div class="loading-card">
					<loader :loading="loading"></loader>
					<p>Carregando práticas...</p>
				</div>
			</div>

			<!-- Practice Table - Mantendo estrutura original -->
			<div v-else class="homogeneous-area-practice-table-modern">
				<vue-good-table
					title="Áreas Homogêneas"
					:columns="fields"
					:rows="visits_information"
					:pagination-options="paginationOptions"
					:fixed-header="true"
					:search-options="{
						enabled: true,
						placeholder: 'Pesquisar...',
					}"
					:sort-options="{
						enabled: true,
						initialSortBy: {
							field: 'id',
							type: 'asc',
						},
					}"
					compactMode
				>
					<div slot="emptystate">
						Não há registros para esta pesquisa
					</div>
					<template slot="table-row" slot-scope="props">
						<span v-if="props.column.field == 'date'">{{
							`${convertDate(props.row.date)}`
						}}</span>
						<button
							v-else-if="props.column.field == 'actions'"
							title="Informações da Prática"
							class="btn btn-secondary"
							@click.prevent="
								showModal(props.row, props.index + 1)
							"
						>
							<i class="fas fa-info-circle fa-lg"></i>
						</button>
					</template>
				</vue-good-table>
			</div>
		</div>

		<!-- Modais mantidos exatamente como estão -->
		<div
			class="modal fade"
			id="modalShow"
				tabindex="-1"
				role="dialog"
				aria-labelledby="modalShowLabel"
				aria-hidden="true"
			>
				<div
					class="modal-dialog modal-dialog-centered modal-lg"
					role="document"
				>
					<div class="modal-content">
						<div class="modal-header justify-content-center">
							<div class="col text-center">
								<h5 class="modal-title" id="modalShowLabel">
									{{
										`Informações da Prática: Área Homogênea ${halabel} - Visita ${visit_information.id}`
									}}
								</h5>
							</div>
						</div>
						<div class="modal-body">
							<div class="container">
								<div id="area_information">
									<div class="row">
										<div class="col-sm">
											<span>
												Floração:
												{{
													generalAreaInfo(
														visit_information.flowering
													)
												}}
											</span>
										</div>
										<div class="col-sm">
											<span>
												Refoliação:
												{{
													generalAreaInfo(
														visit_information.refoliation
													)
												}}
											</span>
										</div>
										<div class="col-sm">
											<span>
												Copa:
												{{
													generalAreaInfo(
														visit_information.top
													)
												}}
											</span>
										</div>
									</div>

									<div class="row">
										<div class="col-sm">
											<i
												v-if="visit_information.pruned"
												class="fas fa-check-circle"
											></i>
											<i
												v-else
												class="fas fa-times-circle"
											></i>
											<span>Podada</span>
										</div>
										<div class="col-sm">
											<i
												v-if="visit_information.mowing"
												class="fas fa-check-circle"
											></i>
											<i
												v-else
												class="fas fa-times-circle"
											></i>
											<span>Roçada</span>
										</div>
										<div class="col-sm">
											<i
												v-if="visit_information.weeding"
												class="fas fa-check-circle"
											></i>
											<i
												v-else
												class="fas fa-times-circle"
											></i>
											<span>Capinada</span>
										</div>
									</div>

									<div class="row">
										<div class="col-sm">
											<i
												v-if="visit_information.grated"
												class="fas fa-check-circle"
											></i>
											<i
												v-else
												class="fas fa-times-circle"
											></i>
											<span>Raleada</span>
										</div>
										<div class="col-sm">
											<i
												v-if="visit_information.renewed"
												class="fas fa-check-circle"
											></i>
											<i
												v-else
												class="fas fa-times-circle"
											></i>
											<span>Renovada</span>
										</div>
										<div class="col-sm">
											<i
												v-if="
													visit_information.fertilized
												"
												class="fas fa-check-circle"
											></i>
											<i
												v-else
												class="fas fa-times-circle"
											></i>
											<span>Adubada</span>
										</div>
									</div>

									<div class="row">
										<div class="col-sm">
											<i
												v-if="
													visit_information.pulverized
												"
												class="fas fa-check-circle"
											></i>
											<i
												v-else
												class="fas fa-times-circle"
											></i>
											<span>Pulverizada</span>
										</div>
										<div class="col-sm">
											<i
												v-if="visit_information.wind"
												class="fas fa-check-circle"
											></i>
											<i
												v-else
												class="fas fa-times-circle"
											></i>
											<span>Vento</span>
										</div>
										<div class="col-sm">
											<i
												v-if="
													visit_information.brown_rot
												"
												class="fas fa-check-circle"
											></i>
											<i
												v-else
												class="fas fa-times-circle"
											></i>
											<span>Podridão Parda</span>
										</div>
									</div>

									<div class="row">
										<div class="col-sm">
											<i
												v-if="visit_information.drought"
												class="fas fa-check-circle"
											></i>
											<i
												v-else
												class="fas fa-times-circle"
											></i>
											<span>Estiagem</span>
										</div>
										<div class="col-sm">
											<i
												v-if="visit_information.rain"
												class="fas fa-check-circle"
											></i>
											<i
												v-else
												class="fas fa-times-circle"
											></i>
											<span>Chuva</span>
										</div>
										<div class="col-sm">
											<i
												v-if="visit_information.rat"
												class="fas fa-check-circle"
											></i>
											<i
												v-else
												class="fas fa-times-circle"
											></i>
											<span>Rato</span>
										</div>
									</div>

									<div class="row">
										<div class="col-sm">
											<i
												v-if="visit_information.flood"
												class="fas fa-check-circle"
											></i>
											<i
												v-else
												class="fas fa-times-circle"
											></i>
											<span>Enchente</span>
										</div>
										<div class="col-sm">
											<i
												v-if="visit_information.insect"
												class="fas fa-check-circle"
											></i>
											<i
												v-else
												class="fas fa-times-circle"
											></i>
											<span>Inseto</span>
										</div>
										<div class="col-sm">
											<i
												v-if="
													visit_information.absence_of_shadow
												"
												class="fas fa-check-circle"
											></i>
											<i
												v-else
												class="fas fa-times-circle"
											></i>
											<span>Sombra</span>
										</div>
									</div>
								</div>

								<div class="row mb-3 ml-3 subtitle">
									<div class="col-sm">
										<span>Legenda:</span>
									</div>

									<div class="col-sm">
										<i class="fas fa-check-circle"></i>
										Tarefa realizada
									</div>

									<div class="col-sm">
										<i class="fas fa-times-circle"></i>
										Tarefa não realizada
									</div>
								</div>

								<!-- <div class="row">
									<div class="col">
										<button
											type="button"
											class="btn btn-success"
											@click="showAreaInfo = false"
										>
											<i class="fas fa-arrow-left"></i>
											<span>Informações dos Frutos</span>
										</button>
									</div>
								</div> -->
							</div>

							<div class="modal-footer">
								<button
									type="button"
									class="btn btn-secondary"
									data-dismiss="modal"
									@click="showAreaInfo = false"
								>
									Sair
								</button>
							</div>
						</div>
					</div>
			</div>
		</div>
	</div>
</template>

<script src="./HomogeneousAreaPractice.js"></script>

<style lang="scss" scoped>
// Variáveis
$primary-color: #3d8160;
$primary-dark: #2d6047;
$text-color: #2c3e50;
$text-light: #6c757d;
$white: #ffffff;
$background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
$shadow: rgba(0, 0, 0, 0.1);
$shadow-hover: rgba(0, 0, 0, 0.15);

// Container Principal
.homogeneous-area-practice-container {
	min-height: 100vh;
	background: $background;
	padding: 2rem;
	font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

	@media (max-width: 768px) {
		padding: 1rem;
	}
}

// Header Section
.page-header {
	background: $white;
	border-radius: 20px;
	padding: 2.5rem;
	margin-bottom: 2rem;
	box-shadow: 0 8px 25px $shadow;
	border: 1px solid rgba(255, 255, 255, 0.2);
	backdrop-filter: blur(10px);
	transition: transform 0.3s ease, box-shadow 0.3s ease;

	&:hover {
		transform: translateY(-2px);
		box-shadow: 0 12px 35px $shadow-hover;
	}

	.header-content {
		display: flex;
		align-items: center;
		gap: 2rem;

		@media (max-width: 768px) {
			flex-direction: column;
			text-align: center;
			gap: 1.5rem;
		}
	}

	.header-navigation {
		display: flex;
		align-items: center;
	}

	.header-icon {
		width: 70px;
		height: 70px;
		background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
		border-radius: 20px;
		display: flex;
		align-items: center;
		justify-content: center;
		color: $white;
		font-size: 2rem;
		box-shadow: 0 8px 25px rgba($primary-color, 0.3);
		transition: transform 0.3s ease;

		&:hover {
			transform: scale(1.05);
		}
	}

	.header-text {
		flex: 1;

		.page-title {
			font-size: 2.5rem;
			font-weight: 700;
			color: $primary-color;
			margin: 0 0 0.5rem 0;
		}

		.page-subtitle {
			font-size: 1.1rem;
			color: $text-light;
			margin: 0;
			font-weight: 400;
		}
	}
}

// Main Content
.main-content {
	display: flex;
	flex-direction: column;
	gap: 2rem;
}

// Loading Section
.loading-section {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 400px;

	.loading-card {
		background: $white;
		border-radius: 20px;
		padding: 3rem;
		text-align: center;
		box-shadow: 0 8px 25px $shadow;
		border: 1px solid rgba(255, 255, 255, 0.2);

		p {
			margin-top: 1rem;
			color: $text-light;
			font-size: 1.1rem;
		}
	}
}

// Tabela - Mantendo funcionalidades, modernizando visual
.homogeneous-area-practice-table-modern {
	background: $white;
	border-radius: 20px;
	box-shadow: 0 8px 25px $shadow;
	padding: 2rem;
	transition: transform 0.3s ease;

	&:hover {
		transform: translateY(-2px);
	}
}

// Ícones originais preservados
:deep(i) {
	&.fa-check-circle {
		color: #06c000;
	}
	&.fa-times-circle {
		color: #cf0000;
	}
}

// Estilos dos modais preservados
:deep(.modal-header) {
	border-bottom: 0px !important;
}

:deep(.modal-footer) {
	border-top: 0px !important;
}

.subtitle {
	background-color: rgb(230, 230, 230);
	border-radius: 5px;
	padding: 5px;
}

#area_information {
	margin: 2em;
	font-size: 16px;

	.row {
		margin-bottom: 1em;
	}
}

// Responsividade
@media (max-width: 768px) {
	.homogeneous-area-practice-container {
		padding: 1rem;
	}

	.page-header {
		padding: 1.5rem;
		margin-bottom: 1rem;

		.header-content {
			flex-direction: column;
			text-align: center;
			gap: 1rem;
		}

		.header-text .page-title {
			font-size: 2rem;
		}
	}

	.homogeneous-area-practice-table-modern {
		padding: 1rem;
		margin: 10px 0;
	}
}

@media (max-width: 480px) {
	.page-header .header-text .page-title {
		font-size: 1.75rem;
	}

	.loading-card {
		padding: 2rem 1rem;
	}
}
</style>
