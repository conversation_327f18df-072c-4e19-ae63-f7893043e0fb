<template>
	<div class="sampling-point-container">
		<!-- Header Principal -->
		<div class="page-header">
			<div class="header-content">
				<div class="header-navigation">
					<back-button
						title="Voltar para unidades operacionais"
					></back-button>
				</div>
				<div class="header-icon">
					<i class="fas fa-map-marker-alt"></i>
				</div>
				<div class="header-text">
					<h1 class="page-title">Pont<PERSON> Amostrais</h1>
					<p class="page-subtitle">
						{{ sampling_points[0]?.stratum?.homogeneous_area?.property?.name ?
							sampling_points[0]?.stratum?.homogeneous_area?.property?.name + " / Área Homogênea " + sampling_points[0]?.stratum?.homogeneous_area?.label + " / Unidade Operacional " + stratum.label :
							'Gerencie os pontos amostrais da unidade operacional' }}
					</p>
				</div>
				<div class="header-actions" v-if="stratum.total_sampling_points < 5">
					<button
						type="button"
						class="btn-primary"
						@click.prevent="addModal"
					>
						<i class="fas fa-plus"></i>
						<span>Adicionar <PERSON></span>
					</button>
				</div>
			</div>
		</div>

		<!-- Conteúdo Principal -->
		<div class="main-content">
			<!-- Loading State -->
			<div v-if="loading" class="loading-section">
				<div class="loading-card">
					<loader :loading="loading"></loader>
					<p>Carregando pontos amostrais...</p>
				</div>
			</div>

			<!-- Sampling Points Table - Mantendo estrutura original -->
			<div v-else class="sampling-points-table-modern">
				<vue-good-table
					title="Pontos Amostrais"
					:columns="fields"
					:rows="sampling_points"
					:pagination-options="paginationOptions"
					:fixed-header="true"
					:row-style-class="rowStyleClassFn"
					:search-options="{
						enabled: true,
						placeholder: 'Pesquisar...',
					}"
					:sort-options="{
						enabled: true,
						initialSortBy: [
							{ field: 'status', type: 'desc' },
							{ field: 'label', type: 'asc' },
						],
					}"
					compactMode
				>
					<div slot="emptystate">
						Não há registros para esta pesquisa
					</div>
					<template slot="table-row" slot-scope="props">
						<span v-if="props.column.field == 'label'">{{
							`${props.row.label}`
						}}</span>
						<div
							v-else-if="props.column.field == 'status'"
							class="custom-control custom-switch"
						>
							<div v-if="props.row.status">
								<input
									type="checkbox"
									class="custom-control-input"
									v-bind:id="`status_${props.row.id}`"
									v-model="props.row.status"
									@click.prevent="
										props.row.status
											? changeStatus(
													props.row.id,
													(status = false)
											  )
											: changeStatus(props.row.id)
									"
								/>
								<label
									class="custom-control-label"
									v-bind:for="`status_${props.row.id}`"
									>{{
										props.row.status
											? "Desativar"
											: "Ativar"
									}}</label
								>
							</div>
							<div v-else>
								{{
									`Ponto Amostral ${
										sampling_point.label
									} desativado em ${convertDate(
										props.row.updated_at
									)}`
								}}
							</div>
						</div>
						<div v-else-if="props.column.field == 'actions'">
							<button
								v-if="props.row.status"
								title="Editar"
								class="btn btn-sm btn-secondary"
								@click.prevent="editModal(props.row)"
							>
								<i class="fas fa-cog fa-lg"></i>
							</button>

							<router-link
								v-if="props.row.status"
								title="Árvores"
								tag="button"
								class="btn btn-sm btn-success"
								v-bind:to="{
									path: '/panel/trees',
									query: { spid: props.row.id },
								}"
							>
								<i title="Árvores">
									<img
										src="/img/cocoa-tree.svg"
										class="img-fluid"
										style="width: 1.3333333333em"
									/>
								</i>
							</router-link>
						</div>
					</template>
				</vue-good-table>
			</div>
		</div>

		<!-- Modais mantidos exatamente como estão -->
		<div
			class="modal fade"
			id="modalSamplingPoints"
				tabindex="-1"
				role="dialog"
				aria-labelledby="modalSamplingPointsLabel"
				aria-hidden="true"
			>
				<div class="modal-dialog modal-dialog-centered" role="document">
					<div class="modal-content">
						<div class="modal-header">
							<h5
								class="modal-title"
								id="modalSamplingPointLabel"
								v-show="!editMode"
							>
								{{
									`CADASTRAR PONTO AMOSTRAL ${
										sampling_points.length + 1
									}`
								}}
							</h5>
							<h5
								class="modal-title"
								id="modalSamplingPointsLabel"
								v-show="editMode"
							>
								{{
									`ATUALIZAR PONTO AMOSTRAL ${sampling_point.label}`
								}}
							</h5>
							<button
								type="button"
								class="close"
								data-dismiss="modal"
								aria-label="Close"
							>
								<span aria-hidden="true">&times;</span>
							</button>
						</div>
						<div class="modal-body">
							<form
								@submit.prevent="editMode ? update() : store()"
							>
								<!-- <div class="form-group">
									<label for="sPointLabel">Rótulo</label>
									<input
										id="sPointLabel"
										type="number"
										min="1"
										class="form-control"
										v-model="sampling_point.label"
										required
									/>
								</div> -->

								<!-- <div class="form-group">
									<label for="sPointPeriod"
										>Período Agrícola</label
									>
									<input
										id="sPointPeriod"
										type="number"
										min="1"
										max="17"
										class="form-control"
										v-model="sampling_point.ini_period"
										required
									/>
								</div> -->

								<div class="form-group">
									<label for="sPointStratum"
										>Unidade Operacional</label
									>
									<input
										type="text"
										class="form-control"
										:value="stratum.label"
										disabled
									/>
									<!-- <select
										:id="'sPointStratum'"
										name="stratum"
										class="form-control"
										v-model="sampling_point.stratum"
										required
									>
										<option
											v-for="stratum in strata"
											v-bind:value="stratum"
											v-bind:key="stratum.id"
										>
											{{
												`${stratum.label} (${
													stratum.status
														? "Ativo"
														: "Inativo"
												})`
											}}
										</option>
									</select> -->
								</div>

								<hr />

								<div class="form-row">
									<div class="form-group col-md-6">
										<label for="latitude">Latitude</label>
										<input
											id="latitude"
											class="form-control"
											aria-describedby="latHelp"
											maxlength="10"
											v-model="
												sampling_point.geolocation
													.latitude
											"
											required
										/>
										<small
											v-if="messages.latitude"
											id="latHelp"
											class="form-text text-danger"
											>{{ messages.latitude }}</small
										>
										<small
											v-else
											id="latHelp"
											class="form-text text-info"
											>Ex.: 14.145782</small
										>
									</div>
									<div class="form-group col-md-6">
										<label for="longitude">Longitude</label>
										<input
											id="longitude"
											class="form-control"
											aria-describedby="lgnHelp"
											maxlength="10"
											v-model="
												sampling_point.geolocation
													.longitude
											"
											required
										/>
										<small
											v-if="messages.longitude"
											id="lgnHelp"
											class="form-text text-danger"
											>{{ messages.longitude }}</small
										>
										<small
											v-else
											id="lgnHelp"
											class="form-text text-info"
											>Ex.: -175.145782</small
										>
									</div>
								</div>

								<div class="modal-footer">
									<button
										type="button"
										class="btn btn-secondary"
										data-dismiss="modal"
									>
										Sair
									</button>
									<button
										type="submit"
										class="btn btn-primary"
										v-show="!editMode"
										:disabled="haveError"
									>
										Cadastrar
									</button>
									<button
										type="submit"
										class="btn btn-primary"
										v-show="editMode"
										:disabled="haveError"
									>
										Atualizar
									</button>
								</div>
							</form>
						</div>
					</div>
				</div>
		</div>
	</div>
</template>

<script src="./SamplingPoint.js"></script>

<style lang="scss" scoped>
// Variáveis
$primary-color: #3d8160;
$primary-dark: #2d6047;
$text-color: #2c3e50;
$text-light: #6c757d;
$white: #ffffff;
$background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
$shadow: rgba(0, 0, 0, 0.1);
$shadow-hover: rgba(0, 0, 0, 0.15);

// Container Principal
.sampling-point-container {
	min-height: 100vh;
	background: $background;
	padding: 2rem;
	font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

	@media (max-width: 768px) {
		padding: 1rem;
	}
}

// Header Section
.page-header {
	background: $white;
	border-radius: 20px;
	padding: 2.5rem;
	margin-bottom: 2rem;
	box-shadow: 0 8px 25px $shadow;
	border: 1px solid rgba(255, 255, 255, 0.2);
	backdrop-filter: blur(10px);
	transition: transform 0.3s ease, box-shadow 0.3s ease;

	&:hover {
		transform: translateY(-2px);
		box-shadow: 0 12px 35px $shadow-hover;
	}

	.header-content {
		display: flex;
		align-items: center;
		gap: 2rem;

		@media (max-width: 768px) {
			flex-direction: column;
			text-align: center;
			gap: 1.5rem;
		}
	}

	.header-navigation {
		display: flex;
		align-items: center;
	}

	.header-icon {
		width: 70px;
		height: 70px;
		background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
		border-radius: 20px;
		display: flex;
		align-items: center;
		justify-content: center;
		color: $white;
		font-size: 2rem;
		box-shadow: 0 8px 25px rgba($primary-color, 0.3);
		transition: transform 0.3s ease;

		&:hover {
			transform: scale(1.05);
		}
	}

	.header-text {
		flex: 1;

		.page-title {
			font-size: 2.5rem;
			font-weight: 700;
			color: $primary-color;
			margin: 0 0 0.5rem 0;
		}

		.page-subtitle {
			font-size: 1.1rem;
			color: $text-light;
			margin: 0;
			font-weight: 400;
		}
	}

	.header-actions {
		display: flex;
		gap: 1rem;

		@media (max-width: 768px) {
			flex-direction: column;
			width: 100%;
		}
	}
}

// Main Content
.main-content {
	display: flex;
	flex-direction: column;
	gap: 2rem;
}

// Loading Section
.loading-section {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 400px;

	.loading-card {
		background: $white;
		border-radius: 20px;
		padding: 3rem;
		text-align: center;
		box-shadow: 0 8px 25px $shadow;
		border: 1px solid rgba(255, 255, 255, 0.2);

		p {
			margin-top: 1rem;
			color: $text-light;
			font-size: 1.1rem;
		}
	}
}

// Tabela - Mantendo funcionalidades, modernizando visual
.sampling-points-table-modern {
	background: $white;
	border-radius: 20px;
	box-shadow: 0 8px 25px $shadow;
	padding: 2rem;
	transition: transform 0.3s ease;

	&:hover {
		transform: translateY(-2px);
	}

	// Mantendo estilo original para pontos desativados
	:deep(.deactivate-sp) {
		background-color: #818181a6 !important;
		background: repeating-linear-gradient(
			45deg,
			#e7e7e7be,
			#e7e7e7be 10px,
			#d4d4d4a2 10px,
			#d4d4d4a2 20px
		);
	}
}

// Botões
.btn-primary {
	background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
	border: none;
	border-radius: 12px;
	padding: 0.75rem 1.5rem;
	font-weight: 600;
	font-size: 0.95rem;
	color: $white;
	display: flex;
	align-items: center;
	gap: 0.5rem;
	transition: all 0.3s ease;
	box-shadow: 0 4px 15px rgba($primary-color, 0.3);
	text-decoration: none;

	&:hover {
		transform: translateY(-2px);
		box-shadow: 0 6px 20px rgba($primary-color, 0.4);
		color: $white;
		text-decoration: none;
	}

	&:active {
		transform: translateY(0);
	}

	i {
		font-size: 1rem;
	}
}

// Responsividade
@media (max-width: 768px) {
	.sampling-point-container {
		padding: 1rem;
	}

	.page-header {
		padding: 1.5rem;
		margin-bottom: 1rem;

		.header-content {
			flex-direction: column;
			text-align: center;
			gap: 1rem;
		}

		.header-text .page-title {
			font-size: 2rem;
		}

		.header-actions {
			width: 100%;
			flex-direction: column;
		}
	}

	.sampling-points-table-modern {
		padding: 1rem;
		margin: 10px 0;
	}

	.btn-primary {
		width: 100%;
		justify-content: center;
	}
}

@media (max-width: 480px) {
	.page-header .header-text .page-title {
		font-size: 1.75rem;
	}

	.loading-card {
		padding: 2rem 1rem;
	}
}
</style>
