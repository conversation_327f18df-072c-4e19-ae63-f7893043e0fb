<template>
	<div id="reports" class="mt-4 dataImportWrapper">
		<!-- IMPORTAÇÃO DE DADOS -->
		<div class="data-box">
			<div class="admin-content">
				<div class="admin-header">
					<h1><i class="fas fa-upload me-2"></i>Importar Dados</h1>
				</div>
			</div>
			<div class="container-fluid px-4">
				<form @submit.prevent="importCSV">
					<div class="row g-3 align-items-end">
						<!-- Select usuário -->
						<div class="col-lg-4 col-md-6 col-12">
							<label for="userSelect" class="modern-label">Selecione um usuário:</label>
							<div class="modern-input-wrapper">
								<select
									id="userSelect"
									v-model="selectedUser"
									class="form-control modern-input"
									required
								>
									<option selected disabled value="">Escolha um usuário...</option>
									<option v-for="user in users" :key="user.id" :value="user.id">
										{{ user.id }} - {{ user.name }}
									</option>
								</select>
							</div>
						</div>

						<!-- Upload de arquivo -->
						<div class="col-lg-5 col-md-6 col-12">
							<label for="csvFile" class="modern-label">Escolha um arquivo .CSV</label>
							<div class="custom-file-upload">
								<label for="csvFile" class="upload-label">
									<i class="fas fa-file-upload me-2"></i>
									Selecionar arquivo
								</label>
								<input
									type="file"
									id="csvFile"
									name="file"
									class="upload-input"
									accept=".csv"
									@change="onChange"
								/>
								<span v-if="csvFile" class="file-name">{{ csvFile.name }}</span>
							</div>
							<div v-show="show" class="error-message mt-2">
								<i class="fas fa-exclamation-triangle me-1"></i>
								{{ message }}
							</div>
						</div>

						<!-- Botão importar -->
						<div class="col-lg-3 col-12">
							<button
								type="submit"
								class="btn btn-agro w-100 import-button"
								:disabled="!selectedUser || !csvFile || show || csvLoading"
							>
								<template v-if="csvLoading">
									<span
										class="spinner-border spinner-border-sm me-2"
										role="status"
										aria-hidden="true"
									></span>
									Carregando...
								</template>
								<template v-else>
									<i class="fas fa-upload me-2"></i>
									Importar
								</template>
							</button>
						</div>
					</div>
				</form>
			</div>
		</div>

		<!-- BAIXAR MODELO -->
		<div class="model-box mt-4">
			<div class="admin-content">
				<div class="admin-header">
					<h1><i class="fas fa-download me-2"></i>Baixar modelo de importação</h1>
				</div>
			</div>
			<div class="container-fluid px-4">
				<div class="row g-3 align-items-center">
					<!-- Texto explicativo -->
					<div class="col-lg-9 col-md-8 col-12">
						<div class="info-text">
							<i class="fas fa-info-circle text-primary me-2"></i>
							<span>
								Para a importação ser bem sucedida, os dados devem seguir uma planilha padrão.
								Baixe o modelo ao lado para preenchimento correto.
							</span>
						</div>
					</div>

					<!-- Botão baixar modelo -->
					<div class="col-lg-3 col-md-4 col-12">
						<button
							type="button"
							@click="downloadCSV()"
							class="btn btn-agro w-100 download-button"
						>
							<i class="fas fa-download me-2"></i>
							Baixar modelo
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script src="./DataImport"></script>

<style lang="scss">
.dataImportWrapper {
	background-color: #f8f9fa;
	border-radius: 20px;
	padding: 1.5rem;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

	h1 {
		color: #3d8160;
		font-family: "Lexend", sans-serif;
		font-weight: 600;
		font-size: 1.5rem;
		margin: 0;
		padding: 1rem 0;
		display: flex;
		align-items: center;
	}

	.data-box,
	.model-box {
		background: white;
		padding: 1.5rem;
		border-radius: 15px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
		border: 1px solid #e9ecef;
	}

	.model-box {
		background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	}

	.container-fluid {
		padding-left: 0;
		padding-right: 0;
	}
}

/* Estilo moderno para select e input */
.modern-label {
	font-weight: 600;
	color: #495057;
	margin-bottom: 0.5rem;
	display: block;
	font-size: 0.95rem;
}

.modern-input-wrapper {
	position: relative;
}

.modern-input {
	width: 100%;
	padding: 0.75rem 1rem;
	border: 2px solid #e9ecef;
	border-radius: 12px;
	background-color: #fff;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
	transition: all 0.3s ease;
	font-size: 0.95rem;

	&:focus {
		outline: none;
		border-color: #3d8160;
		box-shadow: 0 0 0 3px rgba(61, 129, 96, 0.1);
	}

	&:hover {
		border-color: #3d8160;
	}
}

/* Estilo do campo de upload */
.custom-file-upload {
	position: relative;
	display: flex;
	align-items: center;
	gap: 1rem;
	flex-wrap: wrap;
}

.upload-input {
	display: none;
}

.upload-label {
	background: linear-gradient(135deg, #3d8160 0%, #2d6047 100%);
	color: #fff;
	padding: 0.75rem 1.5rem;
	border-radius: 12px;
	cursor: pointer;
	display: flex;
	align-items: center;
	font-weight: 500;
	transition: all 0.3s ease;
	border: none;
	box-shadow: 0 2px 8px rgba(61, 129, 96, 0.3);
	white-space: nowrap;

	&:hover {
		transform: translateY(-1px);
		box-shadow: 0 4px 12px rgba(61, 129, 96, 0.4);
		background: linear-gradient(135deg, #2d6047 0%, #1e4032 100%);
	}

	&:active {
		transform: translateY(0);
	}
}

.file-name {
	font-size: 0.9rem;
	color: #495057;
	font-weight: 500;
	background: #e9ecef;
	padding: 0.5rem 1rem;
	border-radius: 8px;
	border: 1px solid #dee2e6;
	max-width: 200px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.error-message {
	color: #dc3545;
	font-size: 0.875rem;
	font-weight: 500;
}

/* Estilos dos botões */
.import-button,
.download-button {
	background: linear-gradient(135deg, #3d8160 0%, #2d6047 100%);
	border: none;
	border-radius: 12px;
	padding: 0.75rem 1.5rem;
	font-weight: 600;
	font-size: 0.95rem;
	color: white;
	transition: all 0.3s ease;
	box-shadow: 0 2px 8px rgba(61, 129, 96, 0.3);
	min-height: 48px;
	display: flex;
	align-items: center;
	justify-content: center;

	&:hover:not(:disabled) {
		transform: translateY(-1px);
		box-shadow: 0 4px 12px rgba(61, 129, 96, 0.4);
		background: linear-gradient(135deg, #2d6047 0%, #1e4032 100%);
	}

	&:active:not(:disabled) {
		transform: translateY(0);
	}

	&:disabled {
		opacity: 0.6;
		cursor: not-allowed;
		transform: none;
		box-shadow: 0 2px 4px rgba(61, 129, 96, 0.2);
	}
}

.info-text {
	background: #e7f3ff;
	padding: 1rem;
	border-radius: 10px;
	border-left: 4px solid #007bff;
	font-size: 0.95rem;
	line-height: 1.5;
	color: #495057;
}

/* Responsividade */
@media (max-width: 992px) {
	.dataImportWrapper {
		padding: 1rem;
	}

	.data-box,
	.model-box {
		padding: 1rem;
	}

	h1 {
		font-size: 1.3rem;
	}
}

@media (max-width: 768px) {
	.custom-file-upload {
		flex-direction: column;
		align-items: flex-start;
		gap: 0.75rem;
	}

	.upload-label {
		width: 100%;
		justify-content: center;
	}

	.file-name {
		max-width: 100%;
		width: 100%;
	}

	.info-text {
		text-align: left;
		margin-bottom: 1rem;
	}

	.import-button,
	.download-button {
		width: 100%;
		margin-top: 1rem;
	}
}

@media (max-width: 576px) {
	.dataImportWrapper {
		margin-top: 1rem;
		padding: 0.75rem;
	}

	.data-box,
	.model-box {
		padding: 0.75rem;
	}

	h1 {
		font-size: 1.2rem;
		padding: 0.75rem 0;
	}

	.modern-input,
	.upload-label {
		padding: 0.6rem 1rem;
	}
}
</style>
