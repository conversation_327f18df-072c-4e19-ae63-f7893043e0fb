<template>
	<div id="reports" class="mt-5 dataImportWrapper">
		<!-- IMPORTAÇÃO DE DADOS -->
		<div class="data-box">
			<div class="admin-content">
				<div class="admin-header d-flex">
					<h1>Importar Dados</h1>
				</div>
			</div>
			<div class="container">
				<form @submit.prevent="importCSV" class="container">
					<div class="row p-2">
						<!-- Select usuário -->
						<div class="col-md-4 col-12 mb-3">
							<label for="userSelect" class="modern-label">Selecione um usuário:</label>
							<div class="modern-input-wrapper">
								<select
									id="userSelect"
									v-model="selectedUser"
									class="form-control modern-input"
									required
								>
									<option selected disabled value="">Escolha um usuário...</option>
									<option v-for="user in users" :key="user.id" :value="user.id">
										{{ user.id }} - {{ user.name }}
									</option>
								</select>
							</div>
						</div>

						<!-- Upload de arquivo -->
						<div class="col-md-5 col-12 mb-3">
							<label for="csvFile" class="modern-label">Escolha um arquivo .CSV</label>
							<div class="custom-file-upload">
								<label for="csvFile" class="upload-label">
									<i class="fas fa-file-upload"></i> Selecionar arquivo
								</label>
								<input
									type="file"
									id="csvFile"
									name="file"
									class="upload-input"
									accept=".csv"
									@change="onChange"
								/>
								<span v-if="csvFile" class="file-name">{{ csvFile.name }}</span>
							</div>
							<p class="text-danger" v-show="show">{{ message }}</p>
						</div>

						<!-- Botão importar -->
						<div class="col-md-3 col-12 mb-3 d-flex align-items-end">
							<button
								type="submit"
								class="btn btn-agro w-100"
								:disabled="!selectedUser || !csvFile || show || csvLoading"
							>
								<span
									v-if="csvLoading"
									class="spinner-border spinner-border-sm"
									role="status"
									aria-hidden="true"
								></span>
								<span v-if="csvLoading">Carregando...</span>
								<i class="fas fa-upload"></i> Importar
							</button>
						</div>
					</div>
				</form>
			</div>
		</div>

		<!-- BAIXAR MODELO -->
		<div class="model-box mt-4">
			<div class="admin-content">
				<div class="admin-header d-flex">
					<h1>Baixar modelo de importação</h1>
				</div>
			</div>
			<div class="container">
				<form class="container">
					<div class="row p-2">
						<!-- Texto explicativo -->
						<div class="col-md-9 col-12 mb-3">
							<label class="modern-label">
								Para a importação ser bem sucedida, os dados devem seguir uma planilha padrão. Ao lado, você pode baixar o modelo para preenchimento.
							</label>
							<p style="color: red" v-show="show">{{ message }}</p>
						</div>

						<!-- Botão baixar modelo -->
						<div class="col-md-3 col-12 mb-3 d-flex align-items-end">
							<button
								type="button"
								@click="downloadCSV()"
								class="btn btn-agro w-100"
							>
								<i class="fas fa-download"></i> Baixar modelo
							</button>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
</template>

<script src="./DataImport"></script>

<style lang="scss">
.dataImportWrapper {
	background-color: #f5f8fd;
	border-radius: 20px;

	h1 {
		color: #3d8160;
		font-family: "Lexend", sans-serif;
		font-weight: 600;
		font-size: 24px;
		padding: 16px 0px 16px 16px;
		display: flex;
		flex: flex-start;
	}

	.data-box,
	.model-box {
		padding: 2.7%;
		border-radius: 20px;
		display: block;
		justify-content: center;
	}

	.container {
		margin-left: 0;
		margin-right: 0;
		text-align: justify;
	}

	.btn {
		height: auto;
		width: 8.5rem;
	}
}

/* Estilo moderno para select e input */
.modern-label {
	font-weight: 600;
	color: #333;
	margin-bottom: 0.5rem;
	display: block;
}

.modern-input-wrapper {
	position: relative;
}

.modern-input {
	width: 100%;
	padding: 0.6rem 1rem;
	border: 1px solid #ced4da;
	border-radius: 10px;
	background-color: #fff;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
	appearance: none;
}

/* Estilo do campo de upload */
.custom-file-upload {
	position: relative;
	display: flex;
	align-items: center;
	gap: 0.8rem;
}

.upload-input {
	display: none;
}

.upload-label {
	background-color: #3d8160;
	color: #fff;
	padding: 0.6rem 1.2rem;
	border-radius: 10px;
	cursor: pointer;
	display: flex;
	align-items: center;
	gap: 0.5rem;
	font-weight: 500;
	transition: background-color 0.3s;
}

.upload-label:hover {
	background-color: #2d6047;
}

.file-name {
	font-size: 0.95rem;
	color: #333;
	font-weight: 500;
}

@media (max-width: 768px) {
	.d-flex {
		flex-direction: column;
		gap: 1rem;
	}

	.form-group {
		width: 100% !important;
		margin-left: 0 !important;
	}

	.btn {
		width: 100%;
	}
}
</style>
