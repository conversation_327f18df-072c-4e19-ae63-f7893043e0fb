<template>
	<div class="data-import-container">
		<!-- Header Principal -->
		<div class="page-header">
			<div class="header-content">
				<div class="header-icon">
					<i class="fas fa-file-import"></i>
				</div>
				<div class="header-text">
					<h1>Importação de Dados</h1>
					<p>Gerencie a importação de dados do sistema de forma simples e eficiente</p>
				</div>
			</div>
		</div>

		<!-- Cards Container -->
		<div class="cards-container">
			<!-- Card de Importação -->
			<div class="import-card">
				<div class="card-header">
					<div class="card-icon import-icon">
						<i class="fas fa-cloud-upload-alt"></i>
					</div>
					<div class="card-title">
						<h2>Importar Dados</h2>
						<p>Faça upload do seu arquivo CSV para importar dados</p>
					</div>
				</div>

				<div class="card-content">
					<form @submit.prevent="importCSV" class="import-form">
						<!-- Seleção de Usuário -->
						<div class="form-group">
							<label class="form-label">
								<i class="fas fa-user"></i>
								Selecionar Usuário
							</label>
							<div class="select-wrapper">
								<select
									v-model="selectedUser"
									class="form-select"
									required
								>
									<option value="" disabled>Escolha um usuário...</option>
									<option v-for="user in users" :key="user.id" :value="user.id">
										{{ user.name }} (ID: {{ user.id }})
									</option>
								</select>
								<i class="fas fa-chevron-down select-arrow"></i>
							</div>
						</div>

						<!-- Upload de Arquivo -->
						<div class="form-group">
							<label class="form-label">
								<i class="fas fa-file-csv"></i>
								Arquivo CSV
							</label>
							<div class="file-upload-area" :class="{ 'has-file': csvFile, 'drag-over': dragOver }"
								 @dragover.prevent="dragOver = true"
								 @dragleave.prevent="dragOver = false"
								 @drop.prevent="handleDrop">
								<input
									type="file"
									id="csvFile"
									class="file-input"
									accept=".csv"
									@change="onChange"
									ref="fileInput"
								/>
								<div class="upload-content" v-if="!csvFile">
									<div class="upload-icon">
										<i class="fas fa-cloud-upload-alt"></i>
									</div>
									<div class="upload-text">
										<p class="primary-text">Clique para selecionar ou arraste o arquivo</p>
										<p class="secondary-text">Apenas arquivos .CSV são aceitos</p>
									</div>
									<button type="button" class="select-file-btn" @click="$refs.fileInput.click()">
										<i class="fas fa-folder-open"></i>
										Selecionar Arquivo
									</button>
								</div>
								<div class="file-selected" v-else>
									<div class="file-info">
										<div class="file-icon">
											<i class="fas fa-file-csv"></i>
										</div>
										<div class="file-details">
											<p class="file-name">{{ csvFile.name }}</p>
											<p class="file-size">{{ formatFileSize(csvFile.size) }}</p>
										</div>
									</div>
									<button type="button" class="remove-file-btn" @click="removeFile">
										<i class="fas fa-times"></i>
									</button>
								</div>
							</div>
							<div class="error-message" v-if="show">
								<i class="fas fa-exclamation-triangle"></i>
								{{ message }}
							</div>
						</div>

						<!-- Botão de Importação -->
						<div class="form-actions">
							<button
								type="submit"
								class="import-btn"
								:disabled="!selectedUser || !csvFile || show || csvLoading"
								:class="{ 'loading': csvLoading }"
							>
								<div class="btn-content">
									<div class="btn-icon" v-if="!csvLoading">
										<i class="fas fa-upload"></i>
									</div>
									<div class="btn-spinner" v-if="csvLoading">
										<div class="spinner"></div>
									</div>
									<span class="btn-text">
										{{ csvLoading ? 'Importando...' : 'Importar Dados' }}
									</span>
								</div>
								<div class="btn-progress" v-if="csvLoading"></div>
							</button>
						</div>
					</form>
				</div>
			</div>

			<!-- Card de Download do Modelo -->
			<div class="download-card">
				<div class="card-header">
					<div class="card-icon download-icon">
						<i class="fas fa-download"></i>
					</div>
					<div class="card-title">
						<h2>Modelo de Importação</h2>
						<p>Baixe o modelo padrão para estruturar seus dados</p>
					</div>
				</div>

				<div class="card-content">
					<div class="info-section">
						<div class="info-item">
							<div class="info-icon">
								<i class="fas fa-info-circle"></i>
							</div>
							<div class="info-text">
								<h4>Formato Obrigatório</h4>
								<p>Para garantir uma importação bem-sucedida, seus dados devem seguir exatamente a estrutura do modelo fornecido.</p>
							</div>
						</div>

						<div class="info-item">
							<div class="info-icon">
								<i class="fas fa-check-circle"></i>
							</div>
							<div class="info-text">
								<h4>Dados Validados</h4>
								<p>O modelo inclui todas as colunas necessárias e exemplos de preenchimento correto.</p>
							</div>
						</div>
					</div>

					<div class="download-actions">
						<button
							type="button"
							@click="downloadCSV()"
							class="download-btn"
						>
							<div class="btn-content">
								<div class="btn-icon">
									<i class="fas fa-download"></i>
								</div>
								<span class="btn-text">Baixar Modelo CSV</span>
							</div>
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script src="./DataImport"></script>

<style lang="scss" scoped>
// Variáveis de cores
$primary-color: #3d8160;
$primary-dark: #2d6047;
$primary-light: #e8f5e8;
$secondary-color: #6c757d;
$success-color: #28a745;
$danger-color: #dc3545;
$warning-color: #ffc107;
$info-color: #17a2b8;
$light-color: #f8f9fa;
$dark-color: #343a40;
$white: #ffffff;
$border-color: #dee2e6;
$shadow-light: rgba(0, 0, 0, 0.1);
$shadow-medium: rgba(0, 0, 0, 0.15);
$shadow-dark: rgba(0, 0, 0, 0.25);

// Container principal
.data-import-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	padding: 2rem 1rem;
	font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

// Header da página
.page-header {
	background: $white;
	border-radius: 20px;
	padding: 2rem;
	margin-bottom: 2rem;
	box-shadow: 0 10px 30px $shadow-light;
	border: 1px solid rgba(255, 255, 255, 0.2);
	backdrop-filter: blur(10px);

	.header-content {
		display: flex;
		align-items: center;
		gap: 1.5rem;
	}

	.header-icon {
		width: 70px;
		height: 70px;
		background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
		border-radius: 20px;
		display: flex;
		align-items: center;
		justify-content: center;
		color: $white;
		font-size: 2rem;
		box-shadow: 0 8px 25px rgba($primary-color, 0.3);
	}

	.header-text {
		h1 {
			font-size: 2.5rem;
			font-weight: 700;
			color: $dark-color;
			margin: 0 0 0.5rem 0;
			background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			background-clip: text;
		}

		p {
			font-size: 1.1rem;
			color: $secondary-color;
			margin: 0;
			font-weight: 400;
		}
	}
}

// Container dos cards
.cards-container {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 2rem;
	max-width: 1400px;
	margin: 0 auto;
}

// Estilos base dos cards
.import-card,
.download-card {
	background: $white;
	border-radius: 24px;
	overflow: hidden;
	box-shadow: 0 15px 35px $shadow-light;
	border: 1px solid rgba(255, 255, 255, 0.2);
	backdrop-filter: blur(10px);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

	&:hover {
		transform: translateY(-5px);
		box-shadow: 0 25px 50px $shadow-medium;
	}
}

// Header dos cards
.card-header {
	padding: 2rem;
	background: linear-gradient(135deg, $primary-light 0%, rgba($primary-color, 0.1) 100%);
	display: flex;
	align-items: center;
	gap: 1.5rem;
	border-bottom: 1px solid rgba($primary-color, 0.1);

	.card-icon {
		width: 60px;
		height: 60px;
		border-radius: 16px;
		display: flex;
		align-items: center;
		justify-content: center;
		color: $white;
		font-size: 1.5rem;
		box-shadow: 0 8px 20px rgba($primary-color, 0.3);

		&.import-icon {
			background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
		}

		&.download-icon {
			background: linear-gradient(135deg, $info-color 0%, darken($info-color, 10%) 100%);
		}
	}

	.card-title {
		h2 {
			font-size: 1.5rem;
			font-weight: 600;
			color: $dark-color;
			margin: 0 0 0.5rem 0;
		}

		p {
			font-size: 0.95rem;
			color: $secondary-color;
			margin: 0;
		}
	}
}

// Conteúdo dos cards
.card-content {
	padding: 2rem;
}

// Formulário de importação
.import-form {
	display: flex;
	flex-direction: column;
	gap: 2rem;
}

.form-group {
	display: flex;
	flex-direction: column;
	gap: 0.75rem;
}

.form-label {
	font-weight: 600;
	color: $dark-color;
	font-size: 1rem;
	display: flex;
	align-items: center;
	gap: 0.5rem;

	i {
		color: $primary-color;
		font-size: 1.1rem;
	}
}

// Select customizado
.select-wrapper {
	position: relative;

	.form-select {
		width: 100%;
		padding: 1rem 3rem 1rem 1rem;
		border: 2px solid $border-color;
		border-radius: 12px;
		background: $white;
		font-size: 1rem;
		color: $dark-color;
		appearance: none;
		cursor: pointer;
		transition: all 0.3s ease;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

		&:focus {
			outline: none;
			border-color: $primary-color;
			box-shadow: 0 0 0 3px rgba($primary-color, 0.1);
		}

		&:hover {
			border-color: lighten($primary-color, 20%);
		}
	}

	.select-arrow {
		position: absolute;
		right: 1rem;
		top: 50%;
		transform: translateY(-50%);
		color: $secondary-color;
		pointer-events: none;
		transition: transform 0.3s ease;
	}

	&:hover .select-arrow {
		color: $primary-color;
	}
}

// Área de upload
.file-upload-area {
	border: 2px dashed $border-color;
	border-radius: 16px;
	padding: 0.5rem;
	text-align: center;
	background: $light-color;
	transition: all 0.3s ease;
	cursor: pointer;
	position: relative;
	overflow: hidden;

	&:hover {
		border-color: $primary-color;
		background: rgba($primary-color, 0.05);
	}

	&.drag-over {
		border-color: $primary-color;
		background: rgba($primary-color, 0.1);
		transform: scale(1.02);
	}

	&.has-file {
		border-color: $success-color;
		background: rgba($success-color, 0.05);
	}

	.file-input {
		display: none;
	}
}

.upload-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 1rem;

	.upload-icon {
		font-size: 3rem;
		color: $primary-color;
		margin-bottom: 0.5rem;
	}

	.upload-text {
		.primary-text {
			font-size: 1.1rem;
			font-weight: 600;
			color: $dark-color;
			margin: 0 0 0.25rem 0;
		}

		.secondary-text {
			font-size: 0.9rem;
			color: $secondary-color;
			margin: 0;
		}
	}

	.select-file-btn {
		background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
		color: $white;
		border: none;
		padding: 0.75rem 1.5rem;
		border-radius: 10px;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.3s ease;
		display: flex;
		align-items: center;
		gap: 0.5rem;
		box-shadow: 0 4px 15px rgba($primary-color, 0.3);

		&:hover {
			transform: translateY(-2px);
			box-shadow: 0 6px 20px rgba($primary-color, 0.4);
		}
	}
}

.file-selected {
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: $white;
	border-radius: 12px;
	padding: 1rem;
	border: 1px solid $success-color;

	.file-info {
		display: flex;
		align-items: center;
		gap: 1rem;

		.file-icon {
			width: 40px;
			height: 40px;
			background: linear-gradient(135deg, $success-color 0%, darken($success-color, 10%) 100%);
			border-radius: 8px;
			display: flex;
			align-items: center;
			justify-content: center;
			color: $white;
			font-size: 1.2rem;
		}

		.file-details {
			.file-name {
				font-weight: 600;
				color: $dark-color;
				margin: 0 0 0.25rem 0;
				font-size: 1rem;
			}

			.file-size {
				color: $secondary-color;
				margin: 0;
				font-size: 0.85rem;
			}
		}
	}

	.remove-file-btn {
		width: 32px;
		height: 32px;
		border-radius: 50%;
		border: none;
		background: $danger-color;
		color: $white;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;

		&:hover {
			background: darken($danger-color, 10%);
			transform: scale(1.1);
		}
	}
}

.error-message {
	display: flex;
	align-items: center;
	gap: 0.5rem;
	color: $danger-color;
	font-size: 0.9rem;
	font-weight: 500;
	background: rgba($danger-color, 0.1);
	padding: 0.75rem 1rem;
	border-radius: 8px;
	border-left: 4px solid $danger-color;

	i {
		font-size: 1rem;
	}
}

// Botões de ação
.form-actions {
	margin-top: 1rem;
}

.import-btn,
.download-btn {
	width: 100%;
	border: none;
	border-radius: 12px;
	padding: 1rem 2rem;
	font-weight: 600;
	font-size: 1rem;
	cursor: pointer;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;
	box-shadow: 0 4px 15px rgba($primary-color, 0.3);

	&:disabled {
		opacity: 0.6;
		cursor: not-allowed;
		transform: none !important;
	}

	&:not(:disabled):hover {
		transform: translateY(-2px);
		box-shadow: 0 8px 25px rgba($primary-color, 0.4);
	}

	.btn-content {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 0.75rem;
		position: relative;
		z-index: 2;
	}

	.btn-icon {
		font-size: 1.1rem;
	}

	.btn-text {
		font-weight: 600;
	}
}

.import-btn {
	background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
	color: $white;

	&.loading {
		.btn-content {
			opacity: 0.8;
		}
	}

	.btn-progress {
		position: absolute;
		bottom: 0;
		left: 0;
		height: 3px;
		background: rgba($white, 0.3);
		animation: progress 2s infinite;
	}
}

.download-btn {
	background: linear-gradient(135deg, $info-color 0%, darken($info-color, 10%) 100%);
	color: $white;
}

// Spinner personalizado
.btn-spinner {
	.spinner {
		width: 20px;
		height: 20px;
		border: 2px solid rgba($white, 0.3);
		border-top: 2px solid $white;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}
}

// Seção de informações
.info-section {
	display: flex;
	flex-direction: column;
	gap: 1.5rem;
	margin-bottom: 2rem;
}

.info-item {
	display: flex;
	align-items: flex-start;
	gap: 1rem;

	.info-icon {
		width: 40px;
		height: 40px;
		border-radius: 10px;
		background: linear-gradient(135deg, $info-color 0%, darken($info-color, 10%) 100%);
		display: flex;
		align-items: center;
		justify-content: center;
		color: $white;
		font-size: 1.1rem;
		flex-shrink: 0;
	}

	.info-text {
		h4 {
			font-size: 1.1rem;
			font-weight: 600;
			color: $dark-color;
			margin: 0 0 0.5rem 0;
		}

		p {
			font-size: 0.95rem;
			color: $secondary-color;
			margin: 0;
			line-height: 1.5;
		}
	}
}

// Animações
@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

@keyframes progress {
	0% { width: 0%; }
	50% { width: 70%; }
	100% { width: 100%; }
}

// Responsividade
@media (max-width: 1200px) {
	.cards-container {
		grid-template-columns: 1fr;
		gap: 1.5rem;
	}
}

@media (max-width: 768px) {
	.data-import-container {
		padding: 1rem 0.5rem;
	}

	.page-header {
		padding: 1.5rem;
		margin-bottom: 0.5rem;

		.header-content {
			flex-direction: column;
			text-align: center;
			gap: 1rem;
		}

		.header-text h1 {
			font-size: 2rem;
		}
	}

	.card-header {
		padding: 0.5rem;
		flex-direction: column;
		text-align: center;
		gap: 1rem;
	}

	.card-content {
		padding: 0.5rem;
	}

	.file-upload-area {
		padding: 0.5rem;
	}

	.upload-content .upload-icon {
		font-size: 2.5rem;
	}

	.file-selected {
		flex-direction: column;
		gap: 1rem;
		align-items: flex-start;

		.remove-file-btn {
			align-self: flex-end;
		}
	}

	.info-item {
		flex-direction: column;
		text-align: center;
	}
}

@media (max-width: 480px) {
	.page-header .header-text h1 {
		font-size: 1.75rem;
	}

	.card-header .card-title h2 {
		font-size: 1.25rem;
	}

	.upload-content .upload-text .primary-text {
		font-size: 1rem;
	}
}
</style>
