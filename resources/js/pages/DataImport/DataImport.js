export default {
	data() {
		return {
			users: [],
			selectedUser: "",
			authToken: null,
			target: null,
			fileFormat: null,
			csvLoading: false,
			csvFile: null,
			message: null,
			show: true,
		};
	},

	created() {
		this.authToken = window.token;
		this.fetchUsers();
	},

	methods: {
		fetchUsers() {
			axios
				.get("/api/v1/users", { // Substitua pelo endpoint correto da sua API
					headers: { Authorization: `Bearer ${this.authToken}` },
				})
				.then((response) => {
					this.users = response.data.data; // Espera-se que a API retorne uma lista de usuários
				})
				.catch((err) => {
					console.error("Erro ao buscar usuários:", err.response);
				});
		},

		onChange(e) {
			if (e.target.files[0]?.type.match("text/csv")) {
				this.csvFile = e.target.files[0];
				this.show = false;
				this.message = "";
			} else {
				this.csvFile = null;
				this.show = true;
				this.message = "Tipo de arquivo não aceito!";
			}
		},

		importCSV() {
			this.csvLoading = true;
			let formData = new FormData();

			formData.append('file', this.csvFile);
			formData.append("user_id", this.selectedUser);

			alert("Aguarde alguns minutos, pois o processo pode ser demorado");

			axios
				.post("/api/v1/import", formData, {
					headers: { authorization: `bearer ${this.authToken}` },
				})
				.then((response) => {
					console.log(response.data);
					Swal.fire({
						title: "Arquivo importado com sucesso!",
						text: "Seus dados serão mostrados no sistema dentro de alguns minutos.",
						icon: "success",
						showConfirmButton: true,
					});
					this.csvLoading = false;
					this.csvFile = null;
					this.selectedUser = "";
				})
				.catch((err) => {
					console.log(err.response);
					this.csvLoading = null;
				});
		},

		downloadCSV() {
			axios
				.get(
					"/api/v1/export-model-csv", // Alterado para GET
					{
						responseType: "arraybuffer",
						headers: { Authorization: `Bearer ${this.authToken}` }, // Ainda incluímos o token se necessário
					}
				)
				.then((response) => {
					this.downloadFile(response, "modelo", "csv");
				})
				.catch((err) => {
					console.error(err.response);
				});
		},

		downloadFile(response, filename, fileformat) {
			let type = "text/csv";
			var newBlob = new Blob([response.data], { type: type });
			const data = window.URL.createObjectURL(newBlob);
			var link = document.createElement("a");

			link.href = data;
			link.download = `${filename}.${fileformat}`;
			link.click();

			setTimeout(function () {
				window.URL.revokeObjectURL(data);
			}, 100);
		},


	},
};
