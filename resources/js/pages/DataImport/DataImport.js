export default {
	data() {
		return {
			users: [],
			selectedUser: "",
			authToken: null,
			target: null,
			fileFormat: null,
			csvLoading: false,
			csvFile: null,
			message: null,
			show: false,
			dragOver: false,
		};
	},

	created() {
		this.authToken = window.token;
		this.fetchUsers();
	},

	methods: {
		fetchUsers() {
			axios
				.get("/api/v1/users", { // Substitua pelo endpoint correto da sua API
					headers: { Authorization: `Bearer ${this.authToken}` },
				})
				.then((response) => {
					this.users = response.data.data; // Espera-se que a API retorne uma lista de usuários
				})
				.catch((err) => {
					console.error("Erro ao buscar usuários:", err.response);
				});
		},

		onChange(e) {
			const file = e.target.files[0];
			this.handleFile(file);
		},

		handleFile(file) {
			if (file && file.type.match("text/csv")) {
				this.csvFile = file;
				this.show = false;
				this.message = "";
			} else if (file) {
				this.csvFile = null;
				this.show = true;
				this.message = "Tipo de arquivo não aceito! Apenas arquivos .CSV são permitidos.";
			}
		},

		handleDrop(e) {
			this.dragOver = false;
			const files = e.dataTransfer.files;
			if (files.length > 0) {
				this.handleFile(files[0]);
			}
		},

		removeFile() {
			this.csvFile = null;
			this.show = false;
			this.message = "";
			this.$refs.fileInput.value = "";
		},

		formatFileSize(bytes) {
			if (bytes === 0) return '0 Bytes';
			const k = 1024;
			const sizes = ['Bytes', 'KB', 'MB', 'GB'];
			const i = Math.floor(Math.log(bytes) / Math.log(k));
			return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
		},

		importCSV() {
			this.csvLoading = true;
			let formData = new FormData();

			formData.append('file', this.csvFile);
			formData.append("user_id", this.selectedUser);

			// Toast de início do processo
			Swal.fire({
				title: "Iniciando Importação",
				text: "Aguarde alguns minutos, pois o processo pode ser demorado...",
				icon: "info",
				showConfirmButton: false,
				timer: 3000,
				timerProgressBar: true
			});

			axios
				.post("/api/v1/import", formData, {
					headers: { authorization: `bearer ${this.authToken}` },
				})
				.then((response) => {
					console.log(response.data);
					Swal.fire({
						title: "Importação Concluída!",
						text: "Seus dados foram importados com sucesso e estarão disponíveis no sistema em alguns minutos.",
						icon: "success",
						confirmButtonText: "Entendi",
						confirmButtonColor: "#3d8160"
					});
					this.csvLoading = false;
					this.csvFile = null;
					this.selectedUser = "";
					this.$refs.fileInput.value = "";
				})
				.catch((err) => {
					console.error("Erro na importação:", err.response);
					Swal.fire({
						title: "Erro na Importação",
						text: "Ocorreu um erro durante a importação. Verifique o arquivo e tente novamente.",
						icon: "error",
						confirmButtonText: "Tentar Novamente",
						confirmButtonColor: "#dc3545"
					});
					this.csvLoading = false;
				});
		},

		downloadCSV() {
			axios
				.get(
					"/api/v1/export-model-csv", // Alterado para GET
					{
						responseType: "arraybuffer",
						headers: { Authorization: `Bearer ${this.authToken}` }, // Ainda incluímos o token se necessário
					}
				)
				.then((response) => {
					this.downloadFile(response, "modelo", "csv");
				})
				.catch((err) => {
					console.error(err.response);
				});
		},

		downloadFile(response, filename, fileformat) {
			let type = "text/csv";
			var newBlob = new Blob([response.data], { type: type });
			const data = window.URL.createObjectURL(newBlob);
			var link = document.createElement("a");

			link.href = data;
			link.download = `${filename}.${fileformat}`;
			link.click();

			setTimeout(function () {
				window.URL.revokeObjectURL(data);
			}, 100);
		},


	},
};
