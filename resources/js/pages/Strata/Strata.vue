<template>
	<div class="strata-container">
		<!-- Header Principal -->
		<div class="page-header">
			<div class="header-content">
				<div class="header-navigation">
					<back-button
						title="Voltar para áreas homogêneas"
					></back-button>
				</div>
				<div class="header-icon">
					<i class="fas fa-th-large"></i>
				</div>
				<div class="header-text">
					<h1 class="page-title">Unidades Operacionais</h1>
					<p class="page-subtitle">
						{{ strata[0]?.homogeneous_area?.property?.name ?
							strata[0]?.homogeneous_area?.property?.name + " / Área Homogênea " + strata[0]?.homogeneous_area.label :
							'Gerencie as unidades operacionais da área homogênea' }}
					</p>
				</div>
				<div class="header-actions">
					<button
						type="button"
						class="btn-primary"
						@click="addModal"
					>
						<i class="fas fa-plus"></i>
						<span>Adicionar Unidade</span>
					</button>
				</div>
			</div>
		</div>

		<!-- <PERSON><PERSON><PERSON><PERSON> Principal -->
		<div class="main-content">
			<!-- Loading State -->
			<div v-if="loading" class="loading-section">
				<div class="loading-card">
					<loader :loading="loading"></loader>
					<p>Carregando unidades operacionais...</p>
				</div>
			</div>

			<!-- Strata Table - Mantendo estrutura original -->
			<div v-else class="strata-table-modern">
				<vue-good-table
					title="Unidades Operacionais"
					:columns="fields"
					:rows="strata"
					:pagination-options="paginationOptions"
					:fixed-header="true"
					:search-options="{
						enabled: true,
						placeholder: 'Pesquisar...',
					}"
					:sort-options="{
						enabled: true,
						initialSortBy: [
							{ field: 'status', type: 'desc' },
							{ field: 'label', type: 'asc' },
						],
					}"
					compactMode
				>
					<div slot="emptystate">
						Não há registros para esta pesquisa
					</div>
					<template slot="table-row" slot-scope="props">
						<div
							v-if="props.column.field == 'status'"
							class="custom-control custom-switch"
						>
							<input
								type="checkbox"
								class="custom-control-input"
								v-bind:id="`status_${props.row.id}`"
								v-model="props.row.status"
								@click.prevent="
									props.row.status
										? changeStatus(
												props.row.id,
												(status = false)
										  )
										: changeStatus(props.row.id)
								"
							/>
							<label
								class="custom-control-label"
								v-bind:for="`status_${props.row.id}`"
							>
								{{ props.row.status ? "Desativar" : "Ativar" }}
							</label>
						</div>

						<div v-else-if="props.column.field == 'actions'">
							<button
								title="Editar"
								class="btn btn-sm btn-secondary"
								@click="editModal(props.row)"
								v-if="userRole !== 'pre-registered'"
							>
								<i class="fas fa-cog fa-lg"></i>
							</button>

							<router-link
								title="Pontos Amostrais"
								tag="button"
								class="btn btn-sm btn-secondary"
								v-bind:to="{
									path: '/panel/sampling-points',
									query: { strid: props.row.id },
								}"
							>
								<i class="fas fa-map-marker-alt fa-lg"></i>
							</router-link>
						</div>
					</template>
				</vue-good-table>
			</div>
		</div>

		<!-- Modais mantidos exatamente como estão -->
		<div
			class="modal fade"
			id="modalStrata"
				tabindex="-1"
				role="dialog"
				aria-labelledby="modalStrataLabel"
				aria-hidden="true"
			>
				<div class="modal-dialog modal-dialog-centered" role="document">
					<div class="modal-content">
						<div class="modal-header">
							<h5
								class="modal-title"
								id="modalStrataLabel"
								v-show="!editMode"
							>
								ADICIONAR UNIDADE OPERACIONAL
							</h5>
							<h5
								class="modal-title"
								id="modalStrataLabel"
								v-show="editMode"
							>
								{{ `ATUALIZAR ${stratum.label.toUpperCase()}` }}
							</h5>
							<button
								type="button"
								class="close"
								data-dismiss="modal"
								aria-label="Close"
							>
								<span aria-hidden="true">&times;</span>
							</button>
						</div>
						<div class="modal-body">
							<form
								@submit.prevent="editMode ? update() : store()"
							>
								<div class="form-group">
									<label for="label">Rótulo</label>
									<input
										id="label"
										type="text"
										class="form-control"
										placeholder="Nova Unidade Operacional"
										v-model="stratum.label"
									/>
								</div>

								<div class="form-group">
									<label for="stratumHArea"
										>Área Homogênea</label
									>
									<select
										:id="'stratumHArea'"
										name="stratum"
										class="form-control"
										v-model="stratum.homogeneous_area"
										required
									>
										<option
											v-for="homogeneous_area in homogeneous_areas"
											v-bind:value="homogeneous_area"
											v-bind:key="homogeneous_area.id"
										>
											{{
												`${homogeneous_area.label} (${
													homogeneous_area.status
														? "Ativo"
														: "Inativo"
												})`
											}}
										</option>
									</select>
								</div>

								<div class="modal-footer">
									<button
										type="button"
										class="btn btn-secondary"
										data-dismiss="modal"
									>
										Sair
									</button>
									<button
										type="submit"
										class="btn btn-primary"
										v-show="!editMode"
									>
										Cadastrar
									</button>
									<button
										type="submit"
										class="btn btn-primary"
										v-show="editMode"
									>
										Atualizar
									</button>
								</div>
							</form>
						</div>
					</div>
				</div>
		</div>
	</div>
</template>

<script src="./Strata.js"></script>

<style lang="scss" scoped>
// Variáveis
$primary-color: #3d8160;
$primary-dark: #2d6047;
$text-color: #2c3e50;
$text-light: #6c757d;
$white: #ffffff;
$background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
$shadow: rgba(0, 0, 0, 0.1);
$shadow-hover: rgba(0, 0, 0, 0.15);

// Container Principal
.strata-container {
	min-height: 100vh;
	background: $background;
	padding: 2rem;
	font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

	@media (max-width: 768px) {
		padding: 1rem;
	}
}

// Header Section
.page-header {
	background: $white;
	border-radius: 20px;
	padding: 2.5rem;
	margin-bottom: 2rem;
	box-shadow: 0 8px 25px $shadow;
	border: 1px solid rgba(255, 255, 255, 0.2);
	backdrop-filter: blur(10px);
	transition: transform 0.3s ease, box-shadow 0.3s ease;

	&:hover {
		transform: translateY(-2px);
		box-shadow: 0 12px 35px $shadow-hover;
	}

	.header-content {
		display: flex;
		align-items: center;
		gap: 2rem;

		@media (max-width: 768px) {
			flex-direction: column;
			text-align: center;
			gap: 1.5rem;
		}
	}

	.header-navigation {
		display: flex;
		align-items: center;
	}

	.header-icon {
		width: 70px;
		height: 70px;
		background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
		border-radius: 20px;
		display: flex;
		align-items: center;
		justify-content: center;
		color: $white;
		font-size: 2rem;
		box-shadow: 0 8px 25px rgba($primary-color, 0.3);
		transition: transform 0.3s ease;

		&:hover {
			transform: scale(1.05);
		}
	}

	.header-text {
		flex: 1;

		.page-title {
			font-size: 2.5rem;
			font-weight: 700;
			color: $primary-color;
			margin: 0 0 0.5rem 0;
		}

		.page-subtitle {
			font-size: 1.1rem;
			color: $text-light;
			margin: 0;
			font-weight: 400;
		}
	}

	.header-actions {
		display: flex;
		gap: 1rem;

		@media (max-width: 768px) {
			flex-direction: column;
			width: 100%;
		}
	}
}

// Main Content
.main-content {
	display: flex;
	flex-direction: column;
	gap: 2rem;
}

// Loading Section
.loading-section {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 400px;

	.loading-card {
		background: $white;
		border-radius: 20px;
		padding: 3rem;
		text-align: center;
		box-shadow: 0 8px 25px $shadow;
		border: 1px solid rgba(255, 255, 255, 0.2);

		p {
			margin-top: 1rem;
			color: $text-light;
			font-size: 1.1rem;
		}
	}
}

// Tabela - Mantendo funcionalidades, modernizando visual
.strata-table-modern {
	background: $white;
	border-radius: 20px;
	box-shadow: 0 8px 25px $shadow;
	padding: 2rem;
	transition: transform 0.3s ease;

	&:hover {
		transform: translateY(-2px);
	}
}

// Botões
.btn-primary {
	background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
	border: none;
	border-radius: 12px;
	padding: 0.75rem 1.5rem;
	font-weight: 600;
	font-size: 0.95rem;
	color: $white;
	display: flex;
	align-items: center;
	gap: 0.5rem;
	transition: all 0.3s ease;
	box-shadow: 0 4px 15px rgba($primary-color, 0.3);
	text-decoration: none;

	&:hover {
		transform: translateY(-2px);
		box-shadow: 0 6px 20px rgba($primary-color, 0.4);
		color: $white;
		text-decoration: none;
	}

	&:active {
		transform: translateY(0);
	}

	i {
		font-size: 1rem;
	}
}

// Responsividade
@media (max-width: 768px) {
	.strata-container {
		padding: 1rem;
	}

	.page-header {
		padding: 1.5rem;
		margin-bottom: 1rem;

		.header-content {
			flex-direction: column;
			text-align: center;
			gap: 1rem;
		}

		.header-text .page-title {
			font-size: 2rem;
		}

		.header-actions {
			width: 100%;
			flex-direction: column;
		}
	}

	.strata-table-modern {
		padding: 1rem;
		margin: 10px 0;
	}

	.btn-primary {
		width: 100%;
		justify-content: center;
	}
}

@media (max-width: 480px) {
	.page-header .header-text .page-title {
		font-size: 1.75rem;
	}

	.loading-card {
		padding: 2rem 1rem;
	}
}
</style>
