# 📊 Análise do Desenvolvimento dos Bilros - Análise de Dados

## 📍 Localização
- **Vue**: `resources/js/pages/DataAnalysis/SafraResults/SafraResults.vue`
- **JavaScript**: `resources/js/pages/DataAnalysis/SafraResults/SafraResults.js`

## 🎯 Propósito
Tela de visualização da evolução dos bilros ao longo da safra utilizando gráfico de barras empilhadas, permitindo acompanhar o desenvolvimento dos frutos desde a formação até a colheita em diferentes estágios de maturação.

## 🔗 Rota
- **Frontend**: `/panel/safra-results`
- **Permissão**: Administradores e Pré-cadastrados

## 📊 Funcionalidades

### 1. **Gráfico de Barras Empilhadas**
- Visualização dos estágios de desenvolvimento dos bilros
- Barras empilhadas por período de coleta
- Cores diferenciadas por estágio de maturação
- Tooltips informativos com detalhes

### 2. **Controles de Filtro**
- **Propriedade**: Seleção da propriedade
- **Área Homogênea**: Escolha da área específica
- **Período da Safra**: Filtro temporal
- **Tipo de Visualização**: Absoluto ou percentual

### 3. **Estágios de Desenvolvimento**
- **Bilros Jovens**: Frutos em desenvolvimento inicial
- **Bilros em Maturação**: Frutos em processo de amadurecimento
- **Bilros Maduros**: Frutos prontos para colheita
- **Bilros Passados**: Frutos que passaram do ponto ideal

## 🎨 Interface

### Layout Principal
```html
<div class="safra-results-wrapper">
    <div class="admin-content">
        <div class="admin-header">
            <h1>Análise do Desenvolvimento dos Bilros</h1>
            <p>Acompanhe a evolução dos bilros ao longo da safra.</p>
        </div>
        
        <div class="controls-section">
            <!-- Controles de filtro -->
        </div>
        
        <div class="chart-wrapper">
            <!-- Gráfico de barras empilhadas -->
        </div>
        
        <div class="development-analysis">
            <!-- Análise do desenvolvimento -->
        </div>
    </div>
</div>
```

### Controles de Filtro
```html
<form class="controls-form">
    <div class="controls-row">
        <div class="form-group">
            <label for="propriedade">Propriedade</label>
            <select id="propriedade" v-model="selectedOptions.propriedade">
                <option v-for="property in properties" :key="property.id" :value="property.id">
                    {{ property.name }}
                </option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="area">Área Homogênea</label>
            <select id="area" v-model="selectedOptions.area">
                <option v-for="area in homogeneousAreas" :key="area.id" :value="area.id">
                    {{ area.label }}
                </option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="safra">Safra</label>
            <select id="safra" v-model="selectedOptions.safra">
                <option v-for="safra in safras" :key="safra" :value="safra">
                    {{ safra }}
                </option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="viewType">Visualização</label>
            <select id="viewType" v-model="viewType">
                <option value="absolute">Valores Absolutos</option>
                <option value="percentage">Percentual</option>
            </select>
        </div>
    </div>
</form>
```

## 📊 Configuração do Gráfico

### Estrutura de Dados
```javascript
chartData: {
    labels: ['Jan/2024', 'Fev/2024', 'Mar/2024', 'Abr/2024', 'Mai/2024'],
    datasets: [
        {
            label: 'Bilros Jovens',
            backgroundColor: '#81C784', // Verde claro
            borderColor: '#4CAF50',
            borderWidth: 1,
            data: [45, 52, 38, 41, 35]
        },
        {
            label: 'Bilros em Maturação',
            backgroundColor: '#FFB74D', // Laranja claro
            borderColor: '#FF9800',
            borderWidth: 1,
            data: [32, 28, 45, 38, 42]
        },
        {
            label: 'Bilros Maduros',
            backgroundColor: '#B89C30', // Dourado
            borderColor: '#F57F17',
            borderWidth: 1,
            data: [28, 35, 42, 48, 52]
        },
        {
            label: 'Bilros Passados',
            backgroundColor: '#A1887F', // Marrom claro
            borderColor: '#795548',
            borderWidth: 1,
            data: [8, 12, 15, 18, 22]
        }
    ]
}
```

### Opções do Gráfico
```javascript
chartOptions: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: 'top',
            labels: {
                usePointStyle: true,
                padding: 20,
                font: {
                    family: 'Lexend',
                    size: 14,
                    weight: 'bold'
                }
            }
        },
        tooltip: {
            mode: 'index',
            intersect: false,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            callbacks: {
                title: function(tooltipItems) {
                    return `Período: ${tooltipItems[0].label}`;
                },
                label: function(context) {
                    const value = context.parsed.y;
                    const total = this.getTotalForPeriod(context.dataIndex);
                    const percentage = ((value / total) * 100).toFixed(1);
                    return `${context.dataset.label}: ${value} (${percentage}%)`;
                },
                footer: function(tooltipItems) {
                    const total = this.getTotalForPeriod(tooltipItems[0].dataIndex);
                    return `Total: ${total} bilros`;
                }
            }
        }
    },
    scales: {
        x: {
            stacked: true,
            title: {
                display: true,
                text: 'Período de Coleta',
                font: {
                    family: 'Lexend',
                    size: 14,
                    weight: 'bold'
                },
                color: '#b89c30'
            }
        },
        y: {
            stacked: true,
            title: {
                display: true,
                text: this.viewType === 'percentage' ? 'Percentual (%)' : 'Quantidade de Bilros',
                font: {
                    family: 'Lexend',
                    size: 14,
                    weight: 'bold'
                },
                color: '#b89c30'
            },
            beginAtZero: true,
            max: this.viewType === 'percentage' ? 100 : undefined,
            ticks: {
                callback: function(value) {
                    return this.viewType === 'percentage' ? value + '%' : value;
                }
            }
        }
    }
}
```

## 🔄 Fluxo de Funcionamento

### 1. Carregamento Inicial
```javascript
created() {
    this.authToken = window.token;
    this.getProperties();
    this.getSafras();
}

methods: {
    async getProperties() {
        try {
            const response = await axios.get("/api/v1/properties", {
                headers: { authorization: `bearer ${this.authToken}` }
            });
            
            this.properties = response.data.data;
            if (this.properties.length > 0) {
                this.selectedOptions.propriedade = this.properties[0].id;
                this.getHomogeneousAreas();
            }
        } catch (error) {
            this.handleError(error);
        }
    }
}
```

### 2. Busca de Dados de Desenvolvimento
```javascript
async getDevelopmentData() {
    if (!this.selectedOptions.propriedade || 
        !this.selectedOptions.area || 
        !this.selectedOptions.safra) return;
    
    this.loading = true;
    
    try {
        const response = await axios.post("/api/v1/get-bilros-development-data", {
            propertyId: this.selectedOptions.propriedade,
            areaHomogeneaId: this.selectedOptions.area,
            safra: this.selectedOptions.safra
        }, {
            headers: { authorization: `bearer ${this.authToken}` }
        });
        
        this.developmentData = response.data;
        this.updateChart();
        this.calculateDevelopmentMetrics();
    } catch (error) {
        this.handleError(error);
    } finally {
        this.loading = false;
    }
}
```

### 3. Processamento dos Dados
```javascript
updateChart() {
    if (!this.developmentData || Object.keys(this.developmentData).length === 0) return;
    
    const periods = Object.keys(this.developmentData).sort();
    const datasets = this.createDevelopmentDatasets(periods);
    
    this.chartData = {
        labels: periods,
        datasets: datasets
    };
    
    this.chartKey++;
}

createDevelopmentDatasets(periods) {
    const stages = [
        { key: 'young', label: 'Bilros Jovens', color: '#81C784', border: '#4CAF50' },
        { key: 'maturing', label: 'Bilros em Maturação', color: '#FFB74D', border: '#FF9800' },
        { key: 'mature', label: 'Bilros Maduros', color: '#B89C30', border: '#F57F17' },
        { key: 'overripe', label: 'Bilros Passados', color: '#A1887F', border: '#795548' }
    ];
    
    return stages.map(stage => ({
        label: stage.label,
        backgroundColor: stage.color,
        borderColor: stage.border,
        borderWidth: 1,
        data: periods.map(period => this.getValueForStageAndPeriod(stage.key, period))
    }));
}
```

## 📡 API Calls

### Endpoint Principal
```javascript
POST /api/v1/get-bilros-development-data
```

### Parâmetros de Requisição
```javascript
{
    propertyId: number,
    areaHomogeneaId: number,
    safra: string // "2023/2024"
}
```

### Estrutura de Resposta
```javascript
{
    "2024-01-15": {
        young_bilros: 45,
        maturing_bilros: 32,
        mature_bilros: 28,
        overripe_bilros: 8,
        total_bilros: 113,
        collection_date: "2024-01-15"
    },
    "2024-02-15": {
        young_bilros: 52,
        maturing_bilros: 28,
        mature_bilros: 35,
        overripe_bilros: 12,
        total_bilros: 127,
        collection_date: "2024-02-15"
    }
}
```

## 🎨 Estilização

### CSS Principal
```scss
.safra-results-wrapper {
    background-color: #f5f8fd;
    border-radius: 20px;
    
    .admin-content {
        height: 90vh;
        overflow-y: auto;
    }
    
    h1 {
        color: #b89c30;
        font-family: "Lexend", sans-serif;
        font-weight: 600;
        font-size: 24px;
    }
}

.controls-section {
    background: white;
    padding: 25px;
    margin: 20px;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    
    .controls-row {
        display: flex;
        gap: 25px;
        align-items: end;
        justify-content: center;
    }
}

.chart-wrapper {
    margin: 1rem 3rem;
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    height: 600px;
}

.development-analysis {
    margin: 1rem 3rem;
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
```

## 📊 Análise de Desenvolvimento

### Métricas Calculadas
```javascript
computed: {
    totalBilros() {
        return Object.values(this.developmentData)
            .reduce((sum, period) => sum + period.total_bilros, 0);
    },
    
    averageMaturityRate() {
        const periods = Object.values(this.developmentData);
        const totalMature = periods.reduce((sum, period) => sum + period.mature_bilros, 0);
        const totalBilros = periods.reduce((sum, period) => sum + period.total_bilros, 0);
        
        return totalBilros > 0 ? ((totalMature / totalBilros) * 100).toFixed(1) : 0;
    },
    
    developmentTrend() {
        const periods = Object.keys(this.developmentData).sort();
        if (periods.length < 2) return 'stable';
        
        const firstPeriod = this.developmentData[periods[0]];
        const lastPeriod = this.developmentData[periods[periods.length - 1]];
        
        const firstMaturityRate = (firstPeriod.mature_bilros / firstPeriod.total_bilros) * 100;
        const lastMaturityRate = (lastPeriod.mature_bilros / lastPeriod.total_bilros) * 100;
        
        const difference = lastMaturityRate - firstMaturityRate;
        
        if (difference > 5) return 'improving';
        if (difference < -5) return 'declining';
        return 'stable';
    },
    
    peakMaturityPeriod() {
        let maxMaturityRate = 0;
        let peakPeriod = '';
        
        Object.entries(this.developmentData).forEach(([period, data]) => {
            const maturityRate = (data.mature_bilros / data.total_bilros) * 100;
            if (maturityRate > maxMaturityRate) {
                maxMaturityRate = maturityRate;
                peakPeriod = period;
            }
        });
        
        return { period: peakPeriod, rate: maxMaturityRate.toFixed(1) };
    }
}
```

### Interface de Análise
```html
<div class="development-analysis">
    <h3>Análise do Desenvolvimento</h3>
    
    <div class="analysis-grid">
        <div class="analysis-card">
            <div class="card-header">
                <i class="fas fa-seedling"></i>
                <h4>Total de Bilros</h4>
            </div>
            <div class="card-value">{{ totalBilros.toLocaleString() }}</div>
            <div class="card-subtitle">Durante toda a safra</div>
        </div>
        
        <div class="analysis-card">
            <div class="card-header">
                <i class="fas fa-percentage"></i>
                <h4>Taxa de Maturação Média</h4>
            </div>
            <div class="card-value">{{ averageMaturityRate }}%</div>
            <div class="card-subtitle">Bilros maduros vs total</div>
        </div>
        
        <div class="analysis-card" :class="getTrendClass(developmentTrend)">
            <div class="card-header">
                <i class="fas" :class="getTrendIcon(developmentTrend)"></i>
                <h4>Tendência de Desenvolvimento</h4>
            </div>
            <div class="card-value">{{ getTrendLabel(developmentTrend) }}</div>
            <div class="card-subtitle">Evolução ao longo da safra</div>
        </div>
        
        <div class="analysis-card highlight">
            <div class="card-header">
                <i class="fas fa-trophy"></i>
                <h4>Pico de Maturação</h4>
            </div>
            <div class="card-value">{{ peakMaturityPeriod.period }}</div>
            <div class="card-subtitle">{{ peakMaturityPeriod.rate }}% de bilros maduros</div>
        </div>
    </div>
    
    <div class="development-insights">
        <h4>Insights do Desenvolvimento</h4>
        <ul>
            <li v-for="insight in developmentInsights" :key="insight.id">
                <i :class="insight.icon" :style="{ color: insight.color }"></i>
                {{ insight.text }}
            </li>
        </ul>
    </div>
</div>
```

## ✅ Validações

### Validação de Dados
```javascript
validateDevelopmentData(data) {
    if (!data || Object.keys(data).length === 0) {
        throw new Error('Nenhum dado de desenvolvimento encontrado para a safra selecionada');
    }
    
    // Verificar se todos os períodos têm dados válidos
    const invalidPeriods = Object.entries(data).filter(([period, periodData]) => {
        return !periodData.total_bilros || 
               periodData.total_bilros <= 0 ||
               (periodData.young_bilros + periodData.maturing_bilros + 
                periodData.mature_bilros + periodData.overripe_bilros) !== periodData.total_bilros;
    });
    
    if (invalidPeriods.length > 0) {
        console.warn('Períodos com dados inválidos:', invalidPeriods);
    }
    
    return data;
}
```

## 🎯 Casos de Uso

### 1. Monitoramento do Ciclo de Desenvolvimento
1. Selecionar propriedade, área e safra
2. Visualizar evolução dos estágios
3. Identificar padrões de maturação
4. Planejar colheitas otimizadas

### 2. Análise de Eficiência da Safra
1. Comparar taxas de maturação
2. Identificar períodos de pico
3. Avaliar perdas por bilros passados
4. Otimizar timing de colheita

### 3. Planejamento de Recursos
1. Prever demanda de mão de obra
2. Planejar logística de colheita
3. Otimizar uso de equipamentos
4. Estabelecer cronogramas

---

**Funcionalidade**: Visualização do desenvolvimento de bilros por estágios  
**Tipo**: Análise de desenvolvimento  
**Cor**: Dourado (#b89c30)
