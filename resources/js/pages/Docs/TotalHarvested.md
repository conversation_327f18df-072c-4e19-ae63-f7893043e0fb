# 📈 Frutos Colhidos por Área Homogênea - Análise de Dados

## 📍 Localização
- **Vue**: `resources/js/pages/DataAnalysis/TotalHarvested/TotalHarvested.vue`
- **JavaScript**: `resources/js/pages/DataAnalysis/TotalHarvested/TotalHarvested.js`

## 🎯 Propósito
Tela de visualização da quantidade de frutos colhidos por área homogênea ao longo do tempo, utilizando gráfico de linhas para acompanhar tendências de produção e comparar performance entre diferentes áreas.

## 🔗 Rota
- **Frontend**: `/panel/total-harvested`
- **Permissão**: Administradores e Pré-cadastrados

## 📊 Funcionalidades

### 1. **Gráfico de Linhas Temporal**
- Visualização da evolução da colheita ao longo do tempo
- Múltiplas linhas para diferentes áreas homogêneas
- Pontos de dados interativos
- Zoom e navegação temporal

### 2. **Controles de Filtro**
- **Propriedade**: Se<PERSON><PERSON> da propriedade
- **Período da Safra**: Filtro por período específico
- **Tipo de Visualização**: Dados absolutos ou relativos

### 3. **Comparação entre Áreas**
- Múltiplas séries de dados simultâneas
- Cores diferenciadas por área
- Legendas identificativas
- Tooltips informativos

## 🎨 Interface

### Layout Principal
```html
<div class="total-harvested-wrapper">
    <div class="admin-content">
        <div class="admin-header">
            <h1>Frutos Colhidos por Área Homogênea</h1>
            <p>Acompanhe a quantidade de frutos colhidos em cada Área Homogênea por coleta.</p>
        </div>
        
        <div class="graph-control">
            <!-- Controles de filtro -->
        </div>
        
        <div class="chart-wrapper">
            <!-- Gráfico de linhas -->
        </div>
        
        <div class="statistics-panel">
            <!-- Estatísticas resumidas -->
        </div>
    </div>
</div>
```

### Controles de Filtro
```html
<form class="graph-form">
    <div class="row top-row">
        <div class="form-group">
            <label for="propriedade">Propriedade</label>
            <select id="propriedade" v-model="selectedOptions.propriedade">
                <option v-for="property in properties" :key="property.id" :value="property.id">
                    {{ property.name }}
                </option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="safra">Safra</label>
            <select id="safra" v-model="selectedOptions.safra">
                <option v-for="period in datePeriods" :key="period" :value="period">
                    {{ period }}
                </option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="visualization">Visualização</label>
            <select id="visualization" v-model="visualizationType">
                <option value="absolute">Valores Absolutos</option>
                <option value="relative">Valores Relativos (%)</option>
            </select>
        </div>
    </div>
</form>
```

## 📊 Configuração do Gráfico

### Estrutura de Dados
```javascript
chartData: {
    labels: ['Jan/2024', 'Fev/2024', 'Mar/2024', 'Abr/2024', 'Mai/2024'],
    datasets: [
        {
            label: 'Área Homogênea A',
            borderColor: '#3d8160',
            backgroundColor: 'transparent',
            pointBackgroundColor: '#3d8160',
            pointBorderColor: 'white',
            pointRadius: 5,
            data: [120, 135, 148, 162, 175]
        },
        {
            label: 'Área Homogênea B',
            borderColor: '#e74c3c',
            backgroundColor: 'transparent',
            pointBackgroundColor: '#e74c3c',
            pointBorderColor: 'white',
            pointRadius: 5,
            data: [98, 112, 125, 138, 151]
        }
    ]
}
```

### Opções do Gráfico
```javascript
chartOptions: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: 'top',
            labels: {
                usePointStyle: true,
                padding: 20,
                font: {
                    family: 'Lexend',
                    size: 14
                }
            }
        },
        tooltip: {
            mode: 'index',
            intersect: false,
            callbacks: {
                title: function(tooltipItems) {
                    return `Coleta: ${tooltipItems[0].label}`;
                },
                label: function(context) {
                    return `${context.dataset.label}: ${context.parsed.y} frutos`;
                }
            }
        }
    },
    scales: {
        x: {
            display: true,
            title: {
                display: true,
                text: 'Período de Coleta',
                font: {
                    family: 'Lexend',
                    size: 14,
                    weight: 'bold'
                }
            }
        },
        y: {
            display: true,
            title: {
                display: true,
                text: 'Quantidade de Frutos',
                font: {
                    family: 'Lexend',
                    size: 14,
                    weight: 'bold'
                }
            },
            beginAtZero: true
        }
    },
    interaction: {
        mode: 'nearest',
        axis: 'x',
        intersect: false
    }
}
```

## 🔄 Fluxo de Funcionamento

### 1. Carregamento Inicial
```javascript
created() {
    this.authToken = window.token;
    this.getProperties();
}

methods: {
    async getProperties() {
        try {
            const response = await axios.get("/api/v1/properties", {
                headers: { authorization: `bearer ${this.authToken}` }
            });
            
            this.properties = response.data.data;
            if (this.properties.length > 0) {
                this.selectedOptions.propriedade = this.properties[0].id;
                this.getDatePeriods();
            }
        } catch (error) {
            this.handleError(error);
        }
    }
}
```

### 2. Busca de Dados por Período
```javascript
async getDataForPeriod() {
    if (!this.selectedOptions.propriedade || !this.selectedOptions.safra) return;
    
    this.loading = true;
    
    try {
        const response = await axios.post("/api/v1/get-collection-data-harvested", {
            propertyId: this.selectedOptions.propriedade,
            startDate: this.parseStartDate(this.selectedOptions.safra),
            endDate: this.parseEndDate(this.selectedOptions.safra)
        }, {
            headers: { authorization: `bearer ${this.authToken}` }
        });
        
        this.collectionData = response.data;
        this.updateChart();
    } catch (error) {
        this.handleError(error);
    } finally {
        this.loading = false;
    }
}
```

### 3. Processamento e Atualização do Gráfico
```javascript
updateChart() {
    if (!this.collectionData || Object.keys(this.collectionData).length === 0) return;
    
    const areas = this.getUniqueAreas();
    const dates = this.getSortedDates();
    const datasets = this.createDatasets(areas, dates);
    
    this.chartData = {
        labels: dates,
        datasets: datasets
    };
    
    this.chartKey++; // Force re-render
}

createDatasets(areas, dates) {
    const colors = ['#3d8160', '#e74c3c', '#3498db', '#f39c12', '#9b59b6', '#1abc9c'];
    
    return areas.map((area, index) => ({
        label: area.label,
        borderColor: colors[index % colors.length],
        backgroundColor: 'transparent',
        pointBackgroundColor: colors[index % colors.length],
        pointBorderColor: 'white',
        pointRadius: 5,
        data: dates.map(date => this.getValueForAreaAndDate(area.id, date))
    }));
}
```

## 📡 API Calls

### Endpoint Principal
```javascript
POST /api/v1/get-collection-data-harvested
```

### Parâmetros de Requisição
```javascript
{
    propertyId: number,
    startDate: string, // "Outubro/2023"
    endDate: string    // "Setembro/2024"
}
```

### Estrutura de Resposta
```javascript
{
    "2024-01-15": [
        {
            homogeneous_area_id: 1,
            homogeneous_area_label: "Área A",
            total_harvested: 125,
            collection_date: "2024-01-15"
        },
        {
            homogeneous_area_id: 2,
            homogeneous_area_label: "Área B",
            total_harvested: 98,
            collection_date: "2024-01-15"
        }
    ],
    "2024-02-15": [
        // ... dados da próxima coleta
    ]
}
```

## 🎨 Estilização

### CSS Principal
```scss
.total-harvested-wrapper {
    background-color: #f5f8fd;
    border-radius: 20px;
    
    .admin-content {
        height: 90vh;
        overflow-y: auto;
    }
    
    h1 {
        color: #3d8160;
        font-family: "Lexend", sans-serif;
        font-weight: 600;
        font-size: 24px;
    }
}

.graph-control {
    padding: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    
    .graph-form {
        display: flex;
        flex-direction: column;
        align-items: center;
        
        .top-row {
            display: flex;
            gap: 20px;
            margin-bottom: 10px;
        }
    }
}

.chart-wrapper {
    margin: 1rem 3rem;
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    height: 500px;
}

.statistics-panel {
    margin: 1rem 3rem;
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
```

### Responsividade
```scss
@media (max-width: 768px) {
    .top-row {
        flex-direction: column;
        gap: 15px;
    }
    
    .chart-wrapper {
        margin: 1rem;
        height: 400px;
    }
}
```

## 📊 Painel de Estatísticas

### Métricas Calculadas
```javascript
computed: {
    totalHarvested() {
        return Object.values(this.collectionData)
            .flat()
            .reduce((sum, item) => sum + item.total_harvested, 0);
    },
    
    averagePerArea() {
        const areas = this.getUniqueAreas();
        return areas.map(area => ({
            label: area.label,
            average: this.calculateAverageForArea(area.id),
            total: this.calculateTotalForArea(area.id)
        }));
    },
    
    bestPerformingArea() {
        return this.averagePerArea.reduce((best, current) => 
            current.total > best.total ? current : best
        );
    },
    
    growthTrend() {
        const dates = this.getSortedDates();
        if (dates.length < 2) return 0;
        
        const firstTotal = this.getTotalForDate(dates[0]);
        const lastTotal = this.getTotalForDate(dates[dates.length - 1]);
        
        return ((lastTotal - firstTotal) / firstTotal * 100).toFixed(1);
    }
}
```

### Interface das Estatísticas
```html
<div class="statistics-panel">
    <h3>Estatísticas do Período</h3>
    
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value">{{ totalHarvested }}</div>
            <div class="stat-label">Total de Frutos Colhidos</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value">{{ bestPerformingArea.label }}</div>
            <div class="stat-label">Área Mais Produtiva</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value">{{ growthTrend }}%</div>
            <div class="stat-label">Tendência de Crescimento</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-value">{{ averagePerArea.length }}</div>
            <div class="stat-label">Áreas Monitoradas</div>
        </div>
    </div>
</div>
```

## ✅ Validações

### Validação de Dados
```javascript
validateCollectionData(data) {
    if (!data || Object.keys(data).length === 0) {
        throw new Error('Nenhum dado de colheita encontrado para o período selecionado');
    }
    
    // Verificar se há pelo menos uma área com dados
    const hasValidData = Object.values(data).some(dateData => 
        Array.isArray(dateData) && dateData.length > 0
    );
    
    if (!hasValidData) {
        throw new Error('Dados de colheita inválidos ou incompletos');
    }
    
    return data;
}
```

### Tratamento de Erros
```javascript
handleError(error) {
    console.error('Erro ao carregar dados:', error);
    
    if (error.response?.status === 404) {
        this.showEmptyState('Nenhum dado encontrado para o período selecionado');
    } else if (error.message.includes('período selecionado')) {
        this.showEmptyState(error.message);
    } else {
        this.showError('Erro ao carregar dados de colheita');
    }
}
```

## 🎯 Casos de Uso

### 1. Comparação de Performance entre Áreas
1. Selecionar propriedade com múltiplas áreas
2. Escolher período de análise
3. Observar linhas do gráfico
4. Identificar áreas mais/menos produtivas

### 2. Análise de Tendências Temporais
1. Focar em uma área específica
2. Acompanhar evolução ao longo do tempo
3. Identificar padrões sazonais
4. Detectar anomalias ou quedas

### 3. Planejamento de Safra
1. Analisar dados históricos
2. Identificar períodos de maior produção
3. Planejar recursos e logística
4. Estabelecer metas realistas

## 🚨 Estados da Interface

### Loading
```html
<div v-if="loading" class="loading-state">
    <div class="chart-skeleton"></div>
    <p>Carregando dados de colheita...</p>
</div>
```

### Sem Dados
```html
<div v-if="!loading && !hasData" class="empty-state">
    <i class="fas fa-chart-line fa-3x"></i>
    <h3>Nenhum dado disponível</h3>
    <p>Não há dados de colheita para o período selecionado.</p>
</div>
```

### Com Dados
```html
<div class="chart-wrapper">
    <LineChart :key="chartKey" :chart-data="chartData" :chart-options="chartOptions" />
</div>
```

---

**Funcionalidade**: Visualização temporal de frutos colhidos por área  
**Tipo**: Análise de produção  
**Cor**: Verde escuro (#135d2a)
