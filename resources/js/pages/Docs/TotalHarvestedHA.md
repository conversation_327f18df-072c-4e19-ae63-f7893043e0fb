# 📈 Frutos Colhidos por Safra - Análise de Dados

## 📍 Localização
- **Vue**: `resources/js/pages/DataAnalysis/TotalHarvestedHA/TotalHarvestedHA.vue`
- **JavaScript**: `resources/js/pages/DataAnalysis/TotalHarvestedHA/TotalHarvestedHA.js`

## 🎯 Propósito
Tela de visualização da quantidade de frutos colhidos por safra em uma área homogênea específica, utilizando gráfico de linhas para acompanhar a evolução da produção ao longo de diferentes safras.

## 🔗 Rota
- **Frontend**: `/panel/total-harvested-ha`
- **Permissão**: Administradores e Pré-cadastrados

## 📊 Funcionalidades

### 1. **Gráfico de Linhas por Safra**
- Visualização da evolução da colheita entre safras
- Linha única para área homogênea selecionada
- Pontos de dados por período de safra
- Tendências de longo prazo

### 2. **Controles de Seleção**
- **Propriedade**: Seleção da propriedade
- **Área Homogênea**: Escolha da área específica
- **Período de Análise**: Filtro de safras

### 3. **Análise Comparativa**
- Comparação entre diferentes safras
- Identificação de padrões anuais
- Análise de crescimento/declínio
- Métricas de performance

## 🎨 Interface

### Layout Principal
```html
<div class="total-harvested-ha-wrapper">
    <div class="admin-content">
        <div class="admin-header">
            <h1>Frutos Colhidos por Safra</h1>
            <p>Acompanhe a quantidade de frutos colhidos em cada safra por Área Homogênea.</p>
        </div>
        
        <div class="graph-control">
            <!-- Controles de filtro -->
        </div>
        
        <div class="chart-wrapper">
            <!-- Gráfico de linhas -->
        </div>
        
        <div class="analysis-panel">
            <!-- Análises e métricas -->
        </div>
    </div>
</div>
```

### Controles de Filtro
```html
<form class="graph-form">
    <div class="row top-row">
        <div class="form-group">
            <label for="propriedade">Propriedade</label>
            <select id="propriedade" v-model="selectedOptions.propriedade">
                <option v-for="property in properties" :key="property.id" :value="property.id">
                    {{ property.name }}
                </option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="area">Área Homogênea</label>
            <select id="area" v-model="selectedOptions.area">
                <option v-for="area in homogeneousAreas" :key="area.id" :value="area.id">
                    {{ area.label }}
                </option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="years">Período</label>
            <select id="years" v-model="selectedOptions.years">
                <option value="all">Todas as Safras</option>
                <option value="last5">Últimas 5 Safras</option>
                <option value="last3">Últimas 3 Safras</option>
                <option value="current">Safra Atual</option>
            </select>
        </div>
    </div>
</form>
```

## 📊 Configuração do Gráfico

### Estrutura de Dados
```javascript
chartData: {
    labels: ['2020/2021', '2021/2022', '2022/2023', '2023/2024', '2024/2025'],
    datasets: [{
        label: 'Área Homogênea A',
        borderColor: '#135d2a',
        backgroundColor: 'rgba(19, 93, 42, 0.1)',
        pointBackgroundColor: '#135d2a',
        pointBorderColor: 'white',
        pointRadius: 6,
        pointHoverRadius: 8,
        fill: true,
        tension: 0.4,
        data: [1250, 1380, 1420, 1650, 1580]
    }]
}
```

### Opções do Gráfico
```javascript
chartOptions: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: 'top',
            labels: {
                usePointStyle: true,
                padding: 20,
                font: {
                    family: 'Lexend',
                    size: 16,
                    weight: 'bold'
                }
            }
        },
        tooltip: {
            mode: 'index',
            intersect: false,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: 'white',
            bodyColor: 'white',
            borderColor: '#135d2a',
            borderWidth: 2,
            callbacks: {
                title: function(tooltipItems) {
                    return `Safra: ${tooltipItems[0].label}`;
                },
                label: function(context) {
                    return `Total Colhido: ${context.parsed.y.toLocaleString()} frutos`;
                },
                afterLabel: function(context) {
                    const growth = this.calculateGrowthFromPrevious(context.dataIndex);
                    return growth ? `Crescimento: ${growth}%` : '';
                }
            }
        }
    },
    scales: {
        x: {
            display: true,
            title: {
                display: true,
                text: 'Safra (Período)',
                font: {
                    family: 'Lexend',
                    size: 16,
                    weight: 'bold'
                },
                color: '#135d2a'
            },
            grid: {
                color: 'rgba(19, 93, 42, 0.1)'
            }
        },
        y: {
            display: true,
            title: {
                display: true,
                text: 'Quantidade de Frutos Colhidos',
                font: {
                    family: 'Lexend',
                    size: 16,
                    weight: 'bold'
                },
                color: '#135d2a'
            },
            beginAtZero: true,
            grid: {
                color: 'rgba(19, 93, 42, 0.1)'
            },
            ticks: {
                callback: function(value) {
                    return value.toLocaleString();
                }
            }
        }
    },
    interaction: {
        mode: 'nearest',
        axis: 'x',
        intersect: false
    }
}
```

## 🔄 Fluxo de Funcionamento

### 1. Carregamento Inicial
```javascript
created() {
    this.authToken = window.token;
    this.getProperties();
}

methods: {
    async getProperties() {
        try {
            const response = await axios.get("/api/v1/properties", {
                headers: { authorization: `bearer ${this.authToken}` }
            });
            
            this.properties = response.data.data;
            if (this.properties.length > 0) {
                this.selectedOptions.propriedade = this.properties[0].id;
                this.getHomogeneousAreas();
            }
        } catch (error) {
            this.handleError(error);
        }
    }
}
```

### 2. Busca de Áreas Homogêneas
```javascript
async getHomogeneousAreas() {
    if (!this.selectedOptions.propriedade) return;
    
    try {
        const response = await axios.get(
            `/api/v1/homogeneous-area?propid=${this.selectedOptions.propriedade}`,
            {
                headers: { authorization: `Bearer ${this.authToken}` }
            }
        );
        
        this.homogeneousAreas = response.data.data;
        if (this.homogeneousAreas.length > 0) {
            this.selectedOptions.area = this.homogeneousAreas[0].id;
            this.getDataForArea();
        }
    } catch (error) {
        this.handleError(error);
    }
}
```

### 3. Busca de Dados por Safra
```javascript
async getDataForArea() {
    if (!this.selectedOptions.propriedade || !this.selectedOptions.area) return;
    
    this.loading = true;
    
    try {
        const response = await axios.post("/api/v1/get-collection-data-harvested-ha", {
            propertyId: this.selectedOptions.propriedade,
            areaHomogeneaId: this.selectedOptions.area,
            yearFilter: this.selectedOptions.years
        }, {
            headers: { authorization: `bearer ${this.authToken}` }
        });
        
        this.collectionData = response.data;
        this.updateChart();
        this.calculateMetrics();
    } catch (error) {
        this.handleError(error);
    } finally {
        this.loading = false;
    }
}
```

### 4. Processamento e Atualização do Gráfico
```javascript
updateChart() {
    if (!this.collectionData || this.collectionData.length === 0) return;
    
    const safras = this.collectionData.map(item => item.safra);
    const totals = this.collectionData.map(item => item.total_harvested);
    
    const areaLabel = this.homogeneousAreas.find(
        area => area.id == this.selectedOptions.area
    )?.label || 'Área Selecionada';
    
    this.chartData = {
        labels: safras,
        datasets: [{
            label: areaLabel,
            borderColor: '#135d2a',
            backgroundColor: 'rgba(19, 93, 42, 0.1)',
            pointBackgroundColor: '#135d2a',
            pointBorderColor: 'white',
            pointRadius: 6,
            pointHoverRadius: 8,
            fill: true,
            tension: 0.4,
            data: totals
        }]
    };
    
    this.chartKey++;
}
```

## 📡 API Calls

### Endpoint Principal
```javascript
POST /api/v1/get-collection-data-harvested-ha
```

### Parâmetros de Requisição
```javascript
{
    propertyId: number,
    areaHomogeneaId: number,
    yearFilter: string // "all", "last5", "last3", "current"
}
```

### Estrutura de Resposta
```javascript
[
    {
        safra: "2020/2021",
        total_harvested: 1250,
        collection_count: 12,
        average_per_collection: 104.2,
        start_date: "2020-10-01",
        end_date: "2021-09-30"
    },
    {
        safra: "2021/2022",
        total_harvested: 1380,
        collection_count: 14,
        average_per_collection: 98.6,
        start_date: "2021-10-01",
        end_date: "2022-09-30"
    }
]
```

## 🎨 Estilização

### CSS Principal
```scss
.total-harvested-ha-wrapper {
    background-color: #f5f8fd;
    border-radius: 20px;
    
    .admin-content {
        height: 90vh;
        overflow-y: auto;
    }
    
    h1 {
        color: #135d2a;
        font-family: "Lexend", sans-serif;
        font-weight: 600;
        font-size: 24px;
    }
}

.graph-control {
    padding: 20px;
    background: white;
    margin: 20px;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    
    .graph-form {
        display: flex;
        justify-content: center;
        
        .top-row {
            display: flex;
            gap: 25px;
            align-items: end;
        }
    }
}

.chart-wrapper {
    margin: 1rem 3rem;
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    height: 550px;
}

.analysis-panel {
    margin: 1rem 3rem;
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
```

## 📊 Painel de Análise

### Métricas Calculadas
```javascript
computed: {
    totalProduction() {
        return this.collectionData.reduce((sum, item) => sum + item.total_harvested, 0);
    },
    
    averagePerSafra() {
        return this.collectionData.length > 0 
            ? (this.totalProduction / this.collectionData.length).toFixed(0)
            : 0;
    },
    
    bestSafra() {
        return this.collectionData.reduce((best, current) => 
            current.total_harvested > best.total_harvested ? current : best
        );
    },
    
    worstSafra() {
        return this.collectionData.reduce((worst, current) => 
            current.total_harvested < worst.total_harvested ? current : worst
        );
    },
    
    overallGrowth() {
        if (this.collectionData.length < 2) return 0;
        
        const first = this.collectionData[0].total_harvested;
        const last = this.collectionData[this.collectionData.length - 1].total_harvested;
        
        return ((last - first) / first * 100).toFixed(1);
    },
    
    consistency() {
        if (this.collectionData.length < 2) return 100;
        
        const values = this.collectionData.map(item => item.total_harvested);
        const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
        const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
        const stdDev = Math.sqrt(variance);
        const cv = (stdDev / mean) * 100;
        
        return Math.max(0, 100 - cv).toFixed(1);
    }
}
```

### Interface do Painel
```html
<div class="analysis-panel">
    <h3>Análise de Performance da Área</h3>
    
    <div class="metrics-grid">
        <div class="metric-card highlight">
            <div class="metric-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="metric-content">
                <div class="metric-value">{{ totalProduction.toLocaleString() }}</div>
                <div class="metric-label">Total Produzido</div>
                <div class="metric-sublabel">{{ collectionData.length }} safras</div>
            </div>
        </div>
        
        <div class="metric-card">
            <div class="metric-icon">
                <i class="fas fa-calculator"></i>
            </div>
            <div class="metric-content">
                <div class="metric-value">{{ averagePerSafra }}</div>
                <div class="metric-label">Média por Safra</div>
                <div class="metric-sublabel">frutos colhidos</div>
            </div>
        </div>
        
        <div class="metric-card success">
            <div class="metric-icon">
                <i class="fas fa-trophy"></i>
            </div>
            <div class="metric-content">
                <div class="metric-value">{{ bestSafra.safra }}</div>
                <div class="metric-label">Melhor Safra</div>
                <div class="metric-sublabel">{{ bestSafra.total_harvested.toLocaleString() }} frutos</div>
            </div>
        </div>
        
        <div class="metric-card warning">
            <div class="metric-icon">
                <i class="fas fa-chart-line-down"></i>
            </div>
            <div class="metric-content">
                <div class="metric-value">{{ worstSafra.safra }}</div>
                <div class="metric-label">Menor Safra</div>
                <div class="metric-sublabel">{{ worstSafra.total_harvested.toLocaleString() }} frutos</div>
            </div>
        </div>
        
        <div class="metric-card" :class="{ success: overallGrowth > 0, warning: overallGrowth < 0 }">
            <div class="metric-icon">
                <i class="fas" :class="overallGrowth >= 0 ? 'fa-trending-up' : 'fa-trending-down'"></i>
            </div>
            <div class="metric-content">
                <div class="metric-value">{{ overallGrowth }}%</div>
                <div class="metric-label">Crescimento Geral</div>
                <div class="metric-sublabel">primeira vs última safra</div>
            </div>
        </div>
        
        <div class="metric-card">
            <div class="metric-icon">
                <i class="fas fa-balance-scale"></i>
            </div>
            <div class="metric-content">
                <div class="metric-value">{{ consistency }}%</div>
                <div class="metric-label">Consistência</div>
                <div class="metric-sublabel">estabilidade da produção</div>
            </div>
        </div>
    </div>
</div>
```

## ✅ Validações

### Validação de Dados
```javascript
validateCollectionData(data) {
    if (!Array.isArray(data) || data.length === 0) {
        throw new Error('Nenhum dado de safra encontrado para a área selecionada');
    }
    
    // Verificar se todos os itens têm dados válidos
    const invalidItems = data.filter(item => 
        !item.safra || 
        typeof item.total_harvested !== 'number' || 
        item.total_harvested < 0
    );
    
    if (invalidItems.length > 0) {
        console.warn('Dados inválidos encontrados:', invalidItems);
    }
    
    return data.filter(item => 
        item.safra && 
        typeof item.total_harvested === 'number' && 
        item.total_harvested >= 0
    );
}
```

## 🎯 Casos de Uso

### 1. Análise de Evolução da Área
1. Selecionar propriedade e área específica
2. Visualizar todas as safras disponíveis
3. Identificar tendências de crescimento/declínio
4. Avaliar consistência da produção

### 2. Comparação de Performance
1. Alternar entre diferentes áreas
2. Comparar métricas de performance
3. Identificar áreas mais/menos produtivas
4. Planejar investimentos e melhorias

### 3. Planejamento Estratégico
1. Analisar dados históricos
2. Identificar padrões e ciclos
3. Estabelecer metas realistas
4. Avaliar ROI de investimentos

---

**Funcionalidade**: Visualização de frutos colhidos por safra em área específica  
**Tipo**: Análise de produção  
**Cor**: Verde escuro (#135d2a)
