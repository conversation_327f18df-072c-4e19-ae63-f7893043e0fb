# 🥧 Resumo da Coleta - Análise de Dados

## 📍 Localização
- **Vue**: `resources/js/pages/DataAnalysis/DataPercentage/DataPercentage.vue`
- **JavaScript**: `resources/js/pages/DataAnalysis/DataPercentage/DataPercentage.js`

## 🎯 Propósito
Tela de visualização dos dados de uma coleta específica em formato de gráfico de pizza, permitindo análise percentual da distribuição de frutos por categoria e estado.

## 🔗 Rota
- **Frontend**: `/panel/data-percentage`
- **Permissão**: Administradores e Pré-cadastrados

## 📊 Funcionalidades

### 1. **Gráfico de Pizza Interativo**
- Visualização percentual dos dados
- Segmentos coloridos por categoria
- Tooltips informativos
- Legendas explicativas

### 2. **Filtros de Seleção**
- **Propriedade**: Escolha da propriedade
- **<PERSON><PERSON>**: Seleção da área específica
- **Data da Coleta**: Filtro por data específica

### 3. **Categorias de Dados**
- **Frutos Saudáveis**: Frutos em bom estado
- **Frutos com Doenças**: Vassoura de bruxa, podridão parda
- **Frutos com Pragas**: Ataques de ratos, insetos
- **Perdas**: Causas desconhecidas, outros fatores

## 🎨 Interface

### Layout Principal
```html
<div class="data-percentage-wrapper">
    <div class="admin-content">
        <div class="admin-header">
            <h1>Resumo da Coleta</h1>
            <p>Visualize o resumo dos dados de uma coleta.</p>
        </div>
        
        <div class="controls-section">
            <!-- Controles de filtro -->
        </div>
        
        <div class="chart-container">
            <!-- Gráfico de pizza -->
        </div>
        
        <div class="summary-cards">
            <!-- Cards com totais -->
        </div>
    </div>
</div>
```

### Controles de Filtro
```html
<div class="controls-row">
    <div class="form-group">
        <label>Propriedade</label>
        <select v-model="selectedProperty">
            <option v-for="property in properties" :value="property.id">
                {{ property.name }}
            </option>
        </select>
    </div>
    
    <div class="form-group">
        <label>Área Homogênea</label>
        <select v-model="selectedArea">
            <option v-for="area in homogeneousAreas" :value="area.id">
                {{ area.label }}
            </option>
        </select>
    </div>
    
    <div class="form-group">
        <label>Data da Coleta</label>
        <select v-model="selectedDate">
            <option v-for="date in collectionDates" :value="date">
                {{ formatDate(date) }}
            </option>
        </select>
    </div>
</div>
```

## 📊 Configuração do Gráfico

### Estrutura de Dados
```javascript
chartData: {
    labels: [
        'Frutos Saudáveis',
        'Vassoura de Bruxa',
        'Podridão Parda',
        'Ataques de Ratos',
        'Perdas Desconhecidas',
        'Outros'
    ],
    datasets: [{
        data: [45, 20, 15, 10, 7, 3],
        backgroundColor: [
            '#4CAF50', // Verde - Saudáveis
            '#8B4513', // Marrom - Vassoura de Bruxa
            '#9C27B0', // Roxo - Podridão Parda
            '#F44336', // Vermelho - Ratos
            '#757575', // Cinza - Perdas Desconhecidas
            '#FF9800'  // Laranja - Outros
        ],
        borderWidth: 2,
        borderColor: '#fff'
    }]
}
```

### Opções do Gráfico
```javascript
chartOptions: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: 'bottom',
            labels: {
                padding: 20,
                usePointStyle: true,
                font: {
                    family: 'Lexend',
                    size: 14
                }
            }
        },
        tooltip: {
            callbacks: {
                label: function(context) {
                    const label = context.label || '';
                    const value = context.parsed;
                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                    const percentage = ((value / total) * 100).toFixed(1);
                    return `${label}: ${value} (${percentage}%)`;
                }
            }
        }
    }
}
```

## 🔄 Fluxo de Funcionamento

### 1. Carregamento Inicial
```javascript
created() {
    this.loadProperties();
    this.loadInitialData();
}

methods: {
    async loadInitialData() {
        if (this.properties.length > 0) {
            this.selectedProperty = this.properties[0].id;
            await this.loadHomogeneousAreas();
            await this.loadCollectionDates();
            await this.loadCollectionData();
        }
    }
}
```

### 2. Atualização por Filtros
```javascript
watch: {
    selectedProperty() {
        this.loadHomogeneousAreas();
        this.resetAreaAndDate();
    },
    
    selectedArea() {
        this.loadCollectionDates();
        this.resetDate();
    },
    
    selectedDate() {
        this.loadCollectionData();
    }
}
```

### 3. Processamento de Dados
```javascript
methods: {
    processCollectionData(rawData) {
        const processed = {
            healthy: rawData.healthy_fruits || 0,
            witchBroom: rawData.witch_broom || 0,
            brownRot: rawData.brown_rot || 0,
            ratAttacks: rawData.rat_attacks || 0,
            unknownLoss: rawData.unknown_loss || 0,
            others: rawData.others || 0
        };
        
        this.updateChart(processed);
        this.updateSummaryCards(processed);
    }
}
```

## 📡 API Calls

### Endpoints Utilizados
```javascript
// Buscar dados da coleta
GET /api/v1/collection-data-percentage

// Buscar datas de coleta
GET /api/v1/collection-dates

// Buscar áreas homogêneas
GET /api/v1/homogeneous-areas
```

### Parâmetros de Requisição
```javascript
{
    property_id: number,
    homogeneous_area_id: number,
    collection_date: string // YYYY-MM-DD
}
```

### Estrutura de Resposta
```javascript
{
    data: {
        collection_id: 123,
        collection_date: "2024-01-15",
        property_name: "Fazenda São João",
        area_label: "Área A",
        totals: {
            healthy_fruits: 450,
            witch_broom: 200,
            brown_rot: 150,
            rat_attacks: 100,
            unknown_loss: 70,
            others: 30
        },
        percentages: {
            healthy_fruits: 45.0,
            witch_broom: 20.0,
            brown_rot: 15.0,
            rat_attacks: 10.0,
            unknown_loss: 7.0,
            others: 3.0
        }
    }
}
```

## 🎨 Estilização

### CSS Principal
```scss
.data-percentage-wrapper {
    background-color: #f5f8fd;
    border-radius: 20px;
    
    .admin-content {
        height: 90vh;
        overflow-y: auto;
    }
}

.controls-section {
    background: white;
    padding: 20px;
    margin: 20px;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    
    .controls-row {
        display: flex;
        gap: 20px;
        align-items: end;
    }
}

.chart-container {
    background: white;
    margin: 20px;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    height: 500px;
    
    canvas {
        max-height: 400px;
    }
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px;
}
```

### Cards de Resumo
```scss
.summary-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
    
    .card-icon {
        font-size: 2rem;
        margin-bottom: 10px;
    }
    
    .card-value {
        font-size: 1.5rem;
        font-weight: bold;
        color: #3d8160;
    }
    
    .card-label {
        font-size: 0.9rem;
        color: #666;
        margin-top: 5px;
    }
    
    .card-percentage {
        font-size: 0.8rem;
        color: #999;
    }
}
```

## 📊 Cards de Resumo

### Estrutura dos Cards
```html
<div class="summary-cards">
    <div class="summary-card" v-for="category in summaryData" :key="category.key">
        <div class="card-icon" :style="{ color: category.color }">
            <i :class="category.icon"></i>
        </div>
        <div class="card-value">{{ category.value }}</div>
        <div class="card-label">{{ category.label }}</div>
        <div class="card-percentage">{{ category.percentage }}%</div>
    </div>
</div>
```

### Dados dos Cards
```javascript
computed: {
    summaryData() {
        return [
            {
                key: 'healthy',
                label: 'Frutos Saudáveis',
                value: this.data.healthy,
                percentage: this.percentages.healthy,
                color: '#4CAF50',
                icon: 'fas fa-check-circle'
            },
            {
                key: 'witchBroom',
                label: 'Vassoura de Bruxa',
                value: this.data.witchBroom,
                percentage: this.percentages.witchBroom,
                color: '#8B4513',
                icon: 'fas fa-tree'
            },
            {
                key: 'brownRot',
                label: 'Podridão Parda',
                value: this.data.brownRot,
                percentage: this.percentages.brownRot,
                color: '#9C27B0',
                icon: 'fas fa-virus'
            },
            {
                key: 'ratAttacks',
                label: 'Ataques de Ratos',
                value: this.data.ratAttacks,
                percentage: this.percentages.ratAttacks,
                color: '#F44336',
                icon: 'fas fa-paw'
            }
        ];
    }
}
```

## ✅ Validações

### Validação de Dados
```javascript
validateCollectionData(data) {
    const total = Object.values(data.totals).reduce((sum, val) => sum + val, 0);
    
    if (total === 0) {
        throw new Error('Nenhum dado encontrado para a coleta selecionada');
    }
    
    return data;
}
```

### Estados de Erro
```javascript
handleError(error) {
    if (error.response?.status === 404) {
        this.showEmptyState('Nenhum dado encontrado para a coleta selecionada');
    } else {
        this.showError('Erro ao carregar dados da coleta');
    }
}
```

## 🎯 Casos de Uso

### 1. Análise de Qualidade da Coleta
1. Selecionar propriedade e área específica
2. Escolher data da coleta
3. Analisar distribuição percentual
4. Identificar problemas predominantes

### 2. Comparação entre Coletas
1. Alternar entre diferentes datas
2. Observar variações percentuais
3. Identificar tendências temporais

### 3. Monitoramento de Saúde dos Frutos
1. Focar no percentual de frutos saudáveis
2. Acompanhar evolução de doenças
3. Avaliar eficácia de tratamentos

## 🚨 Estados da Interface

### Loading
```html
<div v-if="loading" class="loading-state">
    <div class="chart-skeleton"></div>
    <p>Carregando dados da coleta...</p>
</div>
```

### Sem Dados
```html
<div v-if="!loading && !hasData" class="empty-state">
    <i class="fas fa-chart-pie fa-3x"></i>
    <h3>Nenhum dado disponível</h3>
    <p>Não há dados para a coleta selecionada.</p>
</div>
```

### Com Dados
```html
<div class="chart-container">
    <PieChart :chart-data="chartData" :chart-options="chartOptions" />
</div>
```

## 🔧 Componentes Utilizados

### Chart.js
- **PieChart**: Gráfico de pizza principal
- **Tooltips**: Informações detalhadas
- **Legendas**: Identificação das categorias
- **Animações**: Transições suaves

### Filtros Interativos
- **Dropdowns**: Seleção hierárquica
- **Atualização automática**: Dados em tempo real
- **Validação**: Verificação de seleções válidas

---

**Funcionalidade**: Visualização percentual de dados de coleta  
**Tipo**: Análise de resumo  
**Cor**: Verde claro (#66bb6a)
