# 📊 Quantidade de Frutos por Faixa - Análise de Dados

## 📍 Localização
- **Vue**: `resources/js/pages/DataAnalysis/TotalCocoa/TotalCocoa.vue`
- **JavaScript**: `resources/js/pages/DataAnalysis/TotalCocoa/TotalCocoa.js`

## 🎯 Propósito
Tela de visualização da distribuição dos frutos por faixa etária das árvores utilizando gráfico de barras agrupadas, permitindo análise da produtividade em diferentes estágios de desenvolvimento das plantas de cacau.

## 🔗 Rota
- **Frontend**: `/panel/total-cocoa`
- **Permissão**: Administradores e Pré-cadastrados

## 📊 Funcionalidades

### 1. **Gráfico de Barras Agrupadas**
- Visualização da produção por faixa etária
- Barras agrupadas por período de coleta
- Cores diferenciadas por faixa etária
- Comparação visual entre faixas

### 2. **Controles de Filtro**
- **Propriedade**: Seleção da propriedade
- **<PERSON><PERSON>**: Escolha da área específica
- **Período**: Filtro temporal
- **Tipo de Análise**: Produção ou produtividade

### 3. **Faixas Etárias das Árvores**
- **Jovens (0-3 anos)**: Árvores em desenvolvimento inicial
- **Adultas Jovens (4-7 anos)**: Árvores em produção inicial
- **Adultas (8-15 anos)**: Árvores em plena produção
- **Maduras (16+ anos)**: Árvores em produção estabilizada

## 🎨 Interface

### Layout Principal
```html
<div class="total-cocoa-wrapper">
    <div class="admin-content">
        <div class="admin-header">
            <h1>Quantidade de Frutos por Faixa</h1>
            <p>Visualize a distribuição dos frutos por faixa etária das árvores.</p>
        </div>
        
        <div class="controls-section">
            <!-- Controles de filtro -->
        </div>
        
        <div class="chart-wrapper">
            <!-- Gráfico de barras agrupadas -->
        </div>
        
        <div class="productivity-analysis">
            <!-- Análise de produtividade -->
        </div>
    </div>
</div>
```

### Controles de Filtro
```html
<form class="controls-form">
    <div class="controls-row">
        <div class="form-group">
            <label for="propriedade">Propriedade</label>
            <select id="propriedade" v-model="selectedOptions.propriedade">
                <option v-for="property in properties" :key="property.id" :value="property.id">
                    {{ property.name }}
                </option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="area">Área Homogênea</label>
            <select id="area" v-model="selectedOptions.area">
                <option v-for="area in homogeneousAreas" :key="area.id" :value="area.id">
                    {{ area.label }}
                </option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="periodo">Período</label>
            <select id="periodo" v-model="selectedOptions.periodo">
                <option v-for="period in periods" :key="period" :value="period">
                    {{ period }}
                </option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="analysisType">Tipo de Análise</label>
            <select id="analysisType" v-model="analysisType">
                <option value="production">Produção Total</option>
                <option value="productivity">Produtividade por Árvore</option>
                <option value="efficiency">Eficiência por Faixa</option>
            </select>
        </div>
    </div>
</form>
```

## 📊 Configuração do Gráfico

### Estrutura de Dados
```javascript
chartData: {
    labels: ['Jan/2024', 'Fev/2024', 'Mar/2024', 'Abr/2024', 'Mai/2024'],
    datasets: [
        {
            label: 'Jovens (0-3 anos)',
            backgroundColor: '#81C784',
            borderColor: '#4CAF50',
            borderWidth: 2,
            data: [12, 15, 18, 22, 25]
        },
        {
            label: 'Adultas Jovens (4-7 anos)',
            backgroundColor: '#FFB74D',
            borderColor: '#FF9800',
            borderWidth: 2,
            data: [45, 52, 58, 65, 72]
        },
        {
            label: 'Adultas (8-15 anos)',
            backgroundColor: '#B89C30',
            borderColor: '#F57F17',
            borderWidth: 2,
            data: [85, 92, 98, 105, 112]
        },
        {
            label: 'Maduras (16+ anos)',
            backgroundColor: '#8D6E63',
            borderColor: '#5D4037',
            borderWidth: 2,
            data: [65, 68, 72, 75, 78]
        }
    ]
}
```

### Opções do Gráfico
```javascript
chartOptions: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: 'top',
            labels: {
                usePointStyle: true,
                padding: 20,
                font: {
                    family: 'Lexend',
                    size: 14,
                    weight: 'bold'
                }
            }
        },
        tooltip: {
            mode: 'index',
            intersect: false,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            callbacks: {
                title: function(tooltipItems) {
                    return `Período: ${tooltipItems[0].label}`;
                },
                label: function(context) {
                    const value = context.parsed.y;
                    const unit = this.getAnalysisUnit();
                    return `${context.dataset.label}: ${value} ${unit}`;
                },
                afterBody: function(tooltipItems) {
                    const total = tooltipItems.reduce((sum, item) => sum + item.parsed.y, 0);
                    const unit = this.getAnalysisUnit();
                    return `Total: ${total} ${unit}`;
                }
            }
        }
    },
    scales: {
        x: {
            title: {
                display: true,
                text: 'Período de Coleta',
                font: {
                    family: 'Lexend',
                    size: 14,
                    weight: 'bold'
                },
                color: '#b89c30'
            }
        },
        y: {
            title: {
                display: true,
                text: this.getYAxisLabel(),
                font: {
                    family: 'Lexend',
                    size: 14,
                    weight: 'bold'
                },
                color: '#b89c30'
            },
            beginAtZero: true,
            ticks: {
                callback: function(value) {
                    return value + ' ' + this.getAnalysisUnit();
                }
            }
        }
    }
}
```

## 🔄 Fluxo de Funcionamento

### 1. Carregamento Inicial
```javascript
created() {
    this.authToken = window.token;
    this.getProperties();
    this.getPeriods();
}

methods: {
    async getProperties() {
        try {
            const response = await axios.get("/api/v1/properties", {
                headers: { authorization: `bearer ${this.authToken}` }
            });
            
            this.properties = response.data.data;
            if (this.properties.length > 0) {
                this.selectedOptions.propriedade = this.properties[0].id;
                this.getHomogeneousAreas();
            }
        } catch (error) {
            this.handleError(error);
        }
    }
}
```

### 2. Busca de Dados por Faixa Etária
```javascript
async getCocoaDataByAge() {
    if (!this.selectedOptions.propriedade || 
        !this.selectedOptions.area || 
        !this.selectedOptions.periodo) return;
    
    this.loading = true;
    
    try {
        const response = await axios.post("/api/v1/get-cocoa-data-by-age", {
            propertyId: this.selectedOptions.propriedade,
            areaHomogeneaId: this.selectedOptions.area,
            period: this.selectedOptions.periodo,
            analysisType: this.analysisType
        }, {
            headers: { authorization: `bearer ${this.authToken}` }
        });
        
        this.cocoaData = response.data;
        this.updateChart();
        this.calculateProductivityMetrics();
    } catch (error) {
        this.handleError(error);
    } finally {
        this.loading = false;
    }
}
```

### 3. Processamento dos Dados
```javascript
updateChart() {
    if (!this.cocoaData || Object.keys(this.cocoaData).length === 0) return;
    
    const periods = Object.keys(this.cocoaData).sort();
    const datasets = this.createAgeGroupDatasets(periods);
    
    this.chartData = {
        labels: periods,
        datasets: datasets
    };
    
    this.chartKey++;
}

createAgeGroupDatasets(periods) {
    const ageGroups = [
        { key: 'young', label: 'Jovens (0-3 anos)', color: '#81C784', border: '#4CAF50' },
        { key: 'youngAdult', label: 'Adultas Jovens (4-7 anos)', color: '#FFB74D', border: '#FF9800' },
        { key: 'adult', label: 'Adultas (8-15 anos)', color: '#B89C30', border: '#F57F17' },
        { key: 'mature', label: 'Maduras (16+ anos)', color: '#8D6E63', border: '#5D4037' }
    ];
    
    return ageGroups.map(group => ({
        label: group.label,
        backgroundColor: group.color,
        borderColor: group.border,
        borderWidth: 2,
        data: periods.map(period => this.getValueForAgeGroupAndPeriod(group.key, period))
    }));
}
```

## 📡 API Calls

### Endpoint Principal
```javascript
POST /api/v1/get-cocoa-data-by-age
```

### Parâmetros de Requisição
```javascript
{
    propertyId: number,
    areaHomogeneaId: number,
    period: string, // "2024-01" ou "2023/2024"
    analysisType: string // "production", "productivity", "efficiency"
}
```

### Estrutura de Resposta
```javascript
{
    "2024-01-15": {
        young_trees: {
            tree_count: 45,
            fruit_production: 12,
            productivity_per_tree: 0.27,
            age_range: "0-3 anos"
        },
        young_adult_trees: {
            tree_count: 38,
            fruit_production: 45,
            productivity_per_tree: 1.18,
            age_range: "4-7 anos"
        },
        adult_trees: {
            tree_count: 52,
            fruit_production: 85,
            productivity_per_tree: 1.63,
            age_range: "8-15 anos"
        },
        mature_trees: {
            tree_count: 35,
            fruit_production: 65,
            productivity_per_tree: 1.86,
            age_range: "16+ anos"
        },
        total_trees: 170,
        total_production: 207,
        collection_date: "2024-01-15"
    }
}
```

## 🎨 Estilização

### CSS Principal
```scss
.total-cocoa-wrapper {
    background-color: #f5f8fd;
    border-radius: 20px;
    
    .admin-content {
        height: 90vh;
        overflow-y: auto;
    }
    
    h1 {
        color: #b89c30;
        font-family: "Lexend", sans-serif;
        font-weight: 600;
        font-size: 24px;
    }
}

.controls-section {
    background: white;
    padding: 25px;
    margin: 20px;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    
    .controls-row {
        display: flex;
        gap: 25px;
        align-items: end;
        justify-content: center;
        flex-wrap: wrap;
    }
}

.chart-wrapper {
    margin: 1rem 3rem;
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    height: 600px;
}

.productivity-analysis {
    margin: 1rem 3rem;
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
```

## 📊 Análise de Produtividade

### Métricas Calculadas
```javascript
computed: {
    totalTrees() {
        return Object.values(this.cocoaData)
            .reduce((sum, period) => sum + period.total_trees, 0) / Object.keys(this.cocoaData).length;
    },
    
    totalProduction() {
        return Object.values(this.cocoaData)
            .reduce((sum, period) => sum + period.total_production, 0);
    },
    
    overallProductivity() {
        return this.totalTrees > 0 ? (this.totalProduction / this.totalTrees).toFixed(2) : 0;
    },
    
    mostProductiveAgeGroup() {
        const ageGroups = ['young_trees', 'young_adult_trees', 'adult_trees', 'mature_trees'];
        let maxProductivity = 0;
        let mostProductive = '';
        
        ageGroups.forEach(group => {
            const avgProductivity = this.getAverageProductivityForGroup(group);
            if (avgProductivity > maxProductivity) {
                maxProductivity = avgProductivity;
                mostProductive = group;
            }
        });
        
        return {
            group: this.getAgeGroupLabel(mostProductive),
            productivity: maxProductivity.toFixed(2)
        };
    },
    
    ageDistribution() {
        const ageGroups = ['young_trees', 'young_adult_trees', 'adult_trees', 'mature_trees'];
        const total = this.totalTrees;
        
        return ageGroups.map(group => ({
            label: this.getAgeGroupLabel(group),
            count: this.getAverageTreeCountForGroup(group),
            percentage: total > 0 ? ((this.getAverageTreeCountForGroup(group) / total) * 100).toFixed(1) : 0
        }));
    }
}
```

### Interface de Análise
```html
<div class="productivity-analysis">
    <h3>Análise de Produtividade por Faixa Etária</h3>
    
    <div class="analysis-grid">
        <div class="analysis-card highlight">
            <div class="card-header">
                <i class="fas fa-tree"></i>
                <h4>Total de Árvores</h4>
            </div>
            <div class="card-value">{{ Math.round(totalTrees).toLocaleString() }}</div>
            <div class="card-subtitle">Média por período</div>
        </div>
        
        <div class="analysis-card">
            <div class="card-header">
                <i class="fas fa-seedling"></i>
                <h4>Produção Total</h4>
            </div>
            <div class="card-value">{{ totalProduction.toLocaleString() }}</div>
            <div class="card-subtitle">Frutos no período</div>
        </div>
        
        <div class="analysis-card success">
            <div class="card-header">
                <i class="fas fa-chart-line"></i>
                <h4>Produtividade Geral</h4>
            </div>
            <div class="card-value">{{ overallProductivity }}</div>
            <div class="card-subtitle">Frutos por árvore</div>
        </div>
        
        <div class="analysis-card warning">
            <div class="card-header">
                <i class="fas fa-trophy"></i>
                <h4>Faixa Mais Produtiva</h4>
            </div>
            <div class="card-value">{{ mostProductiveAgeGroup.group }}</div>
            <div class="card-subtitle">{{ mostProductiveAgeGroup.productivity }} frutos/árvore</div>
        </div>
    </div>
    
    <div class="age-distribution">
        <h4>Distribuição por Faixa Etária</h4>
        <div class="distribution-grid">
            <div v-for="group in ageDistribution" :key="group.label" class="distribution-item">
                <div class="distribution-bar">
                    <div class="bar-fill" :style="{ width: group.percentage + '%' }"></div>
                </div>
                <div class="distribution-info">
                    <span class="group-label">{{ group.label }}</span>
                    <span class="group-count">{{ group.count }} árvores ({{ group.percentage }}%)</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="productivity-insights">
        <h4>Insights de Produtividade</h4>
        <ul>
            <li v-for="insight in productivityInsights" :key="insight.id">
                <i :class="insight.icon" :style="{ color: insight.color }"></i>
                {{ insight.text }}
            </li>
        </ul>
    </div>
</div>
```

## ✅ Validações

### Validação de Dados
```javascript
validateCocoaData(data) {
    if (!data || Object.keys(data).length === 0) {
        throw new Error('Nenhum dado de produção por faixa etária encontrado');
    }
    
    // Verificar se todos os períodos têm dados válidos
    const invalidPeriods = Object.entries(data).filter(([period, periodData]) => {
        return !periodData.total_trees || 
               periodData.total_trees <= 0 ||
               !periodData.total_production ||
               periodData.total_production < 0;
    });
    
    if (invalidPeriods.length > 0) {
        console.warn('Períodos com dados inválidos:', invalidPeriods);
    }
    
    return data;
}
```

## 🎯 Casos de Uso

### 1. Análise de Produtividade por Idade
1. Selecionar propriedade e área específica
2. Escolher período de análise
3. Comparar produção entre faixas etárias
4. Identificar faixas mais/menos produtivas

### 2. Planejamento de Replantio
1. Analisar distribuição etária atual
2. Identificar necessidade de renovação
3. Planejar cronograma de replantio
4. Otimizar mix de idades

### 3. Otimização de Manejo
1. Adaptar práticas por faixa etária
2. Personalizar adubação e tratos
3. Ajustar densidade de plantio
4. Maximizar produtividade geral

---

**Funcionalidade**: Visualização de produção por faixa etária das árvores  
**Tipo**: Análise de desenvolvimento  
**Cor**: Dourado (#b89c30)
