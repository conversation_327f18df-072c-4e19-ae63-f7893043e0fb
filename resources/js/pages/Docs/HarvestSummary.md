# 📊 Resumo da Safra - Análise de Dados

## 📍 Localização
- **Vue**: `resources/js/pages/DataAnalysis/HarvestSummary/HarvestSummary.vue`
- **JavaScript**: `resources/js/pages/DataAnalysis/HarvestSummary/HarvestSummary.js`
- **Configurações**: `resources/js/pages/DataAnalysis/HarvestSummary/TableOptions.js`

## 🎯 Propósito
Tela de visualização resumida dos dados da safra em formato tabular, permitindo análise consolidada da produção de cacau por propriedade e área homogênea.

## 🔗 Rota
- **Frontend**: `/panel/harvest-summary`
- **Permissão**: Administradores e Pré-cadastrados

## 📊 Funcionalidades

### 1. **Visualização Tabular**
- Dados organizados em tabela responsiva
- Colunas configuráveis
- Ordenação por colunas
- Paginação automática

### 2. **Filtros de Dados**
- **Propriedade**: Seleção de propriedade específica
- **Período**: Filtro por período de safra
- **<PERSON>rea <PERSON>**: Filtro por área específica

### 3. **Métricas Exibidas**
- **Produção Total**: Quantidade total de frutos
- **Produção por Área**: Distribuição por área homogênea
- **Médias**: Cálculos de produtividade média
- **Comparações**: Dados comparativos entre períodos

## 🎨 Interface

### Layout Principal
```html
<div class="harvest-summary-wrapper">
    <div class="admin-content">
        <div class="admin-header">
            <h1>Resumo da Safra</h1>
            <p>Visualize o resumo dos dados da safra.</p>
        </div>
        
        <div class="filters-section">
            <!-- Controles de filtro -->
        </div>
        
        <div class="table-container">
            <!-- Tabela de dados -->
        </div>
    </div>
</div>
```

### Controles de Filtro
```html
<div class="filters-row">
    <div class="form-group">
        <label>Propriedade</label>
        <select v-model="selectedProperty">
            <option v-for="property in properties" :value="property.id">
                {{ property.name }}
            </option>
        </select>
    </div>
    
    <div class="form-group">
        <label>Período</label>
        <select v-model="selectedPeriod">
            <option v-for="period in periods" :value="period">
                {{ period }}
            </option>
        </select>
    </div>
</div>
```

## 📋 Estrutura da Tabela

### Colunas Principais
```javascript
columns: [
    {
        label: 'Propriedade',
        field: 'property_name',
        sortable: true
    },
    {
        label: 'Área Homogênea',
        field: 'homogeneous_area',
        sortable: true
    },
    {
        label: 'Total de Frutos',
        field: 'total_fruits',
        type: 'number',
        sortable: true
    },
    {
        label: 'Produtividade',
        field: 'productivity',
        type: 'decimal',
        sortable: true
    },
    {
        label: 'Período',
        field: 'harvest_period',
        sortable: true
    }
]
```

### Configurações da Tabela
```javascript
// TableOptions.js
export default {
    pagination: {
        enabled: true,
        perPage: 20,
        perPageDropdown: [10, 20, 50, 100]
    },
    search: {
        enabled: true,
        placeholder: 'Buscar dados...'
    },
    sort: {
        enabled: true,
        initialSortBy: {
            field: 'total_fruits',
            type: 'desc'
        }
    }
}
```

## 🔄 Fluxo de Funcionamento

### 1. Carregamento Inicial
```javascript
created() {
    this.loadProperties();
    this.loadPeriods();
    this.loadHarvestData();
}
```

### 2. Aplicação de Filtros
```javascript
watch: {
    selectedProperty() {
        this.filterData();
    },
    selectedPeriod() {
        this.filterData();
    }
}
```

### 3. Processamento de Dados
```javascript
methods: {
    async loadHarvestData() {
        this.loading = true;
        try {
            const response = await this.fetchHarvestSummary();
            this.processData(response.data);
        } catch (error) {
            this.handleError(error);
        } finally {
            this.loading = false;
        }
    }
}
```

## 📡 API Calls

### Endpoint Principal
```javascript
GET /api/v1/harvest-summary
```

### Parâmetros de Filtro
```javascript
{
    property_id: number,
    period: string,
    homogeneous_area_id?: number
}
```

### Estrutura de Resposta
```javascript
{
    data: [
        {
            id: 1,
            property_name: "Fazenda São João",
            homogeneous_area: "Área A",
            total_fruits: 1250,
            productivity: 85.5,
            harvest_period: "2024-01",
            created_at: "2024-01-15"
        }
    ],
    meta: {
        total: 150,
        per_page: 20,
        current_page: 1
    }
}
```

## 🎨 Estilização

### CSS Principal
```scss
.harvest-summary-wrapper {
    background-color: #f5f8fd;
    border-radius: 20px;
    
    .admin-content {
        height: 90vh;
        overflow-y: auto;
    }
    
    h1 {
        color: #3d8160;
        font-family: "Lexend", sans-serif;
        font-weight: 600;
    }
}

.filters-section {
    background: white;
    padding: 20px;
    margin: 20px;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.table-container {
    background: white;
    margin: 20px;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
```

### Responsividade
```scss
@media (max-width: 768px) {
    .filters-row {
        flex-direction: column;
        gap: 15px;
    }
    
    .table-container {
        overflow-x: auto;
    }
}
```

## ✅ Validações

### Validação de Dados
```javascript
validateData(data) {
    return data.filter(item => {
        return item.total_fruits >= 0 &&
               item.property_name &&
               item.homogeneous_area;
    });
}
```

### Tratamento de Erros
```javascript
handleError(error) {
    if (error.response?.status === 404) {
        this.showMessage('Dados não encontrados para o período selecionado');
    } else {
        this.showMessage('Erro ao carregar dados da safra');
    }
}
```

## 📊 Métricas Calculadas

### Totalizadores
```javascript
computed: {
    totalFruits() {
        return this.data.reduce((sum, item) => sum + item.total_fruits, 0);
    },
    
    averageProductivity() {
        const total = this.data.reduce((sum, item) => sum + item.productivity, 0);
        return (total / this.data.length).toFixed(2);
    },
    
    topProducers() {
        return this.data
            .sort((a, b) => b.total_fruits - a.total_fruits)
            .slice(0, 5);
    }
}
```

## 🎯 Casos de Uso

### 1. Análise de Produtividade por Propriedade
1. Selecionar período específico
2. Ordenar por "Total de Frutos"
3. Identificar propriedades mais produtivas

### 2. Comparação entre Áreas Homogêneas
1. Filtrar por propriedade específica
2. Analisar dados por área homogênea
3. Comparar produtividade entre áreas

### 3. Acompanhamento Temporal
1. Alternar entre diferentes períodos
2. Observar tendências de produção
3. Identificar sazonalidades

## 🚨 Estados da Interface

### Loading
```html
<div v-if="loading" class="loading-state">
    <span class="spinner-border"></span>
    <p>Carregando dados da safra...</p>
</div>
```

### Sem Dados
```html
<div v-if="!loading && data.length === 0" class="empty-state">
    <i class="fas fa-table fa-3x"></i>
    <h3>Nenhum dado encontrado</h3>
    <p>Não há dados disponíveis para o período selecionado.</p>
</div>
```

### Com Dados
```html
<vue-good-table
    :columns="columns"
    :rows="data"
    :pagination-options="paginationOptions"
    :search-options="searchOptions"
/>
```

## 🔧 Componentes Utilizados

### Vue Good Table
- **Paginação**: Navegação entre páginas
- **Busca**: Filtro global de dados
- **Ordenação**: Por qualquer coluna
- **Responsividade**: Adaptação automática

### Filtros Customizados
- **Dropdowns**: Seleção de propriedade e período
- **Busca**: Campo de texto para filtro rápido
- **Limpeza**: Botão para resetar filtros

## 📈 Performance

### Otimizações
- **Lazy Loading**: Carregamento sob demanda
- **Debounce**: Filtros com delay
- **Cache**: Dados em memória local
- **Paginação**: Limitação de registros por página

### Métricas
- **Tempo de carregamento**: < 2 segundos
- **Responsividade**: Adaptação automática
- **Usabilidade**: Interface intuitiva

---

**Funcionalidade**: Visualização tabular de dados de safra  
**Tipo**: Análise de resumo  
**Cor**: Verde claro (#66bb6a)
