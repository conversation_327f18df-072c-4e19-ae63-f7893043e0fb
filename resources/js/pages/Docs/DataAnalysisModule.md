# 📊 Módulo de Análise de Dados - PlataformaCacau

## 📍 Localização
`resources/js/pages/DataAnalysis/DataAnalysis.vue`

## 🎯 Propósito
O Módulo de Análise de Dados oferece ferramentas abrangentes para visualização e análise de dados de produção de cacau, permitindo aos usuários acompanhar tendências, identificar padrões e tomar decisões baseadas em dados.

## 🔗 Rota Principal
- **Frontend**: `/panel/data-analysis`
- **Permissão**: Administradores e Pré-cadastrados

## 📊 Funcionalidades Principais

### 1. **Resumo da Safra**
- **Rota**: `/panel/harvest-summary`
- **Tipo**: Tabela de dados
- **Descrição**: Visualização resumida dos dados da safra
- **Cor**: Verde claro (#66bb6a)

### 2. **Resu<PERSON> da <PERSON>**
- **Rota**: `/panel/data-percentage`
- **Tipo**: Gráfico de pizza
- **Descrição**: Resumo dos dados de uma coleta específica
- **Cor**: Verde claro (#66bb6a)

### 3. **Frutos Colhidos por Área Homogênea**
- **Rota**: `/panel/total-harvested`
- **Tipo**: Gráfico de linhas
- **Descrição**: Acompanhamento da quantidade de frutos colhidos por área
- **Cor**: Verde escuro (#135d2a)

### 4. **Frutos Colhidos por Safra**
- **Rota**: `/panel/total-harvested-ha`
- **Tipo**: Gráfico de linhas
- **Descrição**: Quantidade de frutos colhidos por safra e área homogênea
- **Cor**: Verde escuro (#135d2a)

### 5. **Análise do Desenvolvimento dos Bilros**
- **Rota**: `/panel/safra-results`
- **Tipo**: Gráfico de barras empilhadas
- **Descrição**: Evolução dos bilros ao longo da safra
- **Cor**: Dourado (#b89c30)

### 6. **Quantidade de Frutos por Faixa**
- **Rota**: `/panel/total-cocoa`
- **Tipo**: Gráfico de barras agrupadas
- **Descrição**: Distribuição dos frutos por faixa etária das árvores
- **Cor**: Dourado (#b89c30)

## 🎨 Interface

### Layout Principal
```html
<div class="admin-content">
    <div class="admin-header">
        <h1>Análise de Dados</h1>
        <p>Selecione uma das opções abaixo para visualizar os dados.</p>
    </div>
    
    <div class="cards-container">
        <!-- Grid de cards responsivo -->
    </div>
</div>
```

### Estrutura dos Cards
```javascript
{
    icon: "fas fa-chart-type fa-lg",
    title: "Nome da Análise",
    description: "Descrição detalhada da funcionalidade",
    route: "rota-especifica",
    color: "#codigo-cor"
}
```

## 🎯 Categorias de Análise

### 📈 **Análises de Resumo** (Verde Claro)
- Resumo da Safra
- Resumo da Coleta

### 📊 **Análises de Produção** (Verde Escuro)
- Frutos Colhidos por Área Homogênea
- Frutos Colhidos por Safra

### 📋 **Análises de Desenvolvimento** (Dourado)
- Análise do Desenvolvimento dos Bilros
- Quantidade de Frutos por Faixa

## 🔄 Fluxo de Navegação

1. **Acesso**: Usuário acessa `/panel/data-analysis`
2. **Seleção**: Escolhe um dos 6 cards disponíveis
3. **Redirecionamento**: Sistema navega para a tela específica
4. **Análise**: Usuário visualiza dados e gráficos
5. **Retorno**: Botão "Voltar" retorna ao menu principal

## 📱 Responsividade

### Desktop
- Grid de 3 colunas
- Cards com hover effects
- Layout espaçado

### Tablet
- Grid de 2 colunas
- Cards adaptados

### Mobile
- Grid de 1 coluna
- Cards em largura total

## 🎨 Design System

### Cores por Categoria
```scss
// Resumos
$resumo-color: #66bb6a;

// Produção
$producao-color: #135d2a;

// Desenvolvimento
$desenvolvimento-color: #b89c30;
```

### Ícones por Tipo
- **Tabela**: `fas fa-table`
- **Pizza**: `fas fa-chart-pie`
- **Linhas**: `fas fa-chart-line`
- **Barras**: `fas fa-chart-bar`

## 🔐 Permissões

### Acesso Permitido
- **Administradores**: Acesso completo
- **Pré-cadastrados**: Acesso completo

### Middleware
```php
Route::get('/data-analysis', 'PanelController@index')
    ->middleware('role:admin, pre-registered');
```

## 📊 Métricas e KPIs

### Dados Analisados
- **Produção de frutos**
- **Desenvolvimento de bilros**
- **Distribuição por faixas etárias**
- **Tendências temporais**
- **Comparações entre áreas**

### Períodos de Análise
- **Por coleta**: Dados específicos de uma coleta
- **Por safra**: Dados agregados de uma safra completa
- **Temporal**: Evolução ao longo do tempo

## 🎯 Casos de Uso

### 1. Análise de Produtividade
- Acessar "Frutos Colhidos por Área Homogênea"
- Comparar produção entre diferentes áreas
- Identificar áreas mais produtivas

### 2. Acompanhamento de Safra
- Usar "Resumo da Safra"
- Monitorar progresso geral
- Avaliar metas e objetivos

### 3. Análise de Desenvolvimento
- Verificar "Desenvolvimento dos Bilros"
- Acompanhar evolução temporal
- Identificar padrões de crescimento

## 📈 Benefícios

### Para Produtores
- **Visibilidade**: Dados claros e organizados
- **Comparação**: Análise entre áreas e períodos
- **Tendências**: Identificação de padrões

### Para Gestores
- **Relatórios**: Dados consolidados
- **Decisões**: Base para planejamento
- **Monitoramento**: Acompanhamento contínuo

## 🔧 Tecnologias Utilizadas

### Frontend
- **Vue.js 2**: Framework principal
- **Chart.js**: Biblioteca de gráficos
- **Bootstrap**: Sistema de grid
- **SCSS**: Estilização avançada

### Componentes
- **LineChart**: Gráficos de linha
- **BarChart**: Gráficos de barra
- **PieChart**: Gráficos de pizza
- **DataTable**: Tabelas de dados

## 📚 Documentações Relacionadas

- [Resumo da Safra](./HarvestSummary.md)
- [Resumo da Coleta](./DataPercentage.md)
- [Frutos Colhidos por Área](./TotalHarvested.md)
- [Frutos Colhidos por Safra](./TotalHarvestedHA.md)
- [Desenvolvimento dos Bilros](./SafraResults.md)
- [Quantidade de Frutos por Faixa](./TotalCocoa.md)

---

**Última atualização**: 27 de dezembro de 2024  
**Versão**: 1.0.0
