<template>
	<div id="reports" class="mt-5 pruningWrapper">
	  <div class="admin-content">
		<div class="admin-header d-flex justify-content-between align-items-center p-2">
		  <div class="d-flex align-items-center">
			<back-button title="Voltar para propriedades"></back-button>
			<div class="d-flex flex-column">
			  <h1>Alerta de Vassoura-de-Bruxa</h1>
			  <p><PERSON><PERSON><PERSON>, a seguir, informações essenciais para o controle e manejo dessa doença.</p>
			</div>
		  </div>
		</div>

		<!-- Carrossel -->
		<div class="carousel-container">
			<div id="carouselExample" class="carousel slide" data-ride="carousel">
		  <div class="carousel-inner">
			<div class="carousel-item active">
			  <img src="./imagem1.png" class="d-block w-100" alt="Sintomas da Vassoura-de-Bruxa" />
			</div>
			<div class="carousel-item">
			  <img src="./imagem2.png" class="d-block w-100" alt="Poda Fitossanitária" />
			</div>
			<div class="carousel-item">
			  <img src="./imagem3.png" class="d-block w-100" alt="Cuidados Após a Poda" />
			</div>
			<div class="carousel-item">
			  <img src="./imagem4.png" class="d-block w-100" alt="Cuidados Após a Poda" />
			</div>
			<div class="carousel-item">
			  <img src="./imagem5.png" class="d-block w-100" alt="Cuidados Após a Poda" />
			</div>
		  </div>

		  <!-- Controles -->
		  <a class="carousel-control-prev" href="#carouselExample" role="button" data-slide="prev">
			<span class="carousel-control-prev-icon" aria-hidden="true"></span>
			<span class="sr-only">Anterior</span>
		  </a>
		  <a class="carousel-control-next" href="#carouselExample" role="button" data-slide="next">
			<span class="carousel-control-next-icon" aria-hidden="true"></span>
			<span class="sr-only">Próximo</span>
		  </a>
			</div>
		</div>

		<h3 class="apostila-text">Baixe a apostila com recomendações detalhadas de manejo da Vassoura-de-Bruxa:</h3>
		<div class="download-wrapper text-center">
			<button class="download-btn" @click="downloadHandout">
				<i class="fas fa-file-download"></i> Baixar Apostila
			</button>

			<!-- Botão para abrir modal de referências -->
			<button class="references-btn" @click="openReferencesModal">
				<i class="fas fa-quote-left"></i> Ver Referências Bibliográficas
			</button>
		</div>

		<!-- Modal de Referências -->
		<div class="modal fade" id="referencesModal" tabindex="-1" role="dialog" aria-labelledby="referencesModalLabel" aria-hidden="true">
			<div class="modal-dialog modal-lg modal-dialog-centered" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="referencesModalLabel">
							<i class="fas fa-quote-left"></i>
							Referências Bibliográficas
						</h5>
						<button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
					<div class="modal-body">
						<p class="references-description">Fontes científicas utilizadas na construção deste material</p>

						<div class="references-list">
							<div class="reference-item">
								<div class="reference-number">1</div>
								<div class="reference-text">
									<strong>LIMA, Sanlai Santos</strong> et al. Favorabilidade, distribuição e prevalência da vassoura-de-bruxa do cacaueiro no estado do Espírito Santo, Brasil.
									<em>Agrotrópica</em>, Ilhéus, v. 30, n. 1, p. 5-14, 2018.
									<span class="doi">DOI: 10.21757/0103-3816.2018v30n1p5-14</span>
								</div>
							</div>

							<div class="reference-item">
								<div class="reference-number">2</div>
								<div class="reference-text">
									<strong>RAMOS JÚNIOR, Isaías Flório</strong> et al. Estratégias integradas para o manejo da vassoura-de-bruxa no cacaueiro.
									In: <strong>ENCONTRO LATINO AMERICANO DE INICIAÇÃO CIENTÍFICA</strong>, 28., 2024, São José dos Campos.
									<em>Anais [...]</em>. São José dos Campos: Universidade do Vale do Paraíba, 2024.
								</div>
							</div>

							<div class="reference-item">
								<div class="reference-number">3</div>
								<div class="reference-text">
									<strong>SERRA, W. S.; SODRÉ, G. A.</strong> Manual do cacauicultor: perguntas e respostas.
									Ilhéus, BA: CEPLAC/CEPEC, 2021. 190 p.
									<span class="series">(Boletim Técnico, n. 221)</span>.
									<span class="issn">ISSN 0100-0845</span>.
								</div>
							</div>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-dismiss="modal">Fechar</button>
					</div>
				</div>
			</div>
		</div>

	  </div>
	</div>
</template>

<script>
export default {
	methods: {
		downloadHandout() {
		const fileUrl = "/apostila_vassoura_de_bruxa.pdf";
		const link = document.createElement("a");
		link.href = fileUrl;
		link.setAttribute("download", "./apostila_vassoura_de_bruxa.pdf");
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
		},
		openReferencesModal() {
			$('#referencesModal').modal('show');
		}
	}
};
</script>

  <style lang="scss">
  .pruningWrapper {
		background-color: #f5f8fd;
		border-radius: 20px;

		.admin-content {
			min-height: 90vh;
			height: auto;
			overflow: visible;
		}

		h1 {
			color: #3d8160;
			font-family: "Lexend", sans-serif;
			font-weight: 600;
			font-size: 24px;
			padding: 16px 0px 0px 16px;
			text-align: left;
		}

		p {
			font-family: "Lexend", sans-serif;
			font-size: 18px;
			padding: 0px 0px 0px 16px;
			text-align: left;
		}

		.carousel-container {
			max-width: 1000px;
			margin: 30px auto;
			padding: 0 20px;
			box-shadow: 0 4px 15px rgba(0,0,0,0.1);
			border-radius: 15px;
			overflow: hidden;
		}

		.carousel-inner {
			max-height: 600px;
			background-color: #f8f9fa;
			border-radius: 10px;
			overflow: hidden;
		}

		.carousel-inner img {
			width: 100%;
			height: 600px;
			object-fit: contain;
			background-color: #fff;
		}

		.carousel-caption {
			background: #3c8a5ef1;
			padding: 15px;
			border-radius: 5px;
			color: white;
			font-weight: bold;
		}

		.apostila-text {
			color: #3d8160;
			font-family: "Lexend", sans-serif;
			font-weight: 400;
			font-size: 16px;
			margin: 40px 20px 10px;
			text-align: center;
		}

		.download-wrapper {
			text-align: center;
			margin: 40px 0 80px 0;
			padding-bottom: 20px;
			display: flex;
			flex-direction: column;
			gap: 20px;
			align-items: center;
		}

		.download-btn {
			background-color: #3d8160;
			color: white;
			font-family: "Lexend", sans-serif;
			font-size: 16px;
			padding: 12px 24px;
			border: none;
			border-radius: 12px;
			cursor: pointer;
			transition: all 0.3s ease;
			box-shadow: 0px 4px 15px rgba(61, 129, 96, 0.3);
			font-weight: 600;

			i {
				margin-right: 8px;
			}

			&:hover {
				background-color: #2e644a;
				transform: translateY(-2px);
				box-shadow: 0px 6px 20px rgba(61, 129, 96, 0.4);
			}
		}

		.references-btn {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			color: white;
			font-family: "Lexend", sans-serif;
			font-size: 15px;
			font-weight: 500;
			padding: 10px 20px;
			border: none;
			border-radius: 25px;
			cursor: pointer;
			transition: all 0.3s ease;
			box-shadow: 0px 4px 15px rgba(102, 126, 234, 0.3);
			position: relative;
			overflow: hidden;
			display: inline-flex;
			align-items: center;
			gap: 8px;
			text-decoration: none;

			&::before {
				content: '';
				position: absolute;
				top: 0;
				left: -100%;
				width: 100%;
				height: 100%;
				background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
				transition: left 0.5s;
			}

			&:hover {
				transform: translateY(-2px);
				box-shadow: 0px 6px 25px rgba(102, 126, 234, 0.4);

				&::before {
					left: 100%;
				}
			}

			&:active {
				transform: translateY(0);
			}

			i {
				font-size: 14px;
				opacity: 0.9;
			}
		}

		/* Seção de Referências */
		.references-section {
			max-width: 1000px;
			margin: 60px auto 40px;
			padding: 0 20px;
		}

		.references-header {
			text-align: center;
			margin-bottom: 40px;

			h3 {
				color: #3d8160;
				font-family: "Lexend", sans-serif;
				font-weight: 600;
				font-size: 24px;
				margin-bottom: 10px;
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 12px;

				i {
					font-size: 20px;
				}
			}

			p {
				color: #6c757d;
				font-family: "Lexend", sans-serif;
				font-size: 16px;
				margin: 0;
				padding: 0;
			}
		}

		.references-content {
			background: white;
			border-radius: 15px;
			padding: 30px;
			box-shadow: 0 4px 15px rgba(0,0,0,0.1);
			border: 1px solid #e9ecef;
		}

		.references-list {
			display: flex;
			flex-direction: column;
			gap: 25px;
		}

		.reference-item {
			display: flex;
			gap: 20px;
			align-items: flex-start;
			padding: 20px;
			background: #f8f9fa;
			border-radius: 12px;
			border-left: 4px solid #3d8160;
			transition: all 0.3s ease;

			&:hover {
				background: #f1f3f4;
				transform: translateX(5px);
			}
		}

		.reference-number {
			background: #3d8160;
			color: white;
			width: 32px;
			height: 32px;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 14px;
			font-weight: 600;
			flex-shrink: 0;
			margin-top: 2px;
			font-family: "Lexend", sans-serif;
		}

		.reference-text {
			flex: 1;
			line-height: 1.7;
			font-size: 15px;
			color: #2c3e50;
			text-align: justify;
			font-family: "Lexend", sans-serif;

			strong {
				color: #1a202c;
				font-weight: 600;
			}

			em {
				color: #3d8160;
				font-style: italic;
				font-weight: 500;
			}

			.doi {
				display: inline-block;
				background: #e3f2fd;
				color: #1565c0;
				padding: 4px 8px;
				border-radius: 6px;
				font-size: 12px;
				font-weight: 500;
				margin-top: 8px;
				margin-left: 5px;
			}

			.series {
				color: #6c757d;
				font-style: italic;
				font-weight: 400;
			}

			.issn {
				background: #f3e5f5;
				color: #7b1fa2;
				padding: 4px 8px;
				border-radius: 6px;
				font-size: 12px;
				font-weight: 500;
				margin-left: 5px;
			}
		}

		/* Responsividade para referências */
		@media (max-width: 768px) {
			.references-section {
				margin: 40px auto 20px;
				padding: 0 15px;
			}

			.references-content {
				padding: 20px;
			}

			.reference-item {
				flex-direction: column;
				gap: 15px;
				padding: 15px;

				&:hover {
					transform: none;
				}
			}

			.reference-number {
				align-self: flex-start;
				width: 28px;
				height: 28px;
				font-size: 12px;
			}

			.reference-text {
				font-size: 14px;
				line-height: 1.6;
			}

			.references-header h3 {
				font-size: 20px;
				flex-direction: column;
				gap: 8px;
			}
		}

		/* Seção de Referências */
		.references-section {
			max-width: 1000px;
			margin: 60px auto 40px;
			padding: 0 20px;
		}

		.references-header {
			text-align: center;
			margin-bottom: 40px;

			h3 {
				color: #3d8160;
				font-family: "Lexend", sans-serif;
				font-weight: 600;
				font-size: 24px;
				margin-bottom: 10px;
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 12px;

				i {
					font-size: 20px;
				}
			}

			p {
				color: #6c757d;
				font-family: "Lexend", sans-serif;
				font-size: 16px;
				margin: 0;
				padding: 0;
			}
		}

		.references-content {
			background: white;
			border-radius: 15px;
			padding: 30px;
			box-shadow: 0 4px 15px rgba(0,0,0,0.1);
			border: 1px solid #e9ecef;
		}

		.references-list {
			display: flex;
			flex-direction: column;
			gap: 25px;
		}

		.reference-item {
			display: flex;
			gap: 20px;
			align-items: flex-start;
			padding: 20px;
			background: #f8f9fa;
			border-radius: 12px;
			border-left: 4px solid #3d8160;
			transition: all 0.3s ease;

			&:hover {
				background: #f1f3f4;
				transform: translateX(5px);
			}
		}

		.reference-number {
			background: #3d8160;
			color: white;
			width: 32px;
			height: 32px;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 14px;
			font-weight: 600;
			flex-shrink: 0;
			margin-top: 2px;
			font-family: "Lexend", sans-serif;
		}

		.reference-text {
			flex: 1;
			line-height: 1.7;
			font-size: 15px;
			color: #2c3e50;
			text-align: justify;
			font-family: "Lexend", sans-serif;

			strong {
				color: #1a202c;
				font-weight: 600;
			}

			em {
				color: #3d8160;
				font-style: italic;
				font-weight: 500;
			}

			.doi {
				display: inline-block;
				background: #e3f2fd;
				color: #1565c0;
				padding: 4px 8px;
				border-radius: 6px;
				font-size: 12px;
				font-weight: 500;
				margin-top: 8px;
				margin-left: 5px;
			}

			.series {
				color: #6c757d;
				font-style: italic;
				font-weight: 400;
			}

			.issn {
				background: #f3e5f5;
				color: #7b1fa2;
				padding: 4px 8px;
				border-radius: 6px;
				font-size: 12px;
				font-weight: 500;
				margin-left: 5px;
			}
		}

		/* Responsividade para referências */
		@media (max-width: 768px) {
			.references-section {
				margin: 40px auto 20px;
				padding: 0 15px;
			}

			.references-content {
				padding: 20px;
			}

			.reference-item {
				flex-direction: column;
				gap: 15px;
				padding: 15px;

				&:hover {
					transform: none;
				}
			}

			.reference-number {
				align-self: flex-start;
				width: 28px;
				height: 28px;
				font-size: 12px;
			}

			.reference-text {
				font-size: 14px;
				line-height: 1.6;
			}

			.references-header h3 {
				font-size: 20px;
				flex-direction: column;
				gap: 8px;
			}
		}
	}

  </style>
