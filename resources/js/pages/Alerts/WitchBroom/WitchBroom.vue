<template>
	<div id="reports" class="mt-5 pruningWrapper">
	  <div class="admin-content">
		<div class="admin-header d-flex justify-content-between align-items-center p-2">
		  <div class="d-flex align-items-center">
			<back-button title="Voltar para propriedades"></back-button>
			<div class="d-flex flex-column">
			  <h1>Alerta de Vassoura-de-Bruxa</h1>
			  <p><PERSON><PERSON><PERSON>, a seguir, informações essenciais para o controle e manejo dessa doença.</p>
			</div>
		  </div>
		</div>

		<!-- Carrossel -->
		<div class="carousel-container">
			<div id="carouselExample" class="carousel slide" data-ride="carousel">
		  <div class="carousel-inner">
			<div class="carousel-item active">
			  <img src="./imagem1.png" class="d-block w-100" alt="Sintomas da Vassoura-de-Bruxa" />
			</div>
			<div class="carousel-item">
			  <img src="./imagem2.png" class="d-block w-100" alt="Poda Fitossanitária" />
			</div>
			<div class="carousel-item">
			  <img src="./imagem3.png" class="d-block w-100" alt="Cuidados Após a Poda" />
			</div>
			<div class="carousel-item">
			  <img src="./imagem4.png" class="d-block w-100" alt="Cuidados Após a Poda" />
			</div>
			<div class="carousel-item">
			  <img src="./imagem5.png" class="d-block w-100" alt="Cuidados Após a Poda" />
			</div>
		  </div>

		  <!-- Controles -->
		  <a class="carousel-control-prev" href="#carouselExample" role="button" data-slide="prev">
			<span class="carousel-control-prev-icon" aria-hidden="true"></span>
			<span class="sr-only">Anterior</span>
		  </a>
		  <a class="carousel-control-next" href="#carouselExample" role="button" data-slide="next">
			<span class="carousel-control-next-icon" aria-hidden="true"></span>
			<span class="sr-only">Próximo</span>
		  </a>
			</div>
		</div>

		<h3 class="apostila-text">Baixe a apostila com recomendações detalhadas de manejo da Vassoura-de-Bruxa:</h3>
		<div class="download-wrapper text-center">
			<button class="download-btn" @click="downloadHandout">
				<i class="fas fa-file-download"></i> Baixar Apostila
			</button>
		</div>

	  </div>
	</div>
</template>

<script>
export default {
	methods: {
		downloadHandout() {
		const fileUrl = "/apostila_vassoura_de_bruxa.pdf";
		const link = document.createElement("a");
		link.href = fileUrl;
		link.setAttribute("download", "./apostila_vassoura_de_bruxa.pdf");
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
		}
	}
};
</script>

  <style lang="scss">
  .pruningWrapper {
		background-color: #f5f8fd;
		border-radius: 20px;

		.admin-content {
			min-height: 90vh;
			height: auto;
			overflow: visible;
		}

		h1 {
			color: #3d8160;
			font-family: "Lexend", sans-serif;
			font-weight: 600;
			font-size: 24px;
			padding: 16px 0px 0px 16px;
			text-align: left;
		}

		p {
			font-family: "Lexend", sans-serif;
			font-size: 18px;
			padding: 0px 0px 0px 16px;
			text-align: left;
		}

		.carousel-container {
			max-width: 1000px;
			margin: 30px auto;
			padding: 0 20px;
			box-shadow: 0 4px 15px rgba(0,0,0,0.1);
			border-radius: 15px;
			overflow: hidden;
		}

		.carousel-inner {
			max-height: 600px;
			background-color: #f8f9fa;
			border-radius: 10px;
			overflow: hidden;
		}

		.carousel-inner img {
			width: 100%;
			height: 600px;
			object-fit: contain;
			background-color: #fff;
		}

		.carousel-caption {
			background: #3c8a5ef1;
			padding: 15px;
			border-radius: 5px;
			color: white;
			font-weight: bold;
		}

		.apostila-text {
			color: #3d8160;
			font-family: "Lexend", sans-serif;
			font-weight: 400;
			font-size: 16px;
			margin: 40px 20px 10px;
			text-align: center;
		}

		.download-wrapper {
			text-align: center;
			margin: 40px 0 80px 0;
			padding-bottom: 20px;
		}

		.download-btn {
			background-color: #3d8160;
			color: white;
			font-family: "Lexend", sans-serif;
			font-size: 16px;
			padding: 10px 20px;
			border: none;
			border-radius: 10px;
			cursor: pointer;
			transition: background-color 0.3s ease;
			box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);

			i {
				margin-right: 8px;
			}

			&:hover {
				background-color: #2e644a;
			}
		}
	}

  </style>
