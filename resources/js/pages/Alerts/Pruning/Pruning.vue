<template>
	<div class="pruning-alert-container">
		<!-- Header Principal -->
		<div class="page-header">
			<div class="header-content">
				<back-button title="Voltar para propriedades"></back-button>
				<div class="header-info">
					<div class="header-icon">
						<i class="fas fa-cut"></i>
					</div>
					<div class="header-text">
						<h1>Alerta de Poda</h1>
						<p><PERSON><PERSON><PERSON>, a seguir, recomendações importantes para diferentes tipos de poda</p>
					</div>
				</div>
			</div>
		</div>

		<!-- <PERSON><PERSON><PERSON><PERSON> Principal -->
		<div class="main-content">
			<!-- Seção do Carrossel -->
			<div class="carousel-section">
				<div class="section-header">
					<div class="section-icon">
						<i class="fas fa-images"></i>
					</div>
					<div class="section-title">
						<h2>Guia Visual de Poda</h2>
						<p>Aprenda as técnicas corretas através de imagens ilustrativas</p>
					</div>
				</div>

				<div class="carousel-container">
					<div id="carouselPruning" class="carousel slide" data-ride="carousel" data-interval="5000">
						<ol class="carousel-indicators">
							<li data-target="#carouselPruning" data-slide-to="0" class="active"></li>
							<li data-target="#carouselPruning" data-slide-to="1"></li>
							<li data-target="#carouselPruning" data-slide-to="2"></li>
							<li data-target="#carouselPruning" data-slide-to="3"></li>
							<li data-target="#carouselPruning" data-slide-to="4"></li>
							<li data-target="#carouselPruning" data-slide-to="5"></li>
						</ol>

						<div class="carousel-inner">
							<div class="carousel-item active">
								<img src="./poda1.jpeg" class="d-block w-100" alt="Poda de Formação" />
								<div class="carousel-caption">
									<h5>Poda de Formação</h5>
									<p>Técnica fundamental para dar forma adequada à planta jovem</p>
								</div>
							</div>
							<div class="carousel-item">
								<img src="./poda2.jpeg" class="d-block w-100" alt="Poda Fitossanitária" />
								<div class="carousel-caption">
									<h5>Poda Fitossanitária</h5>
									<p>Remoção de partes doentes para manter a saúde da planta</p>
								</div>
							</div>
							<div class="carousel-item">
								<img src="./poda3.jpeg" class="d-block w-100" alt="Poda de Manutenção" />
								<div class="carousel-caption">
									<h5>Poda de Manutenção</h5>
									<p>Cuidados regulares para manter a produtividade</p>
								</div>
							</div>
							<div class="carousel-item">
								<img src="./poda4.jpeg" class="d-block w-100" alt="Ferramentas de Poda" />
								<div class="carousel-caption">
									<h5>Ferramentas Adequadas</h5>
									<p>Equipamentos corretos para cada tipo de corte</p>
								</div>
							</div>
							<div class="carousel-item">
								<img src="./poda5.jpeg" class="d-block w-100" alt="Época de Poda" />
								<div class="carousel-caption">
									<h5>Época Ideal</h5>
									<p>Timing correto para realizar cada tipo de poda</p>
								</div>
							</div>
							<div class="carousel-item">
								<img src="./poda6.jpeg" class="d-block w-100" alt="Cuidados Pós-Poda" />
								<div class="carousel-caption">
									<h5>Cuidados Pós-Poda</h5>
									<p>Tratamentos necessários após a realização da poda</p>
								</div>
							</div>
						</div>

						<!-- Controles -->
						<a class="carousel-control-prev" href="#carouselPruning" role="button" data-slide="prev">
							<span class="carousel-control-prev-icon" aria-hidden="true"></span>
							<span class="sr-only">Anterior</span>
						</a>
						<a class="carousel-control-next" href="#carouselPruning" role="button" data-slide="next">
							<span class="carousel-control-next-icon" aria-hidden="true"></span>
							<span class="sr-only">Próximo</span>
						</a>
					</div>
				</div>
			</div>

			<!-- Seção de Download -->
			<div class="download-section">
				<div class="section-header">
					<div class="section-icon">
						<i class="fas fa-download"></i>
					</div>
					<div class="section-title">
						<h2>Material de Apoio</h2>
						<p>Baixe recursos adicionais para aprofundar seus conhecimentos</p>
					</div>
				</div>

				<div class="download-content">
					<div class="download-card">
						<div class="download-icon">
							<i class="fas fa-file-pdf"></i>
						</div>
						<div class="download-info">
							<h3>Apostila de Poda</h3>
							<p>Guia completo com recomendações detalhadas sobre os tipos de poda</p>
						</div>
						<button class="download-btn" @click="downloadHandout">
							<i class="fas fa-download"></i>
							Baixar PDF
						</button>
					</div>

					<div class="references-card">
						<button class="references-btn" @click="openReferencesModal">
							<i class="fas fa-quote-left"></i>
							Ver Referências Bibliográficas
						</button>
					</div>
				</div>
			</div>
		</div>

		<!-- Modal de Referências -->
		<div class="modal fade" id="referencesModal" tabindex="-1" role="dialog" aria-labelledby="referencesModalLabel" aria-hidden="true">
			<div class="modal-dialog modal-lg modal-dialog-centered" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="referencesModalLabel">
							<i class="fas fa-quote-left"></i>
							Referências Bibliográficas
						</h5>
						<button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
					<div class="modal-body">
						<p class="references-description">Fontes científicas utilizadas na construção deste material</p>

						<div class="references-list">
							<div class="reference-item">
								<div class="reference-number">1</div>
								<div class="reference-text">
									<strong>SERVIÇO NACIONAL DE APRENDIZAGEM RURAL (Senar)</strong>. Cacau: produção, manejo e colheita.
									Brasília: Senar, 2018. 145 p.
									<span class="series">(Coleção Senar, 215)</span>.
									<span class="isbn">ISBN ***********-197-1</span>
								</div>
							</div>

							<div class="reference-item">
								<div class="reference-number">2</div>
								<div class="reference-text">
									<strong>MONTEIRO, Wilson Reis</strong> et al. Manejo de cacauais seminais pela poda e correção do sombreamento permanente.
									Ilhéus: CEPLAC/CEPEC, 2011. 30 p.
									<span class="series">(Boletim Técnico, n. 201)</span>.
									<span class="issn">ISSN 0100-0845</span>.
								</div>
							</div>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-dismiss="modal">Fechar</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	methods: {
		downloadHandout() {
			const fileUrl = "/apostila_poda.pdf";
			const link = document.createElement("a");
			link.href = fileUrl;
			link.setAttribute("download", "apostila_poda.pdf");
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
		},
		openReferencesModal() {
			$('#referencesModal').modal('show');
		}
	}
};
</script>

<style lang="scss" scoped>
// Variáveis
$primary-color: #3d8160;
$primary-dark: #2d6047;
$text-color: #2c3e50;
$text-light: #6c757d;
$white: #ffffff;
$background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
$shadow: rgba(0, 0, 0, 0.1);
$shadow-hover: rgba(0, 0, 0, 0.15);

.pruning-alert-container {
	min-height: 100vh;
	background: $background;
	padding: 1rem;
	font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

	// Header Principal
	.page-header {
		background: $white;
		border-radius: 20px;
		padding: 2rem;
		margin-bottom: 2rem;
		box-shadow: 0 10px 30px $shadow;
		border: 1px solid rgba(255, 255, 255, 0.2);
		backdrop-filter: blur(10px);

		.header-content {
			display: flex;
			align-items: center;
			gap: 1.5rem;
		}

		.header-info {
			display: flex;
			align-items: center;
			gap: 1.5rem;
			flex: 1;
		}

		.header-icon {
			width: 70px;
			height: 70px;
			background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
			border-radius: 20px;
			display: flex;
			align-items: center;
			justify-content: center;
			color: $white;
			font-size: 2rem;
			box-shadow: 0 8px 25px rgba($primary-color, 0.3);
			transition: transform 0.3s ease;

			&:hover {
				transform: scale(1.05);
			}
		}

		.header-text {
			h1 {
				font-size: 2.5rem;
				font-weight: 700;
				color: $text-color;
				margin: 0 0 0.5rem 0;
				background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
				background-clip: text;
			}

			p {
				font-size: 1.1rem;
				color: $text-light;
				margin: 0;
				font-weight: 400;
			}
		}
	}

	// Conteúdo Principal
	.main-content {
		display: flex;
		flex-direction: column;
		gap: 2rem;
	}

	// Seções
	.carousel-section, .download-section {
		background: $white;
		border-radius: 16px;
		padding: 2rem;
		box-shadow: 0 4px 20px $shadow;
		border: 1px solid rgba(255, 255, 255, 0.2);
		transition: transform 0.3s ease, box-shadow 0.3s ease;

		&:hover {
			transform: translateY(-2px);
			box-shadow: 0 8px 30px $shadow-hover;
		}
	}

	// Headers das Seções
	.section-header {
		display: flex;
		align-items: center;
		gap: 1rem;
		margin-bottom: 2rem;

		.section-icon {
			width: 60px;
			height: 60px;
			background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
			border-radius: 12px;
			display: flex;
			align-items: center;
			justify-content: center;
			color: $white;
			font-size: 1.5rem;
			box-shadow: 0 4px 15px rgba($primary-color, 0.3);
		}

		.section-title {
			h2 {
				font-size: 1.5rem;
				font-weight: 600;
				color: $text-color;
				margin: 0 0 0.5rem 0;
			}

			p {
				color: $text-light;
				margin: 0;
				font-size: 1rem;
			}
		}
	}

	// Carrossel
	.carousel-container {
		border-radius: 12px;
		overflow: hidden;
		box-shadow: 0 4px 20px $shadow;

		.carousel {
			border-radius: 12px;
		}

		.carousel-indicators {
			bottom: 15px;

			li {
				width: 12px;
				height: 12px;
				border-radius: 50%;
				background-color: rgba($white, 0.5);
				border: none;
				margin: 0 4px;
				transition: all 0.3s ease;

				&.active {
					background-color: $white;
					transform: scale(1.2);
				}
			}
		}

		.carousel-inner {
			border-radius: 12px;

			.carousel-item {
				img {
					width: 100%;
					height: 500px;
					object-fit: cover;
					border-radius: 12px;
				}

				.carousel-caption {
					background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
					border-radius: 0 0 12px 12px;
					padding: 2rem;
					bottom: 0;
					left: 0;
					right: 0;

					h5 {
						font-size: 1.5rem;
						font-weight: 600;
						margin-bottom: 0.5rem;
						color: $white;
					}

					p {
						font-size: 1rem;
						margin: 0;
						color: rgba($white, 0.9);
					}
				}
			}
		}

		.carousel-control-prev,
		.carousel-control-next {
			width: 50px;
			height: 50px;
			background: rgba(0, 0, 0, 0.7);
			border-radius: 50%;
			top: 50%;
			transform: translateY(-50%);
			opacity: 0.8;
			transition: all 0.3s ease;
			border: 2px solid rgba(255, 255, 255, 0.3);

			&:hover {
				opacity: 1;
				background: rgba(0, 0, 0, 0.9);
				transform: translateY(-50%) scale(1.1);
				border-color: rgba(255, 255, 255, 0.5);
			}

			.carousel-control-prev-icon,
			.carousel-control-next-icon {
				width: 20px;
				height: 20px;
				background-image: none;
				position: relative;

				&::before {
					content: '';
					position: absolute;
					top: 50%;
					left: 50%;
					width: 10px;
					height: 10px;
					border: solid $white;
					border-width: 0 3px 3px 0;
					transform: translate(-50%, -50%) rotate(-45deg);
				}
			}

			.carousel-control-prev-icon::before {
				transform: translate(-50%, -50%) rotate(135deg);
			}
		}

		.carousel-control-prev {
			left: 15px;
		}

		.carousel-control-next {
			right: 15px;
		}
	}

	// Seção de Download
	.download-content {
		display: flex;
		flex-direction: column;
		gap: 1.5rem;
	}

	.download-card {
		display: flex;
		align-items: center;
		gap: 1.5rem;
		padding: 1.5rem;
		background: rgba($primary-color, 0.05);
		border-radius: 12px;
		border: 1px solid rgba($primary-color, 0.1);
		transition: all 0.3s ease;

		&:hover {
			background: rgba($primary-color, 0.08);
			transform: translateY(-2px);
		}

		.download-icon {
			width: 60px;
			height: 60px;
			background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
			border-radius: 12px;
			display: flex;
			align-items: center;
			justify-content: center;
			color: $white;
			font-size: 1.5rem;
			flex-shrink: 0;
		}

		.download-info {
			flex: 1;

			h3 {
				font-size: 1.25rem;
				font-weight: 600;
				color: $text-color;
				margin: 0 0 0.5rem 0;
			}

			p {
				color: $text-light;
				margin: 0;
				line-height: 1.5;
			}
		}

		.download-btn {
			background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
			color: $white;
			border: none;
			padding: 0.75rem 1.5rem;
			border-radius: 8px;
			font-weight: 600;
			cursor: pointer;
			transition: all 0.3s ease;
			display: flex;
			align-items: center;
			gap: 0.5rem;
			box-shadow: 0 4px 15px rgba($primary-color, 0.3);
			flex-shrink: 0;

			&:hover {
				transform: translateY(-2px);
				box-shadow: 0 6px 20px rgba($primary-color, 0.4);
			}

			i {
				font-size: 1rem;
			}
		}
	}

	.references-card {
		text-align: center;

		.references-btn {
			background: transparent;
			color: $primary-color;
			border: 2px solid $primary-color;
			padding: 0.75rem 1.5rem;
			border-radius: 8px;
			font-weight: 500;
			cursor: pointer;
			transition: all 0.3s ease;
			display: inline-flex;
			align-items: center;
			gap: 0.5rem;

			&:hover {
				background: $primary-color;
				color: $white;
				transform: translateY(-2px);
			}

			i {
				font-size: 1rem;
			}
		}
	}

	// Responsividade
	@media (max-width: 768px) {
		.pruning-alert-container {
			padding: 0.5rem;
		}

		.page-header {
			padding: 1.5rem;
			margin-bottom: 1.5rem;

			.header-content {
				flex-direction: column;
				text-align: center;
				gap: 1rem;
			}

			.header-info {
				flex-direction: column;
				gap: 1rem;
			}

			.header-icon {
				width: 60px;
				height: 60px;
				font-size: 1.8rem;
			}

			.header-text h1 {
				font-size: 2rem;
			}

			.header-text p {
				font-size: 1rem;
			}
		}

		.carousel-section, .download-section {
			padding: 1.5rem;

			.section-header {
				flex-direction: column;
				text-align: center;
				gap: 1rem;

				.section-icon {
					width: 50px;
					height: 50px;
					font-size: 1.3rem;
				}
			}
		}

		.carousel-container {
			.carousel-inner .carousel-item img {
				height: 300px;
			}

			.carousel-control-prev,
			.carousel-control-next {
				width: 40px;
				height: 40px;
			}

			.carousel-indicators li {
				width: 10px;
				height: 10px;
			}
		}

		.download-card {
			flex-direction: column;
			text-align: center;
			gap: 1rem;

			.download-icon {
				width: 50px;
				height: 50px;
				font-size: 1.3rem;
			}

			.download-btn {
				padding: 0.6rem 1.2rem;
				font-size: 0.9rem;
			}
		}

		.references-btn {
			padding: 0.6rem 1.2rem;
			font-size: 0.9rem;
		}
	}

	@media (max-width: 480px) {
		.page-header .header-text h1 {
			font-size: 1.75rem;
		}

		.carousel-container .carousel-inner .carousel-item img {
			height: 250px;
		}

		.section-header .section-title h2 {
			font-size: 1.25rem;
		}
	}
}
</style>
