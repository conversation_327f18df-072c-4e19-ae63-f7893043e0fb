<template>
	<div id="reports" class="mt-5 pruningWrapper">
	  <div class="admin-content">
		<div class="admin-header d-flex justify-content-between align-items-center p-2">
		  <div class="d-flex align-items-center">
			<back-button title="Voltar para propriedades"></back-button>
			<div class="d-flex flex-column">
			  <h1>Alerta de Poda</h1>
			  <p><PERSON><PERSON><PERSON>, a seguir, recomendações importantes para diferentes tipos de poda.</p>
			</div>
		  </div>
		</div>

		<!-- Carrossel -->
		<div class="carousel-container">
			<div id="carouselExample" class="carousel slide" data-ride="carousel">
		  <div class="carousel-inner">
			<div class="carousel-item active">
			  <img src="./poda1.jpeg" class="d-block w-100" alt="Poda de Formação" />
			</div>
			<div class="carousel-item">
			  <img src="./poda2.jpeg" class="d-block w-100" alt="Poda Fitossanitária" />
			</div>
			<div class="carousel-item">
			  <img src="./poda3.jpeg" class="d-block w-100" alt="Cuidados Após a Poda" />
			</div>
			<div class="carousel-item">
			  <img src="./poda4.jpeg" class="d-block w-100" alt="Cuidados Após a Poda" />
			</div>
			<div class="carousel-item">
			  <img src="./poda5.jpeg" class="d-block w-100" alt="Cuidados Após a Poda" />
			</div>
			<div class="carousel-item">
			  <img src="./poda6.jpeg" class="d-block w-100" alt="Cuidados Após a Poda" />
			</div>
		  </div>

		  <!-- Controles -->
		  <a class="carousel-control-prev" href="#carouselExample" role="button" data-slide="prev">
			<span class="carousel-control-prev-icon" aria-hidden="true"></span>
			<span class="sr-only">Anterior</span>
		  </a>
		  <a class="carousel-control-next" href="#carouselExample" role="button" data-slide="next">
			<span class="carousel-control-next-icon" aria-hidden="true"></span>
			<span class="sr-only">Próximo</span>
		  </a>
			</div>
		</div>

		<h3 class="apostila-text">Baixe a apostila com recomendações detalhadas sobre os tipos de poda:</h3>
		<div class="download-wrapper text-center">
		  <button class="download-btn" @click="downloadHandout">
			<i class="fas fa-file-download"></i> Baixar Apostila
		  </button>
		</div>

	  </div>
	</div>
  </template>

  <script>
  export default {
	methods: {
	  downloadHandout() {
		const fileUrl = "/apostila_poda.pdf";
		const link = document.createElement("a");
		link.href = fileUrl;
		link.setAttribute("download", "./apostila_poda.pdf");
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
	  }
	}
  };
  </script>

<style lang="scss">
.pruningWrapper {
  background-color: #f5f8fd;
  border-radius: 20px;

  .admin-content {
    min-height: 100vh;
    height: auto;
    overflow: visible;
  }

  h1 {
    color: #3d8160;
    font-family: "Lexend", sans-serif;
    font-weight: 600;
    font-size: 24px;
    padding: 16px 0px 0px 16px;
    text-align: left;
  }

  p {
    font-family: "Lexend", sans-serif;
    font-size: 18px;
    padding: 0px 0px 0px 16px;
    text-align: left;
  }

  .carousel-container {
    max-width: 1000px;
    margin: 30px auto;
    padding: 0 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-radius: 15px;
    overflow: hidden;
  }

  .carousel-inner {
    max-height: 600px;
    background-color: #f8f9fa;
    border-radius: 10px;
    overflow: hidden;
  }

  .carousel-inner img {
    width: 100%;
    height: 600px;
    object-fit: contain;
    background-color: #fff;
  }

  // Remove imagem padrão das setas
  .carousel-control-prev-icon,
  .carousel-control-next-icon {
    background-image: none !important;
    width: 30px;
    height: 30px;
    display: inline-block;
    position: relative;
  }

  .carousel-control-prev-icon::before,
  .carousel-control-next-icon::before {
    content: '';
    display: block;
    width: 10px;
    height: 10px;
    border: solid black;
    border-width: 0 3px 3px 0;
    padding: 5px;
    transform: rotate(135deg);
    position: absolute;
    top: 50%;
    left: 50%;
    transform-origin: center;
    transform: translate(-50%, -50%) rotate(135deg);
  }

  .carousel-control-next-icon::before {
    transform: translate(-50%, -50%) rotate(-45deg);
  }

  .apostila-text {
    color: #3d8160;
    font-family: "Lexend", sans-serif;
    font-weight: 400;
    font-size: 16px;
    margin: 40px 20px 10px;
    text-align: center;
  }

  .download-wrapper {
    text-align: center;
    margin: 40px 0 80px 0;
    padding-bottom: 20px;
  }

  .download-btn {
    background-color: #3d8160;
    color: white;
    font-family: "Lexend", sans-serif;
    font-size: 16px;
    padding: 10px 20px;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);

    i {
      margin-right: 8px;
    }

    &:hover {
      background-color: #2e644a;
    }
  }
}
</style>

