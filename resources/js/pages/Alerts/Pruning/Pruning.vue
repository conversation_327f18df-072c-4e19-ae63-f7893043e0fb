<template>
	<div id="reports" class="mt-5 pruningWrapper">
	  <div class="admin-content">
		<div class="admin-header d-flex justify-content-between align-items-center p-2">
		  <div class="d-flex align-items-center">
			<back-button title="Voltar para propriedades"></back-button>
			<div class="d-flex flex-column">
			  <h1>Alerta de Poda</h1>
			  <p><PERSON><PERSON><PERSON>, a seguir, recomendações importantes para diferentes tipos de poda.</p>
			</div>
		  </div>
		</div>

		<!-- Carrossel -->
		<div class="carousel-container">
			<div id="carouselExample" class="carousel slide" data-ride="carousel">
		  <div class="carousel-inner">
			<div class="carousel-item active">
			  <img src="./poda1.jpeg" class="d-block w-100" alt="Poda de Formação" />
			</div>
			<div class="carousel-item">
			  <img src="./poda2.jpeg" class="d-block w-100" alt="Poda Fitossanitária" />
			</div>
			<div class="carousel-item">
			  <img src="./poda3.jpeg" class="d-block w-100" alt="Cuidados Após a Poda" />
			</div>
			<div class="carousel-item">
			  <img src="./poda4.jpeg" class="d-block w-100" alt="Cuidados Após a Poda" />
			</div>
			<div class="carousel-item">
			  <img src="./poda5.jpeg" class="d-block w-100" alt="Cuidados Após a Poda" />
			</div>
			<div class="carousel-item">
			  <img src="./poda6.jpeg" class="d-block w-100" alt="Cuidados Após a Poda" />
			</div>
		  </div>

		  <!-- Controles -->
		  <a class="carousel-control-prev" href="#carouselExample" role="button" data-slide="prev">
			<span class="carousel-control-prev-icon" aria-hidden="true"></span>
			<span class="sr-only">Anterior</span>
		  </a>
		  <a class="carousel-control-next" href="#carouselExample" role="button" data-slide="next">
			<span class="carousel-control-next-icon" aria-hidden="true"></span>
			<span class="sr-only">Próximo</span>
		  </a>
			</div>
		</div>

		<h3 class="apostila-text">Baixe a apostila com recomendações detalhadas sobre os tipos de poda:</h3>
		<div class="download-wrapper text-center">
		  <button class="download-btn" @click="downloadHandout">
			<i class="fas fa-file-download"></i> Baixar Apostila
		  </button>

		  <!-- Botão para abrir modal de referências -->
		  <button class="references-btn" @click="openReferencesModal">
			<i class="fas fa-quote-left"></i> Ver Referências Bibliográficas
		  </button>
		</div>

		<!-- Modal de Referências -->
		<div class="modal fade" id="referencesModal" tabindex="-1" role="dialog" aria-labelledby="referencesModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
			<div class="modal-content">
			  <div class="modal-header">
				<h5 class="modal-title" id="referencesModalLabel">
				  <i class="fas fa-quote-left"></i>
				  Referências Bibliográficas
				</h5>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
				  <span aria-hidden="true">&times;</span>
				</button>
			  </div>
			  <div class="modal-body">
				<p class="references-description">Fontes científicas utilizadas na construção deste material</p>

				<div class="references-list">
				  <div class="reference-item">
					<div class="reference-number">1</div>
					<div class="reference-text">
					  <strong>SERVIÇO NACIONAL DE APRENDIZAGEM RURAL (Senar)</strong>. Cacau: produção, manejo e colheita.
					  Brasília: Senar, 2018. 145 p.
					  <span class="series">(Coleção Senar, 215)</span>.
					  <span class="isbn">ISBN ***********-197-1</span>
					</div>
				  </div>

				  <div class="reference-item">
					<div class="reference-number">2</div>
					<div class="reference-text">
					  <strong>MONTEIRO, Wilson Reis</strong> et al. Manejo de cacauais seminais pela poda e correção do sombreamento permanente.
					  Ilhéus: CEPLAC/CEPEC, 2011. 30 p.
					  <span class="series">(Boletim Técnico, n. 201)</span>.
					  <span class="issn">ISSN 0100-0845</span>.
					</div>
				  </div>
				</div>
			  </div>
			  <div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal">Fechar</button>
			  </div>
			</div>
		  </div>
		</div>

	  </div>
	</div>
  </template>

  <script>
  export default {
	methods: {
	  downloadHandout() {
		const fileUrl = "/apostila_poda.pdf";
		const link = document.createElement("a");
		link.href = fileUrl;
		link.setAttribute("download", "./apostila_poda.pdf");
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
	  },
	  openReferencesModal() {
		$('#referencesModal').modal('show');
	  }
	}
  };
  </script>

<style lang="scss">
.pruningWrapper {
  background-color: #f5f8fd;
  border-radius: 20px;

  .admin-content {
    min-height: 100vh;
    height: auto;
    overflow: visible;
  }

  h1 {
    color: #3d8160;
    font-family: "Lexend", sans-serif;
    font-weight: 600;
    font-size: 24px;
    padding: 16px 0px 0px 16px;
    text-align: left;
  }

  p {
    font-family: "Lexend", sans-serif;
    font-size: 18px;
    padding: 0px 0px 0px 16px;
    text-align: left;
  }

  .carousel-container {
    max-width: 1000px;
    margin: 30px auto;
    padding: 0 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-radius: 15px;
    overflow: hidden;
  }

  .carousel-inner {
    max-height: 600px;
    background-color: #f8f9fa;
    border-radius: 10px;
    overflow: hidden;
  }

  .carousel-inner img {
    width: 100%;
    height: 600px;
    object-fit: contain;
    background-color: #fff;
  }

  // Remove imagem padrão das setas
  .carousel-control-prev-icon,
  .carousel-control-next-icon {
    background-image: none !important;
    width: 30px;
    height: 30px;
    display: inline-block;
    position: relative;
  }

  .carousel-control-prev-icon::before,
  .carousel-control-next-icon::before {
    content: '';
    display: block;
    width: 10px;
    height: 10px;
    border: solid black;
    border-width: 0 3px 3px 0;
    padding: 5px;
    transform: rotate(135deg);
    position: absolute;
    top: 50%;
    left: 50%;
    transform-origin: center;
    transform: translate(-50%, -50%) rotate(135deg);
  }

  .carousel-control-next-icon::before {
    transform: translate(-50%, -50%) rotate(-45deg);
  }

  .apostila-text {
    color: #3d8160;
    font-family: "Lexend", sans-serif;
    font-weight: 400;
    font-size: 16px;
    margin: 40px 20px 10px;
    text-align: center;
  }

  .download-wrapper {
    text-align: center;
    margin: 40px 0 80px 0;
    padding-bottom: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: center;
  }

  .download-btn {
    background-color: #3d8160;
    color: white;
    font-family: "Lexend", sans-serif;
    font-size: 16px;
    padding: 12px 24px;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0px 4px 15px rgba(61, 129, 96, 0.3);
    font-weight: 600;

    i {
      margin-right: 8px;
    }

    &:hover {
      background-color: #2e644a;
      transform: translateY(-2px);
      box-shadow: 0px 6px 20px rgba(61, 129, 96, 0.4);
    }
  }

  .references-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-family: "Lexend", sans-serif;
    font-size: 15px;
    font-weight: 500;
    padding: 10px 20px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0px 4px 15px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
      transition: left 0.5s;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0px 6px 25px rgba(102, 126, 234, 0.4);

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: translateY(0);
    }

    i {
      font-size: 14px;
      opacity: 0.9;
    }
  }

  /* Modal de Referências */
  .modal-header {
    background-color: #3d8160;
    color: white;
    border-bottom: none;

    .modal-title {
      font-family: "Lexend", sans-serif;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .close {
      color: white;
      opacity: 0.8;

      &:hover {
        opacity: 1;
      }
    }
  }

  .references-description {
    color: #6c757d;
    font-family: "Lexend", sans-serif;
    font-size: 14px;
    margin-bottom: 20px;
    text-align: center;
    font-style: italic;
  }

  .references-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .reference-item {
    display: flex;
    gap: 15px;
    align-items: flex-start;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #3d8160;
  }

  .reference-number {
    background: #3d8160;
    color: white;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    flex-shrink: 0;
    margin-top: 2px;
    font-family: "Lexend", sans-serif;
  }

  .reference-text {
    flex: 1;
    line-height: 1.6;
    font-size: 14px;
    color: #2c3e50;
    text-align: justify;
    font-family: "Lexend", sans-serif;

    strong {
      color: #1a202c;
      font-weight: 600;
    }

    em {
      color: #3d8160;
      font-style: italic;
      font-weight: 500;
    }

    .series {
      color: #6c757d;
      font-style: italic;
      font-weight: 400;
    }

    .isbn {
      background: #e8f5e8;
      color: #2e7d32;
      padding: 3px 6px;
      border-radius: 4px;
      font-size: 11px;
      font-weight: 500;
      margin-left: 5px;
    }

    .issn {
      background: #f3e5f5;
      color: #7b1fa2;
      padding: 3px 6px;
      border-radius: 4px;
      font-size: 11px;
      font-weight: 500;
      margin-left: 5px;
    }
  }

  /* Responsividade para modal */
  @media (max-width: 768px) {
    .download-wrapper {
      flex-direction: column;
      gap: 15px;
    }

    .download-btn, .references-btn {
      font-size: 14px;
      padding: 10px 18px;
    }

    .reference-item {
      flex-direction: column;
      gap: 10px;
      padding: 12px;
    }

    .reference-number {
      align-self: flex-start;
      width: 24px;
      height: 24px;
      font-size: 11px;
    }

    .reference-text {
      font-size: 13px;
      line-height: 1.5;
    }
  }
}
</style>
