<template>
	<div class="pruning-alert-container">
		<!-- Header Principal -->
		<div class="page-header">
			<div class="header-content">
				<back-button title="Voltar para propriedades"></back-button>
				<div class="header-icon">
					<i class="fas fa-cut"></i>
				</div>
				<div class="header-text">
					<h1>Alerta de Poda</h1>
					<p><PERSON><PERSON><PERSON>, a seguir, recomendações importantes para diferentes tipos de poda</p>
				</div>
			</div>
		</div>

		<!-- Carrossel -->
		<div class="carousel-container">
			<div id="carouselExample" class="carousel slide" data-ride="carousel">
				<div class="carousel-inner">
					<div class="carousel-item active">
						<img src="./poda1.jpeg" class="d-block w-100" alt="Poda de Formação" />
					</div>
					<div class="carousel-item">
						<img src="./poda2.jpeg" class="d-block w-100" alt="Poda Fitossanitária" />
					</div>
					<div class="carousel-item">
						<img src="./poda3.jpeg" class="d-block w-100" alt="Cuidados Após a Poda" />
					</div>
					<div class="carousel-item">
						<img src="./poda4.jpeg" class="d-block w-100" alt="Cuidados Após a Poda" />
					</div>
					<div class="carousel-item">
						<img src="./poda5.jpeg" class="d-block w-100" alt="Cuidados Após a Poda" />
					</div>
					<div class="carousel-item">
						<img src="./poda6.jpeg" class="d-block w-100" alt="Cuidados Após a Poda" />
					</div>
				</div>

				<!-- Controles -->
				<a class="carousel-control-prev" href="#carouselExample" role="button" data-slide="prev">
					<span class="carousel-control-prev-icon" aria-hidden="true"></span>
					<span class="sr-only">Anterior</span>
				</a>
				<a class="carousel-control-next" href="#carouselExample" role="button" data-slide="next">
					<span class="carousel-control-next-icon" aria-hidden="true"></span>
					<span class="sr-only">Próximo</span>
				</a>
			</div>
		</div>

		<h3 class="apostila-text">Baixe a apostila com recomendações detalhadas sobre os tipos de poda:</h3>
		<div class="download-wrapper text-center">
			<button class="download-btn" @click="downloadHandout">
				<i class="fas fa-file-download"></i> Baixar Apostila
			</button>

			<!-- Botão para abrir modal de referências -->
			<button class="references-btn" @click="openReferencesModal">
				<i class="fas fa-quote-left"></i> Ver Referências Bibliográficas
			</button>
		</div>

		<!-- Modal de Referências -->
		<div class="modal fade" id="referencesModal" tabindex="-1" role="dialog" aria-labelledby="referencesModalLabel" aria-hidden="true">
			<div class="modal-dialog modal-lg modal-dialog-centered" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="referencesModalLabel">
							<i class="fas fa-quote-left"></i>
							Referências Bibliográficas
						</h5>
						<button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
					<div class="modal-body">
						<p class="references-description">Fontes científicas utilizadas na construção deste material</p>

						<div class="references-list">
							<div class="reference-item">
								<div class="reference-number">1</div>
								<div class="reference-text">
									<strong>SERVIÇO NACIONAL DE APRENDIZAGEM RURAL (Senar)</strong>. Cacau: produção, manejo e colheita.
									Brasília: Senar, 2018. 145 p.
									<span class="series">(Coleção Senar, 215)</span>.
									<span class="isbn">ISBN ***********-197-1</span>
								</div>
							</div>

							<div class="reference-item">
								<div class="reference-number">2</div>
								<div class="reference-text">
									<strong>MONTEIRO, Wilson Reis</strong> et al. Manejo de cacauais seminais pela poda e correção do sombreamento permanente.
									Ilhéus: CEPLAC/CEPEC, 2011. 30 p.
									<span class="series">(Boletim Técnico, n. 201)</span>.
									<span class="issn">ISSN 0100-0845</span>.
								</div>
							</div>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-dismiss="modal">Fechar</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	methods: {
		downloadHandout() {
			const fileUrl = "/apostila_poda.pdf";
			const link = document.createElement("a");
			link.href = fileUrl;
			link.setAttribute("download", "apostila_poda.pdf");
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
		},
		openReferencesModal() {
			$('#referencesModal').modal('show');
		}
	}
};
</script>

<style lang="scss">
.pruning-alert-container {
	background-color: #f5f8fd;
	border-radius: 20px;
	min-height: 100vh;
	font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

	// Header no estilo DataImport
	.page-header {
		background: #ffffff;
		border-radius: 20px;
		padding: 2rem;
		margin-bottom: 2rem;
		box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
		border: 1px solid rgba(255, 255, 255, 0.2);
		backdrop-filter: blur(10px);

		.header-content {
			display: flex;
			align-items: center;
			gap: 1.5rem;
		}

		.header-icon {
			width: 70px;
			height: 70px;
			background: linear-gradient(135deg, #3d8160 0%, #2d6047 100%);
			border-radius: 20px;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #ffffff;
			font-size: 2rem;
			box-shadow: 0 8px 25px rgba(61, 129, 96, 0.3);
		}

		.header-text {
			h1 {
				font-size: 2.5rem;
				font-weight: 700;
				color: #2c3e50;
				margin: 0 0 0.5rem 0;
				background: linear-gradient(135deg, #3d8160 0%, #2d6047 100%);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
				background-clip: text;
			}

			p {
				font-size: 1.1rem;
				color: #6c757d;
				margin: 0;
				font-weight: 400;
			}
		}
	}

	.carousel-container {
		max-width: 1000px;
		margin: 30px auto;
		padding: 0 20px;
		box-shadow: 0 4px 15px rgba(0,0,0,0.1);
		border-radius: 15px;
		overflow: hidden;
	}

	.carousel-inner {
		max-height: 600px;
		background-color: #f8f9fa;
		border-radius: 10px;
		overflow: hidden;
	}

	.carousel-inner img {
		width: 100%;
		height: 600px;
		object-fit: contain;
		background-color: #fff;
	}

	// Remove imagem padrão das setas
	.carousel-control-prev-icon,
	.carousel-control-next-icon {
		background-image: none !important;
		width: 30px;
		height: 30px;
		display: inline-block;
		position: relative;
	}

	.carousel-control-prev-icon::before,
	.carousel-control-next-icon::before {
		content: '';
		display: block;
		width: 10px;
		height: 10px;
		border: solid black;
		border-width: 0 3px 3px 0;
		padding: 5px;
		transform: rotate(135deg);
		position: absolute;
		top: 50%;
		left: 50%;
		transform-origin: center;
		transform: translate(-50%, -50%) rotate(135deg);
	}

	.carousel-control-next-icon::before {
		transform: translate(-50%, -50%) rotate(-45deg);
	}

	.apostila-text {
		color: #3d8160;
		font-family: "Lexend", sans-serif;
		font-weight: 400;
		font-size: 16px;
		margin: 40px 20px 10px;
		text-align: center;
	}

	.download-wrapper {
		text-align: center;
		margin: 40px 0 80px 0;
		padding-bottom: 20px;
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 20px;
	}

	.download-btn {
		background: linear-gradient(135deg, #3d8160 0%, #2d6047 100%);
		color: white;
		border: none;
		padding: 15px 30px;
		border-radius: 10px;
		font-family: "Lexend", sans-serif;
		font-weight: 600;
		font-size: 16px;
		cursor: pointer;
		transition: all 0.3s ease;
		box-shadow: 0 4px 15px rgba(61, 129, 96, 0.3);
		display: inline-flex;
		align-items: center;
		gap: 10px;

		&:hover {
			transform: translateY(-2px);
			box-shadow: 0 6px 20px rgba(61, 129, 96, 0.4);
		}

		i {
			font-size: 18px;
		}
	}

	.references-btn {
		background: transparent;
		color: #3d8160;
		border: 2px solid #3d8160;
		padding: 12px 25px;
		border-radius: 8px;
		font-family: "Lexend", sans-serif;
		font-weight: 500;
		font-size: 14px;
		cursor: pointer;
		transition: all 0.3s ease;
		display: inline-flex;
		align-items: center;
		gap: 8px;

		&:hover {
			background: #3d8160;
			color: white;
			transform: translateY(-1px);
		}

		i {
			font-size: 14px;
		}
	}
	// Responsividade
	@media (max-width: 768px) {
		.page-header {
			padding: 1.5rem;
			margin-bottom: 1.5rem;

			.header-content {
				flex-direction: column;
				text-align: center;
				gap: 1rem;
			}

			.header-icon {
				width: 60px;
				height: 60px;
				font-size: 1.8rem;
			}

			.header-text h1 {
				font-size: 2rem;
			}

			.header-text p {
				font-size: 1rem;
			}
		}

		.carousel-container {
			margin: 20px 10px;
			padding: 0 10px;
		}

		.carousel-inner img {
			height: 400px;
		}

		.apostila-text {
			font-size: 14px;
			margin: 30px 15px 15px;
		}

		.download-wrapper {
			margin: 30px 0 60px 0;
			gap: 15px;
		}

		.download-btn {
			padding: 12px 25px;
			font-size: 14px;
		}

		.references-btn {
			padding: 10px 20px;
			font-size: 13px;
		}
	}

	@media (max-width: 480px) {
		.page-header .header-text h1 {
			font-size: 1.75rem;
		}
	}
}
</style>
