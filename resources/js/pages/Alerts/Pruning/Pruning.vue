<template>
	<div class="pruning-alert-container">
		<!-- Header Principal -->
		<div class="page-header">
			<div class="header-content">
				<back-button title="Voltar para propriedades"></back-button>
				<div class="header-icon">
					<i class="fas fa-cut"></i>
				</div>
				<div class="header-text">
					<h1>Alerta de Poda</h1>
					<p><PERSON><PERSON><PERSON>, a seguir, recomendações importantes para diferentes tipos de poda</p>
				</div>
			</div>
		</div>

		<!-- Seção de Conteúdo Educativo -->
		<div class="content-section">
			<!-- Card de Galeria de Imagens -->
			<div class="gallery-card">
				<div class="card-header">
					<div class="card-icon">
						<i class="fas fa-images"></i>
					</div>
					<div class="card-title">
						<h2>Guia Visual de Poda</h2>
						<p>Aprenda as técnicas corretas através de imagens ilustrativas</p>
					</div>
				</div>

				<div class="gallery-container">
					<div class="image-carousel">
						<div class="carousel-wrapper">
							<div class="carousel-track" :style="{ transform: `translateX(-${currentSlide * 100}%)` }">
								<div class="carousel-slide" v-for="(image, index) in images" :key="index">
									<div class="image-container">
										<img :src="image.src" :alt="image.alt" class="carousel-image" />
										<div class="image-overlay">
											<h3>{{ image.title }}</h3>
											<p>{{ image.description }}</p>
										</div>
									</div>
								</div>
							</div>
						</div>

						<!-- Controles do Carrossel -->
						<div class="carousel-controls">
							<button class="carousel-btn prev-btn" @click="previousSlide" :disabled="currentSlide === 0">
								<i class="fas fa-chevron-left"></i>
							</button>
							<button class="carousel-btn next-btn" @click="nextSlide" :disabled="currentSlide === images.length - 1">
								<i class="fas fa-chevron-right"></i>
							</button>
						</div>

						<!-- Indicadores -->
						<div class="carousel-indicators">
							<button
								v-for="(image, index) in images"
								:key="index"
								class="indicator"
								:class="{ active: currentSlide === index }"
								@click="goToSlide(index)"
							></button>
						</div>
					</div>
				</div>
			</div>

		<!-- Card de Download e Recursos -->
		<div class="resources-card">
			<div class="card-header">
				<div class="card-icon">
					<i class="fas fa-download"></i>
				</div>
				<div class="card-title">
					<h2>Material de Apoio</h2>
					<p>Baixe recursos adicionais para aprofundar seus conhecimentos</p>
				</div>
			</div>

			<div class="resources-content">
				<div class="download-section">
					<div class="download-item">
						<div class="download-icon">
							<i class="fas fa-file-pdf"></i>
						</div>
						<div class="download-info">
							<h3>Apostila de Poda</h3>
							<p>Guia completo com recomendações detalhadas sobre os tipos de poda</p>
						</div>
						<button class="download-btn" @click="downloadHandout">
							<i class="fas fa-download"></i>
							Baixar PDF
						</button>
					</div>
				</div>

				<div class="references-section">
					<button class="references-btn" @click="openReferencesModal">
						<i class="fas fa-quote-left"></i>
						Ver Referências Bibliográficas
					</button>
				</div>
			</div>
		</div>
	</div>

		<!-- Modal de Referências -->
		<div class="modal fade" id="referencesModal" tabindex="-1" role="dialog" aria-labelledby="referencesModalLabel" aria-hidden="true">
		  <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
			<div class="modal-content">
			  <div class="modal-header">
				<h5 class="modal-title" id="referencesModalLabel">
				  <i class="fas fa-quote-left"></i>
				  Referências Bibliográficas
				</h5>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
				  <span aria-hidden="true">&times;</span>
				</button>
			  </div>
			  <div class="modal-body">
				<p class="references-description">Fontes científicas utilizadas na construção deste material</p>

				<div class="references-list">
				  <div class="reference-item">
					<div class="reference-number">1</div>
					<div class="reference-text">
					  <strong>SERVIÇO NACIONAL DE APRENDIZAGEM RURAL (Senar)</strong>. Cacau: produção, manejo e colheita.
					  Brasília: Senar, 2018. 145 p.
					  <span class="series">(Coleção Senar, 215)</span>.
					  <span class="isbn">ISBN ***********-197-1</span>
					</div>
				  </div>

				  <div class="reference-item">
					<div class="reference-number">2</div>
					<div class="reference-text">
					  <strong>MONTEIRO, Wilson Reis</strong> et al. Manejo de cacauais seminais pela poda e correção do sombreamento permanente.
					  Ilhéus: CEPLAC/CEPEC, 2011. 30 p.
					  <span class="series">(Boletim Técnico, n. 201)</span>.
					  <span class="issn">ISSN 0100-0845</span>.
					</div>
				  </div>
				</div>
			  </div>
			  <div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal">Fechar</button>
			  </div>
			</div>
		  </div>
		</div>

</template>

<script>
export default {
	name: 'PruningAlert',
	data() {
		return {
			currentSlide: 0,
			images: [
				{
					src: './poda1.jpeg',
					alt: 'Poda de Formação',
					title: 'Poda de Formação',
					description: 'Técnica fundamental para dar forma adequada à planta jovem'
				},
				{
					src: './poda2.jpeg',
					alt: 'Poda Fitossanitária',
					title: 'Poda Fitossanitária',
					description: 'Remoção de partes doentes para manter a saúde da planta'
				},
				{
					src: './poda3.jpeg',
					alt: 'Poda de Manutenção',
					title: 'Poda de Manutenção',
					description: 'Cuidados regulares para manter a produtividade'
				},
				{
					src: './poda4.jpeg',
					alt: 'Ferramentas de Poda',
					title: 'Ferramentas Adequadas',
					description: 'Equipamentos corretos para cada tipo de corte'
				},
				{
					src: './poda5.jpeg',
					alt: 'Época de Poda',
					title: 'Época Ideal',
					description: 'Timing correto para realizar cada tipo de poda'
				},
				{
					src: './poda6.jpeg',
					alt: 'Cuidados Pós-Poda',
					title: 'Cuidados Pós-Poda',
					description: 'Tratamentos necessários após a realização da poda'
				}
			]
		}
	},
	methods: {
		nextSlide() {
			if (this.currentSlide < this.images.length - 1) {
				this.currentSlide++;
			}
		},
		previousSlide() {
			if (this.currentSlide > 0) {
				this.currentSlide--;
			}
		},
		goToSlide(index) {
			this.currentSlide = index;
		},
		downloadHandout() {
			const fileUrl = "/apostila_poda.pdf";
			const link = document.createElement("a");
			link.href = fileUrl;
			link.setAttribute("download", "apostila_poda.pdf");
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
		},
		openReferencesModal() {
			$('#referencesModal').modal('show');
		}
	},
	mounted() {
		// Auto-play do carrossel (opcional)
		setInterval(() => {
			this.nextSlide();
			if (this.currentSlide === this.images.length - 1) {
				this.currentSlide = 0;
			}
		}, 5000);
	}
};
</script>

<style lang="scss" scoped>
// Variáveis
$primary-color: #3d8160;
$primary-dark: #2d6047;
$text-color: #2c3e50;
$text-light: #6c757d;
$white: #ffffff;
$background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
$shadow: rgba(0, 0, 0, 0.1);
$shadow-hover: rgba(0, 0, 0, 0.15);
$border-color: #dee2e6;
$success-color: #28a745;
$danger-color: #dc3545;

// Container principal
.pruning-alert-container {
	min-height: 100vh;
	background: $background;
	padding: 1rem;
	font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

// Header no estilo DataImport
.page-header {
	background: $white;
	border-radius: 20px;
	padding: 2rem;
	margin-bottom: 2rem;
	box-shadow: 0 10px 30px $shadow;
	border: 1px solid rgba(255, 255, 255, 0.2);
	backdrop-filter: blur(10px);

	.header-content {
		display: flex;
		align-items: center;
		gap: 1.5rem;
	}

	.header-icon {
		width: 70px;
		height: 70px;
		background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
		border-radius: 20px;
		display: flex;
		align-items: center;
		justify-content: center;
		color: $white;
		font-size: 2rem;
		box-shadow: 0 8px 25px rgba($primary-color, 0.3);
	}

	.header-text {
		h1 {
			font-size: 2.5rem;
			font-weight: 700;
			color: $text-color;
			margin: 0 0 0.5rem 0;
			background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			background-clip: text;
		}

		p {
			font-size: 1.1rem;
			color: $text-light;
			margin: 0;
			font-weight: 400;
		}
	}
}

// Seção de conteúdo
.content-section {
	display: flex;
	flex-direction: column;
	gap: 2rem;
}

// Cards base
.gallery-card, .resources-card {
	background: $white;
	border-radius: 16px;
	padding: 2rem;
	box-shadow: 0 4px 20px $shadow;
	border: 1px solid rgba(255, 255, 255, 0.2);

	.card-header {
		display: flex;
		align-items: center;
		gap: 1rem;
		margin-bottom: 2rem;

		.card-icon {
			width: 60px;
			height: 60px;
			background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
			border-radius: 12px;
			display: flex;
			align-items: center;
			justify-content: center;
			color: $white;
			font-size: 1.5rem;
			box-shadow: 0 4px 15px rgba($primary-color, 0.3);
		}

		.card-title {
			h2 {
				font-size: 1.5rem;
				font-weight: 600;
				color: $text-color;
				margin: 0 0 0.5rem 0;
			}

			p {
				color: $text-light;
				margin: 0;
				font-size: 1rem;
			}
		}
	}
}

// Galeria de imagens
.gallery-container {
	.image-carousel {
		position: relative;
		overflow: hidden;
		border-radius: 12px;
		box-shadow: 0 4px 20px $shadow;

		.carousel-wrapper {
			width: 100%;
			height: 500px;
			overflow: hidden;
		}

		.carousel-track {
			display: flex;
			transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
			height: 100%;
		}

		.carousel-slide {
			min-width: 100%;
			height: 100%;
			position: relative;
		}

		.image-container {
			width: 100%;
			height: 100%;
			position: relative;
			overflow: hidden;

			.carousel-image {
				width: 100%;
				height: 100%;
				object-fit: cover;
				transition: transform 0.3s ease;
			}

			.image-overlay {
				position: absolute;
				bottom: 0;
				left: 0;
				right: 0;
				background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
				color: $white;
				padding: 2rem;
				transform: translateY(100%);
				transition: transform 0.3s ease;

				h3 {
					font-size: 1.5rem;
					font-weight: 600;
					margin: 0 0 0.5rem 0;
				}

				p {
					margin: 0;
					opacity: 0.9;
				}
			}

			&:hover .image-overlay {
				transform: translateY(0);
			}

			&:hover .carousel-image {
				transform: scale(1.05);
			}
		}

		.carousel-controls {
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
			width: 100%;
			display: flex;
			justify-content: space-between;
			padding: 0 1rem;
			pointer-events: none;

			.carousel-btn {
				width: 50px;
				height: 50px;
				border-radius: 50%;
				background: rgba($white, 0.9);
				border: none;
				color: $primary-color;
				font-size: 1.2rem;
				cursor: pointer;
				transition: all 0.3s ease;
				pointer-events: all;
				box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);

				&:hover:not(:disabled) {
					background: $white;
					transform: scale(1.1);
				}

				&:disabled {
					opacity: 0.5;
					cursor: not-allowed;
				}
			}
		}

		.carousel-indicators {
			position: absolute;
			bottom: 1rem;
			left: 50%;
			transform: translateX(-50%);
			display: flex;
			gap: 0.5rem;

			.indicator {
				width: 12px;
				height: 12px;
				border-radius: 50%;
				border: none;
				background: rgba($white, 0.5);
				cursor: pointer;
				transition: all 0.3s ease;

				&.active {
					background: $white;
					transform: scale(1.2);
				}

				&:hover {
					background: rgba($white, 0.8);
				}
			}
		}
	}
}

// Card de recursos
.resources-card {
	.resources-content {
		.download-section {
			margin-bottom: 2rem;

			.download-item {
				display: flex;
				align-items: center;
				gap: 1.5rem;
				padding: 1.5rem;
				background: rgba($primary-color, 0.05);
				border-radius: 12px;
				border: 1px solid rgba($primary-color, 0.1);

				.download-icon {
					width: 60px;
					height: 60px;
					background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
					border-radius: 12px;
					display: flex;
					align-items: center;
					justify-content: center;
					color: $white;
					font-size: 1.5rem;
				}

				.download-info {
					flex: 1;

					h3 {
						font-size: 1.25rem;
						font-weight: 600;
						color: $text-color;
						margin: 0 0 0.5rem 0;
					}

					p {
						color: $text-light;
						margin: 0;
					}
				}

				.download-btn {
					background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
					color: $white;
					border: none;
					padding: 0.75rem 1.5rem;
					border-radius: 8px;
					font-weight: 600;
					cursor: pointer;
					transition: all 0.3s ease;
					display: flex;
					align-items: center;
					gap: 0.5rem;
					box-shadow: 0 4px 15px rgba($primary-color, 0.3);

					&:hover {
						transform: translateY(-2px);
						box-shadow: 0 6px 20px rgba($primary-color, 0.4);
					}
				}
			}
		}

		.references-section {
			text-align: center;

			.references-btn {
				background: transparent;
				color: $primary-color;
				border: 2px solid $primary-color;
				padding: 0.75rem 1.5rem;
				border-radius: 8px;
				font-weight: 500;
				cursor: pointer;
				transition: all 0.3s ease;
				display: inline-flex;
				align-items: center;
				gap: 0.5rem;

				&:hover {
					background: $primary-color;
					color: $white;
					transform: translateY(-2px);
				}
			}
		}
	}
}

// Responsividade
@media (max-width: 768px) {
	.pruning-alert-container {
		padding: 0.5rem;
	}

	.page-header {
		padding: 1.5rem;
		margin-bottom: 1.5rem;

		.header-content {
			flex-direction: column;
			text-align: center;
			gap: 1rem;
		}

		.header-icon {
			width: 60px;
			height: 60px;
			font-size: 1.8rem;
		}

		.header-text h1 {
			font-size: 2rem;
		}

		.header-text p {
			font-size: 1rem;
		}
	}

	.gallery-card, .resources-card {
		padding: 1.5rem;

		.card-header {
			flex-direction: column;
			text-align: center;
			gap: 1rem;
		}
	}

	.gallery-container .image-carousel {
		.carousel-wrapper {
			height: 400px;
		}

		.carousel-controls .carousel-btn {
			width: 40px;
			height: 40px;
			font-size: 1rem;
		}

		.image-overlay {
			padding: 1rem;

			h3 {
				font-size: 1.25rem;
			}
		}
	}

	.resources-content .download-section .download-item {
		flex-direction: column;
		text-align: center;
		gap: 1rem;
	}
}

@media (max-width: 480px) {
	.page-header .header-text h1 {
		font-size: 1.75rem;
	}

	.gallery-container .image-carousel .carousel-wrapper {
		height: 300px;
	}
}
</style>
