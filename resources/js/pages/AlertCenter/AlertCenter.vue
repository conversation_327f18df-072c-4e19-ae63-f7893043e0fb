<template>
	<div class="alert-center-wrapper">
		<div class="container-fluid">
			<!-- Header Section -->
			<div class="page-header">
				<div class="header-content">
					<h1 class="page-title">Central de Alertas</h1>
					<p class="page-subtitle">
						Monitore a saúde das suas propriedades e acesse materiais educativos para melhor manejo
					</p>
				</div>
			</div>
		</div>

		<!-- Navigation Tabs -->
		<div class="nav-tabs-container">
			<div class="container-fluid">
				<ul class="nav nav-tabs modern-tabs" role="tablist">
					<li class="nav-item">
						<a
							class="nav-link"
							:class="{ active: activeTab === 'alerts' }"
							@click="setActiveTab('alerts')"
							role="tab"
						>
							<i class="fas fa-exclamation-triangle"></i>
							<span>Alertas das Propriedades</span>
						</a>
					</li>
					<li class="nav-item">
						<a
							class="nav-link"
							:class="{ active: activeTab === 'materials' }"
							@click="setActiveTab('materials')"
							role="tab"
						>
							<i class="fas fa-book-open"></i>
							<span>Materiais de Apoio</span>
						</a>
					</li>
				</ul>
			</div>
		</div>

		<div class="container-fluid">
			<!-- Tab Content -->
			<div class="tab-content">
				<!-- Alerts Tab -->
				<div
					class="tab-pane fade"
					:class="{ 'show active': activeTab === 'alerts' }"
					role="tabpanel"
				>

					<!-- Estatísticas Simplificadas -->
					<div class="simple-stats-grid">
						<div class="simple-stats-card warning">
							<div class="simple-stats-icon">
								<i class="fas fa-cut"></i>
							</div>
							<div class="simple-stats-content">
								<h3>{{ podaStats.maintenance }}</h3>
								<p>Precisam de Poda</p>
							</div>
						</div>

						<div class="simple-stats-card success">
							<div class="simple-stats-icon">
								<i class="fas fa-check-circle"></i>
							</div>
							<div class="simple-stats-content">
								<h3>{{ podaStats.ok }}</h3>
								<p>Poda em Dia</p>
							</div>
						</div>

						<div class="simple-stats-card danger">
							<div class="simple-stats-icon">
								<i class="fas fa-broom"></i>
							</div>
							<div class="simple-stats-content">
								<h3>{{ vassouraStats.critical }}</h3>
								<p>Vassoura Crítica</p>
							</div>
						</div>

						<div class="simple-stats-card info">
							<div class="simple-stats-icon">
								<i class="fas fa-shield-alt"></i>
							</div>
							<div class="simple-stats-content">
								<h3>{{ vassouraStats.ok }}</h3>
								<p>Vassoura Controlada</p>
							</div>
						</div>
					</div>

						<!-- Filtros Simples -->
						<div class="simple-filters">
							<div class="simple-filters-row">
								<div class="simple-filter-item">
									<label>Poda:</label>
									<select v-model="filters.poda" class="simple-select">
										<option value="">Todos</option>
										<option value="maintenance">Precisam de Manutenção</option>
										<option value="ok">Em Dia</option>
										<option value="never">Nunca Podadas</option>
									</select>
								</div>

								<div class="simple-filter-item">
									<label>Vassoura:</label>
									<select v-model="filters.vassoura" class="simple-select">
										<option value="">Todos</option>
										<option value="critical">Crítico</option>
										<option value="high">Alto</option>
										<option value="medium">Médio</option>
										<option value="low">Baixo</option>
									</select>
								</div>

								<div class="simple-filter-item search-item">
									<label>Buscar:</label>
									<input
										type="text"
										v-model="filters.search"
										class="simple-search"
										placeholder="Nome da propriedade..."
									>
								</div>
							</div>
						</div>

			<!-- Loading -->
			<div v-if="loading" class="text-center py-5">
				<div class="spinner-border text-primary" role="status">
					<span class="sr-only">Carregando...</span>
				</div>
				<p class="mt-2">Carregando alertas das propriedades...</p>
			</div>

			<!-- Lista de Alertas -->
			<div v-else class="row">
				<div
					v-for="property in filteredProperties"
					:key="property.id"
					class="col-lg-6 col-xl-4 mb-4"
				>
					<div class="alert-card">
						<!-- Header do Card -->
						<div class="alert-card-header">
							<h5 class="property-name">{{ property.name }}</h5>
							<div class="property-info">
								<small class="text-muted">
									<i class="fas fa-map-marker-alt"></i>
									{{ property.city }}, {{ property.uf }}
								</small>
							</div>
						</div>

						<!-- Alerta de Poda -->
						<div class="alert-section poda-section">
							<div class="alert-section-header">
								<i class="fas fa-cut"></i>
								<span>Alerta de Poda</span>
							</div>
							<div class="alert-content">
								<div
									class="poda-alert"
									:class="getPodaAlertClass(property.pruning_alert)"
								>
									<div class="alert-status">
										<i :class="getPodaIcon(property.pruning_alert)"></i>
										<span>{{ getPodaMessage(property.pruning_alert) }}</span>
									</div>
									<div v-if="property.pruning_alert && property.pruning_alert.lastDate" class="alert-details">
										<small>
											Última poda: {{ property.pruning_alert.lastDate }}
											<span v-if="property.pruning_alert.monthsAgo">
												({{ property.pruning_alert.monthsAgo }} meses atrás)
											</span>
										</small>
									</div>
								</div>
							</div>
						</div>

						<!-- Alerta de Vassoura de Bruxa -->
						<div class="alert-section vassoura-section">
							<div class="alert-section-header">
								<i class="fas fa-broom"></i>
								<span>Vassoura de Bruxa</span>
							</div>
							<div class="alert-content">
								<div class="vassoura-alert">
									<div class="vassoura-progress-container">
										<div class="vassoura-progress-bar">
											<div
												class="vassoura-progress-fill"
												:style="getVassouraProgressStyle(property.witchs_broom_alert)"
											></div>
										</div>
										<div class="vassoura-progress-text">
											{{ getVassouraPercentage(property.witchs_broom_alert) }}%
										</div>
									</div>
									<div class="vassoura-status">
										<span
											class="vassoura-status-badge"
											:class="getVassouraStatusClass(property.witchs_broom_alert)"
										>
											{{ getVassouraStatusText(property.witchs_broom_alert) }}
										</span>
									</div>
									<div v-if="property.witchs_broom_alert && property.witchs_broom_alert.totalFruits" class="vassoura-details">
										<small>
											{{ property.witchs_broom_alert.witchsBroomFruits }} de {{ property.witchs_broom_alert.totalFruits }} frutos infectados
										</small>
									</div>
								</div>
							</div>
						</div>

						<!-- Ações -->
						<div class="alert-card-actions">
							<button
								class="btn btn-sm btn-outline-primary"
								@click="viewProperty(property.id)"
							>
								<i class="fas fa-eye"></i>
								Ver Propriedade
							</button>
						</div>
					</div>
				</div>
			</div>

					<!-- Mensagem quando não há dados -->
					<div v-if="!loading && filteredProperties.length === 0" class="text-center py-5">
						<div class="no-data-message">
							<i class="fas fa-search text-muted"></i>
							<h4 class="mt-3">Nenhuma propriedade encontrada</h4>
							<p class="text-muted">
								Tente ajustar os filtros ou verifique se há propriedades cadastradas.
							</p>
						</div>
					</div>
				</div>

				<!-- Materials Tab -->
				<div
					class="tab-pane fade"
					:class="{ 'show active': activeTab === 'materials' }"
					role="tabpanel"
				>
					<div class="materials-section">
						<div class="materials-header">
							<h2>
								<i class="fas fa-seedling"></i>
								Materiais Educativos
							</h2>
							<p>Acesse guias completos e materiais de apoio para melhor manejo das suas propriedades</p>
						</div>

						<div class="simple-materials-grid">
							<!-- Card Poda -->
							<div class="simple-material-card">
								<div class="simple-material-icon">
									<i class="fas fa-cut"></i>
								</div>
								<div class="simple-material-content">
									<h3>Guia de Poda</h3>
									<p>Aprenda técnicas corretas de poda para maximizar a produtividade do cacau</p>
									<button class="btn-simple" @click="viewFullMaterial('poda')">
										<i class="fas fa-book-open"></i>
										Ver Guia Completo
									</button>
								</div>
							</div>

							<!-- Card Vassoura de Bruxa -->
							<div class="simple-material-card">
								<div class="simple-material-icon">
									<i class="fas fa-broom"></i>
								</div>
								<div class="simple-material-content">
									<h3>Controle de Vassoura de Bruxa</h3>
									<p>Estratégias eficazes para prevenir e controlar esta doença do cacaueiro</p>
									<button class="btn-simple" @click="viewFullMaterial('vassoura')">
										<i class="fas fa-book-open"></i>
										Ver Guia Completo
									</button>
								</div>
							</div>
						</div>


					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script src="./AlertCenter.js"></script>

<style scoped>
/* Layout Principal */
.alert-center-wrapper {
	background: #f8f9fa;
	min-height: 100vh;
	padding: 20px 0;
}

/* Header Section */
.page-header {
	background: white;
	border-radius: 15px;
	padding: 40px;
	margin-bottom: 30px;
	box-shadow: 0 4px 15px rgba(0,0,0,0.1);
	text-align: center;
}

.page-title {
	font-size: 2.5rem;
	font-weight: 700;
	color: #2c3e50;
	margin-bottom: 15px;
}

.page-subtitle {
	font-size: 1.2rem;
	color: #6c757d;
	line-height: 1.6;
	max-width: 600px;
	margin: 0 auto;
}

/* Navigation Tabs */
.nav-tabs-container {
	background: white;
	box-shadow: 0 2px 10px rgba(0,0,0,0.1);
	position: sticky;
	top: 0;
	z-index: 100;
}

.modern-tabs {
	border: none;
	margin: 0;
	padding: 0 20px;
}

.modern-tabs .nav-item {
	margin-right: 2rem;
}

.modern-tabs .nav-link {
	border: none;
	background: none;
	color: #6c757d;
	font-weight: 600;
	padding: 20px 0;
	border-bottom: 3px solid transparent;
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	gap: 10px;
}

.modern-tabs .nav-link:hover {
	color: rgb(61, 129, 96);
	border-bottom-color: rgba(61, 129, 96, 0.3);
}

.modern-tabs .nav-link.active {
	color: rgb(61, 129, 96);
	border-bottom-color: rgb(61, 129, 96);
}

.modern-tabs .nav-link i {
	font-size: 1.1rem;
}

/* Tab Content */
.tab-content {
	background: #f8f9fa;
	min-height: calc(100vh - 200px);
	padding: 40px 0;
}

/* Estatísticas Simplificadas */
.simple-stats-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
	gap: 20px;
	margin-bottom: 30px;
}

.simple-stats-card {
	background: white;
	border-radius: 12px;
	padding: 25px;
	box-shadow: 0 2px 10px rgba(0,0,0,0.1);
	display: flex;
	align-items: center;
	gap: 20px;
	transition: all 0.3s ease;
}

.simple-stats-card:hover {
	transform: translateY(-2px);
	box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.simple-stats-icon {
	width: 50px;
	height: 50px;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 1.5rem;
	color: white;
}

.simple-stats-card.warning .simple-stats-icon {
	background: #ffc107;
}

.simple-stats-card.success .simple-stats-icon {
	background: #28a745;
}

.simple-stats-card.danger .simple-stats-icon {
	background: #dc3545;
}

.simple-stats-card.info .simple-stats-icon {
	background: rgb(61, 129, 96);
}

.simple-stats-content h3 {
	font-size: 2rem;
	font-weight: 700;
	color: #2c3e50;
	margin: 0 0 5px 0;
}

.simple-stats-content p {
	font-size: 0.9rem;
	color: #6c757d;
	margin: 0;
}

/* Filtros Simplificados */
.simple-filters {
	background: white;
	border-radius: 12px;
	padding: 20px;
	margin-bottom: 30px;
	box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.simple-filters-row {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
	gap: 20px;
	align-items: end;
}

.simple-filter-item {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.simple-filter-item label {
	font-weight: 600;
	color: #495057;
	font-size: 0.9rem;
}

.simple-select, .simple-search {
	padding: 10px 12px;
	border: 1px solid #ddd;
	border-radius: 8px;
	background: white;
	font-size: 0.9rem;
	transition: all 0.3s ease;
}

.simple-select:focus, .simple-search:focus {
	outline: none;
	border-color: rgb(61, 129, 96);
	box-shadow: 0 0 0 2px rgba(61, 129, 96, 0.1);
}

/* Cards de Alerta */
.alert-card {
	background: white;
	border-radius: 12px;
	box-shadow: 0 4px 15px rgba(0,0,0,0.1);
	overflow: hidden;
	transition: transform 0.2s, box-shadow 0.2s;
}

.alert-card:hover {
	transform: translateY(-3px);
	box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.alert-card-header {
	padding: 20px 20px 10px;
	border-bottom: 1px solid #e9ecef;
}

.property-name {
	font-weight: 600;
	color: #2c3e50;
	margin-bottom: 5px;
}

.property-info {
	color: #6c757d;
}

.alert-section {
	padding: 15px 20px;
}

.alert-section:not(:last-child) {
	border-bottom: 1px solid #f1f3f4;
}

.alert-section-header {
	display: flex;
	align-items: center;
	margin-bottom: 10px;
	font-weight: 600;
	color: #495057;
}

.alert-section-header i {
	margin-right: 8px;
	width: 16px;
}

/* Alertas de Poda */
.poda-alert {
	padding: 12px;
	border-radius: 8px;
	border-left: 4px solid;
}

.poda-alert.maintenance {
	background-color: #fff3cd;
	border-left-color: #ffc107;
	color: #856404;
}

.poda-alert.ok {
	background-color: #d4edda;
	border-left-color: #28a745;
	color: #155724;
}

.poda-alert.never {
	background-color: #f8d7da;
	border-left-color: #dc3545;
	color: #721c24;
}

.poda-alert.error {
	background-color: #f8f9fa;
	border-left-color: #6c757d;
	color: #495057;
}

.alert-status {
	display: flex;
	align-items: center;
	font-weight: 600;
	margin-bottom: 5px;
}

.alert-status i {
	margin-right: 8px;
}

.alert-details {
	margin-top: 5px;
}

/* Alertas de Vassoura de Bruxa */
.vassoura-progress-container {
	display: flex;
	align-items: center;
	margin-bottom: 15px;
	gap: 12px;
}

.vassoura-progress-bar {
	flex: 1;
	height: 16px;
	background-color: #e9ecef;
	border-radius: 8px;
	overflow: hidden;
	border: 1px solid #dee2e6;
}

.vassoura-progress-fill {
	height: 100%;
	border-radius: 8px;
	transition: all 0.4s ease;
	position: relative;
}

.vassoura-progress-text {
	font-weight: 700;
	color: #2c3e50;
	min-width: 50px;
	text-align: center;
	font-size: 0.9rem;
}

.vassoura-status {
	margin-bottom: 8px;
}

.vassoura-status-badge {
	padding: 4px 10px;
	border-radius: 12px;
	font-size: 0.75rem;
	font-weight: 600;
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

.vassoura-status-badge.low {
	background-color: #d4edda;
	color: #155724;
	border: 1px solid #c3e6cb;
}

.vassoura-status-badge.medium {
	background-color: #fff3cd;
	color: #856404;
	border: 1px solid #ffeaa7;
}

.vassoura-status-badge.high {
	background-color: #f8d7da;
	color: #721c24;
	border: 1px solid #f5c6cb;
}

.vassoura-status-badge.critical {
	background-color: #343a40;
	color: white;
	border: 1px solid #454d55;
}

.vassoura-details {
	margin-top: 8px;
}

.vassoura-details small {
	color: #6c757d;
	font-size: 0.8rem;
}

/* Ações */
.alert-card-actions {
	padding: 15px 20px;
	background-color: #f8f9fa;
	display: flex;
	gap: 10px;
}

.alert-card-actions .btn {
	flex: 1;
}

/* Mensagem sem dados */
.no-data-message {
	color: #6c757d;
}

.no-data-message i {
	font-size: 3rem;
}

/* Materiais de Apoio */
.materials-section {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 20px;
}

.materials-header {
	text-align: center;
	margin-bottom: 50px;
}

.materials-header h2 {
	color: #2c3e50;
	font-weight: 700;
	font-size: 2.5rem;
	margin-bottom: 15px;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15px;
}

.materials-header p {
	color: #6c757d;
	font-size: 1.2rem;
	max-width: 600px;
	margin: 0 auto;
}

.simple-materials-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
	gap: 25px;
	margin-bottom: 40px;
}

.simple-material-card {
	background: white;
	border-radius: 15px;
	padding: 30px;
	box-shadow: 0 4px 15px rgba(0,0,0,0.1);
	display: flex;
	align-items: center;
	gap: 25px;
	transition: all 0.3s ease;
}

.simple-material-card:hover {
	transform: translateY(-3px);
	box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.simple-material-icon {
	width: 60px;
	height: 60px;
	border-radius: 50%;
	background: rgb(61, 129, 96);
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 1.8rem;
	color: white;
	flex-shrink: 0;
}

.simple-material-content {
	flex: 1;
}

.simple-material-content h3 {
	color: #2c3e50;
	font-weight: 600;
	font-size: 1.2rem;
	margin-bottom: 10px;
}

.simple-material-content p {
	color: #6c757d;
	margin-bottom: 15px;
	font-size: 0.9rem;
	line-height: 1.4;
}

.btn-simple {
	background: rgb(61, 129, 96);
	color: white;
	border: none;
	padding: 10px 16px;
	border-radius: 8px;
	font-size: 0.9rem;
	font-weight: 600;
	display: flex;
	align-items: center;
	gap: 8px;
	cursor: pointer;
	transition: all 0.3s ease;
}

.btn-simple:hover {
	background: #4a7c59;
	transform: translateY(-1px);
}



/* Responsividade */
@media (max-width: 768px) {
	.alert-center-wrapper {
		padding: 10px 0;
	}

	.page-header {
		padding: 30px 20px;
		margin-bottom: 20px;
	}

	.page-title {
		font-size: 2rem;
	}

	.page-subtitle {
		font-size: 1.1rem;
	}

	.simple-stats-grid {
		grid-template-columns: 1fr;
		gap: 15px;
	}

	.simple-filters-row {
		grid-template-columns: 1fr;
		gap: 15px;
	}

	.simple-materials-grid {
		grid-template-columns: 1fr;
		gap: 20px;
	}

	.simple-material-card {
		flex-direction: column;
		text-align: center;
		gap: 20px;
	}

	.alert-card-actions {
		flex-direction: column;
	}

	.alert-card-actions .btn {
		margin-bottom: 5px;
	}

	.modern-tabs {
		padding: 0 10px;
	}

	.modern-tabs .nav-item {
		margin-right: 1rem;
	}

	.modern-tabs .nav-link span {
		display: none;
	}
}
</style>
