import axios from "axios";

export default {
	name: "AlertCenter",
	data() {
		return {
			authToken: null,
			loading: false,
			activeTab: 'alerts',
			properties: [],
			filters: {
				poda: '',
				vassoura: '',
				search: ''
			},
			podaStats: {
				maintenance: 0,
				ok: 0,
				never: 0,
				total: 0
			},
			vassouraStats: {
				critical: 0,
				high: 0,
				medium: 0,
				low: 0,
				ok: 0
			},

		};
	},
	computed: {
		filteredProperties() {
			let filtered = this.properties;

			// Filtro por poda
			if (this.filters.poda) {
				filtered = filtered.filter(property => {
					if (!property.pruning_alert) return false;
					return property.pruning_alert.status === this.filters.poda;
				});
			}

			// Filtro por vassoura
			if (this.filters.vassoura) {
				filtered = filtered.filter(property => {
					if (!property.witchs_broom_alert) return false;
					return property.witchs_broom_alert.status === this.filters.vassoura;
				});
			}

			// Filtro por busca
			if (this.filters.search) {
				const searchTerm = this.filters.search.toLowerCase();
				filtered = filtered.filter(property =>
					property.name.toLowerCase().includes(searchTerm) ||
					property.city.toLowerCase().includes(searchTerm) ||
					property.uf.toLowerCase().includes(searchTerm)
				);
			}

			return filtered;
		},
		hasActiveFilters() {
			return this.filters.poda || this.filters.vassoura || this.filters.search;
		}
	},

	created() {
			this.authToken = window.token;
	},

	async mounted() {
		await this.loadAlerts();
	},
	methods: {
		async loadAlerts() {
			this.loading = true;
			try {
				// Buscar propriedades
				const response = await axios.get("/api/v1/properties", {
					headers: { authorization: `bearer ${this.authToken}` },
				});

				if (response.status === 200) {
					this.properties = response.data.data;
					// Carregar alertas para cada propriedade
					await this.loadPropertiesAlerts();
					// Calcular estatísticas
					this.calculateStats();
				}
			} catch (err) {
				console.error('Erro ao carregar alertas:', err);
			} finally {
				this.loading = false;
			}
		},

		async loadPropertiesAlerts() {
			const alertPromises = this.properties.map(async (property) => {
				// Buscar dados de poda e vassoura em paralelo
				const [pruningAlert, witchsBroomAlert] = await Promise.all([
					this.getPruningAlert(property.id),
					this.getWitchsBroomAlert(property.id)
				]);

				property.pruning_alert = pruningAlert;
				property.witchs_broom_alert = witchsBroomAlert;
			});

			await Promise.all(alertPromises);
		},

		async getPruningAlert(propertyId) {
			try {
				const response = await axios.get(`/api/v1/properties/${propertyId}/last-pruning`, {
					headers: { authorization: `bearer ${this.authToken}` },
				});

				if (response.data && response.data.last_pruning_date) {
					const lastPruningDate = new Date(response.data.last_pruning_date);
					const currentDate = new Date();
					const monthsDiff = (currentDate.getFullYear() - lastPruningDate.getFullYear()) * 12 +
									  (currentDate.getMonth() - lastPruningDate.getMonth());

					if (monthsDiff > 6) {
						return {
							status: 'maintenance',
							message: 'Poda de Manutenção',
							lastDate: this.formatDate(lastPruningDate),
							monthsAgo: monthsDiff
						};
					} else {
						return {
							status: 'ok',
							message: 'Em dia',
							lastDate: this.formatDate(lastPruningDate),
							monthsAgo: monthsDiff
						};
					}
				} else {
					return {
						status: 'never',
						message: 'Nunca podada',
						lastDate: null,
						monthsAgo: null
					};
				}
			} catch (err) {
				console.log('Erro ao buscar dados de poda:', err);
				return {
					status: 'error',
					message: 'Erro ao carregar',
					lastDate: null,
					monthsAgo: null
				};
			}
		},

		async getWitchsBroomAlert(propertyId) {
			try {
				const start = performance.now(); // ⏱ Início da medição

				const response = await axios.get(`/api/v1/properties/${propertyId}/witchs-broom-data`, {
					headers: { authorization: `bearer ${this.authToken}` },
				});

				const end = performance.now(); // ⏱ Fim da medição
				const duration = (end - start).toFixed(2); // Tempo em milissegundos

				console.log(`Tempo da requisição: ${duration}ms`);

				if (response.data && response.data.total_fruits && response.data.witchs_broom_fruits) {
					const totalFruits = response.data.total_fruits;
					const witchsBroomFruits = response.data.witchs_broom_fruits;
					const percentage = totalFruits > 0 ? (witchsBroomFruits / totalFruits) * 100 : 0;

					let status = 'low';
					let color = '#4caf50'; // Verde

					if (percentage > 20) {
						status = 'critical';
						color = '#000000'; // Preto
					} else if (percentage > 10) {
						status = 'high';
						color = '#f44336'; // Vermelho
					} else if (percentage > 1) {
						status = 'medium';
						color = '#ff9800'; // Amarelo
					}

					return {
						percentage: percentage.toFixed(1),
						status,
						color,
						totalFruits,
						witchsBroomFruits,
						duration
					};
				} else {
					return {
						percentage: 0,
						status: 'no_data',
						color: '#9e9e9e',
						totalFruits: 0,
						witchsBroomFruits: 0,
						duration
					};
				}
			} catch (err) {
				console.log('Erro ao buscar dados de vassoura de bruxa:', err);
				return {
					percentage: 0,
					status: 'error',
					color: '#9e9e9e',
					totalFruits: 0,
					witchsBroomFruits: 0,
					duration: 0
				};
			}
		},

		calculateStats() {
			// Reset stats
			this.podaStats = { maintenance: 0, ok: 0, never: 0, total: 0 };
			this.vassouraStats = { critical: 0, high: 0, medium: 0, low: 0, ok: 0 };

			this.properties.forEach(property => {
				// Estatísticas de poda
				if (property.pruning_alert) {
					// Contar por status individual
					if (property.pruning_alert.status === 'maintenance') {
						this.podaStats.maintenance++;
					} else if (property.pruning_alert.status === 'never') {
						this.podaStats.never++;
						// Propriedades nunca podadas também precisam de poda
						this.podaStats.maintenance++;
					} else if (property.pruning_alert.status === 'ok') {
						this.podaStats.ok++;
					}
					this.podaStats.total++;
				}

				// Estatísticas de vassoura
				if (property.witchs_broom_alert) {
					this.vassouraStats[property.witchs_broom_alert.status]++;

					// Calcular "ok" (baixo + sem dados)
					if (property.witchs_broom_alert.status === 'low' ||
						property.witchs_broom_alert.status === 'no_data') {
						this.vassouraStats.ok++;
					}
				}
			});
		},

		formatDate(date) {
			if (!date) return "";
			const d = new Date(date);
			const day = String(d.getDate()).padStart(2, '0');
			const month = String(d.getMonth() + 1).padStart(2, '0');
			const year = d.getFullYear();
			return `${day}/${month}/${year}`;
		},

		// Métodos para renderização de poda
		getPodaAlertClass(alert) {
			if (!alert) return 'error';
			return alert.status;
		},

		getPodaIcon(alert) {
			if (!alert) return 'fas fa-question-circle';

			switch (alert.status) {
				case 'maintenance':
					return 'fas fa-exclamation-triangle';
				case 'ok':
					return 'fas fa-check-circle';
				case 'never':
					return 'fas fa-times-circle';
				default:
					return 'fas fa-question-circle';
			}
		},

		getPodaMessage(alert) {
			if (!alert) return 'Erro ao carregar';
			return alert.message;
		},

		// Métodos para renderização de vassoura
		getVassouraProgressStyle(alert) {
			if (!alert) return { width: '0%', backgroundColor: '#9e9e9e' };

			const percentage = Math.min(parseFloat(alert.percentage), 100);
			let backgroundColor = '#4caf50'; // Verde (padrão para 0-1%)

			// Definir cor baseada na porcentagem
			if (percentage > 20) {
				backgroundColor = '#000000'; // Preto (>20%)
			} else if (percentage > 10) {
				backgroundColor = '#f44336'; // Vermelho (11-20%)
			} else if (percentage > 1) {
				backgroundColor = '#ff9800'; // Amarelo (2-10%)
			}

			return {
				width: `${percentage}%`,
				backgroundColor: backgroundColor
			};
		},

		getVassouraPercentage(alert) {
			if (!alert) return '0.0';
			return alert.percentage || '0.0';
		},

		getVassouraStatusClass(alert) {
			if (!alert) return 'low';

			const percentage = parseFloat(alert.percentage || 0);
			if (percentage > 20) return 'critical';
			if (percentage > 10) return 'high';
			if (percentage > 1) return 'medium';
			return 'low';
		},

		getVassouraStatusText(alert) {
			if (!alert || alert.status === 'no_data') return 'Sem dados';

			const percentage = parseFloat(alert.percentage || 0);
			if (percentage > 20) return 'Crítico';
			if (percentage > 10) return 'Alto';
			if (percentage > 1) return 'Médio';
			return 'Baixo';
		},

		// Controle de Guias
		setActiveTab(tab) {
			this.activeTab = tab;
		},

		// Controle de Filtros
		getFilterLabel(type, value) {
			const labels = {
				poda: {
					maintenance: 'Precisam de Manutenção',
					ok: 'Em Dia',
					never: 'Nunca Podadas'
				},
				vassoura: {
					critical: 'Crítico',
					high: 'Alto',
					medium: 'Médio',
					low: 'Baixo'
				}
			};
			return labels[type][value] || value;
		},

		clearAllFilters() {
			this.filters.poda = '';
			this.filters.vassoura = '';
			this.filters.search = '';
		},





		viewFullMaterial(type) {
			// Navegar para página completa do material
			const routes = {
				poda: '/panel/pruning',
				vassoura: '/panel/witch-broom'
			};

			if (routes[type]) {
				this.$router.push(routes[type]);
			}
		},

		// Visualizar propriedade
		viewProperty(propertyId) {
			this.$router.push(`/panel/property/${propertyId}`);
		},

		// Ações
		viewProperty(propertyId) {
			this.$router.push({
				path: '/panel/homogeneous-area',
				query: { propid: propertyId }
			});
		}
	}
};
