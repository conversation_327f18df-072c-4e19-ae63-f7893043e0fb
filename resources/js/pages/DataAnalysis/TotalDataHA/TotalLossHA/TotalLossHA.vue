<template>
	<div id="totalLoss" class="mt-5 totalLossWrapper">
		<div class="admin-content">
			<div class="admin-header d-flex justify-content-between align-items-center p-2">
				<div class="d-flex align-items-center">
					<back-button
								title="Voltar para selecionar análise"
					></back-button>
					<div class="d-flex flex-column">
						<h1>Perda de Frutos por Causa Desconhecida por Safra</h1>
						<p>
							Selecione os itens abaixo para visualizar por Safra, em
							um gráfico de linhas, a quantidade de frutos perdidos por Área Homogênea
						</p>
					</div>
				</div>
			</div>
			<div class="graph-control">
				<form @submit.prevent="getTotalLossForPeriod" class="graph-form">
					<div class="row top-row">
						<div class="form-group">
							<label for="propriedade">Propriedade</label>
							<select id="propriedade" v-model="selectedOptions.propriedade">
								<option v-for="(property, index) in properties" :key="property.id" :value="property.id" :selected="index === 0">{{ property.name }}</option>
							</select>
						</div>
						<div class="form-group">
							<label for="areaHomogenea">Área Homogênea</label>
							<select id="areaHomogenea" v-model="selectedOptions.areaHomogenea">
								<option v-for="(area) in homogeneousAreas" :key="area.id" :value="area.id">{{ area.label }}</option>
							</select>
						</div>
					</div>
				</form>
			</div>
			<!-- Seção para exibir os dados recebidos -->
			<div v-if="collectionData.length > 0 && !loading" class="chart-wrapper">
				<template>
				<div class="chart-container">
					<LineChart :key="chartKey" :chart-data="chartData" :chart-options="chartOptions" />
				</div>
				</template>
			</div>
			<div v-else-if="loading" class="chart-wrapper">
				<!-- Exibe a animação de loading, deve estar no centro da tela e ter um bom tamanho-->
				<span
						class="spinner-border spinner-border-sm"
						role="status"
						aria-hidden="true"
						style="width: 3rem; height: 3rem; position: absolute; top: 50%; left: 50%;"

				></span>
			</div>
		</div>
	</div>
</template>

<script src="./TotalLossHA.js"></script>

<style lang="scss">
.totalLossWrapper {
	background-color: #f5f8fd;
	border-radius: 20px;

	.admin-content {
	height: 90vh;
	}

	h1 {
		color: #3d8160;
		font-family: "Lexend", sans-serif;
		font-weight: 600;
		font-size: 24px;
		padding: 16px 0px 0px 16px;
		text-align: left;
	}

	p {
		font-family: "Lexend", sans-serif;
		font-size: 18px;
		padding: 0px 0px 0px 16px;
		text-align: left;
	}

	.btn {
		height: auto;
		width: auto;
	}
}

.graph-control {
	padding: 16px;
	display: flex;
	justify-content: center;
	align-items: center;

	.graph-form {
		display: flex;
		flex-direction: column;
		align-items: center;

		.top-row {
			display: flex;
			gap: 20px;
			margin-bottom: 10px;
			justify-content: center;
		}

		.faixas, .coletas {
			display: flex;
			flex-direction: column;
			gap: 10px;
			align-items: center;
		}

		.form-group {
			display: flex;
			align-items: center;
			gap: 10px;
		}

		label {
			white-space: nowrap;
		}

		select {
			padding: 6px;
			border: 1px solid #ccc;
			border-radius: 4px;
			min-width: 5rem;
		}
	}
}

.chart-wrapper {
	margin: 1rem 3rem;
}

canvas {
	width: 90% !important;
	height: 90% !important;
}
</style>
