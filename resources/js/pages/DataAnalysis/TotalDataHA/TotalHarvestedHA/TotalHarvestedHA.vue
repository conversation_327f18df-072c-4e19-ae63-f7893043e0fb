<template>
	<div class="total-harvested-ha-container">
		<!-- Header Principal -->
		<div class="page-header">
			<div class="header-content">
				<back-button title="Voltar para selecionar análise"></back-button>
				<div class="header-icon">
					<i class="fas fa-chart-line"></i>
				</div>
				<div class="header-text">
					<h1>Frutos Colhidos por Safra</h1>
					<p>Visualize por safra a quantidade de frutos colhidos por área homogênea através de gráfico de linhas</p>
				</div>
			</div>
		</div>

		<!-- Filtros -->
		<div class="filters-section">
			<div class="filters-card">
				<h3>Filtros de Análise</h3>
				<form @submit.prevent="getTotalHarvestedForPeriod" class="filters-form">
					<div class="filters-grid">
						<div class="filter-group">
							<label for="propriedade">
								<i class="fas fa-map-marker-alt"></i>
								Propriedade
							</label>
							<select id="propriedade" v-model="selectedOptions.propriedade" class="filter-select">
								<option value="" disabled>Selecione uma propriedade</option>
								<option v-for="property in properties" :key="property.id" :value="property.id">
									{{ property.name }}
								</option>
							</select>
							<div v-if="properties.length === 0" class="empty-message">
								<i class="fas fa-exclamation-circle"></i>
								Nenhuma propriedade encontrada
							</div>
						</div>

						<div class="filter-group">
							<label for="areaHomogenea">
								<i class="fas fa-layer-group"></i>
								Área Homogênea
							</label>
							<select id="areaHomogenea" v-model="selectedOptions.areaHomogenea" class="filter-select" :disabled="!selectedOptions.propriedade">
								<option value="" disabled>Selecione uma área homogênea</option>
								<option v-for="area in homogeneousAreas" :key="area.id" :value="area.id">
									{{ area.label }}
								</option>
							</select>
							<div v-if="selectedOptions.propriedade && homogeneousAreas.length === 0" class="empty-message">
								<i class="fas fa-exclamation-circle"></i>
								Nenhuma área homogênea encontrada
							</div>
						</div>
					</div>
				</form>
			</div>
		</div>

		<!-- Seção do Gráfico -->
		<div class="chart-section" v-if="!loading">
			<div class="chart-card" v-if="collectionData.length > 0 && chartData.labels && chartData.labels.length > 0">
				<div class="chart-header">
					<h3>Evolução por Safra</h3>
					<p>Quantidade de frutos colhidos por área homogênea ao longo das safras</p>
				</div>
				<div class="chart-container">
					<LineChart :key="chartKey" :chart-data="chartData" :chart-options="chartOptions" />
				</div>
			</div>

			<div class="empty-chart" v-else-if="collectionData.length > 0">
				<div class="empty-content">
					<div class="empty-icon">
						<i class="fas fa-chart-line"></i>
					</div>
					<h3>Nenhum dado encontrado</h3>
					<p>Não foram encontrados dados para os filtros selecionados. Tente ajustar os parâmetros de busca.</p>
				</div>
			</div>
		</div>

		<!-- Loading State -->
		<div class="loading-section" v-if="loading">
			<div class="loading-content">
				<div class="loading-spinner">
					<div class="spinner"></div>
				</div>
				<h3>Processando dados...</h3>
				<p>Aguarde enquanto geramos sua análise</p>
			</div>
		</div>
	</div>
</template>

<script src="./TotalHarvestedHA.js"></script>

<style lang="scss" scoped>
// Variáveis
$primary-color: #3d8160;
$primary-dark: #2d6047;
$text-color: #2c3e50;
$text-light: #6c757d;
$white: #ffffff;
$background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
$shadow: rgba(0, 0, 0, 0.1);
$shadow-hover: rgba(0, 0, 0, 0.15);
$border-color: #dee2e6;
$success-color: #28a745;
$danger-color: #dc3545;

// Container principal
.total-harvested-ha-container {
	min-height: 100vh;
	background: $background;
	padding: 1rem;
	font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

// Header no estilo DataImport
.page-header {
	background: $white;
	border-radius: 20px;
	padding: 2rem;
	margin-bottom: 2rem;
	box-shadow: 0 10px 30px $shadow;
	border: 1px solid rgba(255, 255, 255, 0.2);
	backdrop-filter: blur(10px);

	.header-content {
		display: flex;
		align-items: center;
		gap: 1.5rem;
	}

	.header-icon {
		width: 70px;
		height: 70px;
		background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
		border-radius: 20px;
		display: flex;
		align-items: center;
		justify-content: center;
		color: $white;
		font-size: 2rem;
		box-shadow: 0 8px 25px rgba($primary-color, 0.3);
	}

	.header-text {
		h1 {
			font-size: 2.5rem;
			font-weight: 700;
			color: $text-color;
			margin: 0 0 0.5rem 0;
			background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			background-clip: text;
		}

		p {
			font-size: 1.1rem;
			color: $text-light;
			margin: 0;
			font-weight: 400;
		}
	}
}

// Seção de filtros
.filters-section {
	margin-bottom: 2rem;

	.filters-card {
		background: $white;
		border-radius: 16px;
		padding: 2rem;
		box-shadow: 0 4px 20px $shadow;
		border: 1px solid rgba(255, 255, 255, 0.2);

		h3 {
			font-size: 1.5rem;
			font-weight: 600;
			color: $text-color;
			margin: 0 0 1.5rem 0;
			display: flex;
			align-items: center;
			gap: 0.5rem;

			&::before {
				content: '';
				width: 4px;
				height: 24px;
				background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
				border-radius: 2px;
			}
		}
	}
}

.filters-form {
	.filters-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 1.5rem;
		margin-bottom: 2rem;
	}

	.filter-group {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;

		label {
			font-weight: 600;
			color: $text-color;
			font-size: 0.95rem;
			display: flex;
			align-items: center;
			gap: 0.5rem;

			i {
				color: $primary-color;
				font-size: 1rem;
			}
		}

		.filter-select {
			padding: 0.75rem 1rem;
			border: 2px solid $border-color;
			border-radius: 8px;
			background: $white;
			font-size: 1rem;
			color: $text-color;
			transition: all 0.3s ease;
			appearance: none;
			background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
			background-position: right 0.5rem center;
			background-repeat: no-repeat;
			background-size: 1.5em 1.5em;
			padding-right: 2.5rem;

			&:focus {
				outline: none;
				border-color: $primary-color;
				box-shadow: 0 0 0 3px rgba($primary-color, 0.1);
			}

			&:disabled {
				background-color: #f8f9fa;
				color: #6c757d;
				cursor: not-allowed;
			}
		}

		.empty-message {
			display: flex;
			align-items: center;
			gap: 0.5rem;
			color: $danger-color;
			font-size: 0.85rem;
			font-weight: 500;
			background: rgba($danger-color, 0.1);
			padding: 0.5rem 0.75rem;
			border-radius: 6px;
			border-left: 3px solid $danger-color;

			i {
				font-size: 0.9rem;
			}
		}
	}

	.filter-actions {
		display: flex;
		justify-content: center;
		margin-top: 1rem;
	}
}

// Botão de análise
.analyze-btn {
	background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
	color: $white;
	border: none;
	border-radius: 12px;
	padding: 1rem 2rem;
	font-weight: 600;
	font-size: 1rem;
	cursor: pointer;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	box-shadow: 0 4px 15px rgba($primary-color, 0.3);
	min-width: 200px;

	&:disabled {
		opacity: 0.6;
		cursor: not-allowed;
		transform: none !important;
	}

	&:not(:disabled):hover {
		transform: translateY(-2px);
		box-shadow: 0 8px 25px rgba($primary-color, 0.4);
	}

	.btn-content {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 0.75rem;
	}

	.btn-icon {
		font-size: 1.1rem;
	}

	.btn-text {
		font-weight: 600;
	}

	&.loading .btn-content {
		opacity: 0.8;
	}
}

// Spinner personalizado
.btn-spinner, .loading-spinner {
	.spinner {
		width: 20px;
		height: 20px;
		border: 2px solid rgba($white, 0.3);
		border-top: 2px solid $white;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}
}

.loading-spinner .spinner {
	width: 40px;
	height: 40px;
	border-width: 4px;
	border-color: rgba($primary-color, 0.3);
	border-top-color: $primary-color;
}

// Seção do gráfico
.chart-section {
	.chart-card {
		background: $white;
		border-radius: 16px;
		padding: 2rem;
		box-shadow: 0 4px 20px $shadow;
		border: 1px solid rgba(255, 255, 255, 0.2);

		.chart-header {
			margin-bottom: 2rem;
			text-align: center;

			h3 {
				font-size: 1.5rem;
				font-weight: 600;
				color: $text-color;
				margin: 0 0 0.5rem 0;
			}

			p {
				color: $text-light;
				margin: 0;
			}
		}

		.chart-container {
			height: 500px;
			width: 100%;
		}
	}

	.empty-chart {
		background: $white;
		border-radius: 16px;
		padding: 3rem;
		box-shadow: 0 4px 20px $shadow;
		text-align: center;

		.empty-content {
			.empty-icon {
				width: 80px;
				height: 80px;
				background: rgba($text-light, 0.1);
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0 auto 1.5rem auto;
				color: $text-light;
				font-size: 2rem;
			}

			h3 {
				font-size: 1.5rem;
				font-weight: 600;
				color: $text-color;
				margin: 0 0 1rem 0;
			}

			p {
				color: $text-light;
				margin: 0;
				max-width: 400px;
				margin: 0 auto;
			}
		}
	}
}

// Loading state
.loading-section {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 400px;
	background: $white;
	border-radius: 16px;
	box-shadow: 0 4px 20px $shadow;

	.loading-content {
		text-align: center;

		.loading-spinner {
			margin-bottom: 1.5rem;
			display: flex;
			justify-content: center;
		}

		h3 {
			font-size: 1.5rem;
			font-weight: 600;
			color: $text-color;
			margin: 0 0 0.5rem 0;
		}

		p {
			color: $text-light;
			margin: 0;
		}
	}
}

// Animações
@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

// Responsividade
@media (max-width: 768px) {
	.total-harvested-ha-container {
		padding: 0.5rem;
	}

	.page-header {
		padding: 1.5rem;
		margin-bottom: 1.5rem;

		.header-content {
			flex-direction: column;
			text-align: center;
			gap: 1rem;
		}

		.header-icon {
			width: 60px;
			height: 60px;
			font-size: 1.8rem;
		}

		.header-text h1 {
			font-size: 2rem;
		}

		.header-text p {
			font-size: 1rem;
		}
	}

	.filters-card {
		padding: 1.5rem;
	}

	.filters-grid {
		grid-template-columns: 1fr;
		gap: 1rem;
	}

	.analyze-btn {
		width: 100%;
		min-width: auto;
	}

	.chart-card {
		padding: 1.5rem;

		.chart-container {
			height: 400px;
		}
	}
}

@media (max-width: 480px) {
	.page-header .header-text h1 {
		font-size: 1.75rem;
	}

	.filters-card h3 {
		font-size: 1.25rem;
	}

	.chart-card .chart-container {
		height: 350px;
	}
}
</style>
