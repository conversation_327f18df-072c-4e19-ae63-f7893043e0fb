import axios from "axios";
import LineChart from "../../../../components/LineChart.vue";

export default {
	components: {
		LineChart,
	},
	data() {
		return {
			authToken: null,
			properties: [],
			homogeneousAreas: [],
			selectedOptions: {
				propriedade: "",
				areaHomogenea: "",
			},
			loading: true,
			datePeriods: [], // Vai armazenar as safras disponíveis
			collectionData: [],
			chartKey: 0,
			chartData: {
				labels: [],
				datasets: []
			},
			chartOptions: {
				responsive: true,
				maintainAspectRatio: false,
			}
		};
	},

	computed: {
		totalProperties() {
			return this.properties.length > 0;
		}
	},

	watch: {
		"selectedOptions.propriedade": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				this.selectedOptions.areaHomogenea = "";
				this.homogeneousAreas = [];
				this.datePeriods = [];
				this.fetchHomogeneousAreas();
				this.fetchVisitDates();
			}
		},

		"selectedOptions.areaHomogenea": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				this.fetchCollectionData();
			}
		},
	},

	computed: {
		isFormValid() {
			return this.selectedOptions.propriedade &&
				   this.selectedOptions.areaHomogenea;
		}
	},

	created() {
		this.authToken = window.token;
		this.loading = true;
		this.fetchProperties();
	},

	methods: {
		fetchProperties() {
			this.loading = true;
			axios
				.get("/api/v1/properties", {
					headers: { authorization: `bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						this.properties = response.data.data;
						if (this.properties.length > 0) {
							this.selectedOptions.propriedade = this.properties[0].id;
							this.fetchHomogeneousAreas(true);
							this.fetchVisitDates(true);
						}
					}
				})
				.catch((err) => {
					console.log(err);
					this.loading = false;
				});
		},

		fetchHomogeneousAreas(isInit = false) {
			if (!this.selectedOptions.propriedade) return;

			this.loading = true;
			axios
				.get(`/api/v1/homogeneous-area?propid=${this.selectedOptions.propriedade}`, {
					headers: { authorization: `Bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						this.homogeneousAreas = response.data.data;
						if (isInit && this.homogeneousAreas.length > 0) {
							this.selectedOptions.areaHomogenea = this.homogeneousAreas[0].id;
						}
					}
				})
				.catch((err) => {
					console.error(err);
					this.loading = false;
				});
		},

		async fetchVisitDates(isInit = false) {
			try {
				const response = await axios.get("/api/v1/get-visit-dates", {
					headers: { authorization: `bearer ${this.authToken}` },
				});
				const { oldestVisit, latestVisit } = response.data;

				if (oldestVisit && latestVisit) {
					this.generateDateOptions(oldestVisit, latestVisit);
					if (isInit && this.datePeriods.length > 0) {
						this.fetchCollectionData();
					}
				} else {
					this.datePeriods = [];
				}
			} catch (error) {
				if (error.response && error.response.status === 404) {
					this.datePeriods = [];
				} else {
					console.error(error);
				}
			}
		},

		generateDateOptions(oldestVisit, latestVisit) {
			const oldestYear = new Date(oldestVisit).getFullYear();
			const latestYear = new Date(latestVisit).getFullYear();
			const currentYear = new Date().getFullYear();
			const currentMonth = new Date().getMonth();
			const limitYear = currentMonth <= 8 ? currentYear - 1 : currentYear;

			this.datePeriods = [];

			for (let year = oldestYear; year <= latestYear && year <= limitYear; year++) {
				const nextYear = year + 1;
				this.datePeriods.push(`Outubro/${year} - Setembro/${nextYear}`);
			}
		},

		parseSelectedPeriod(period) {
			const periodParts = period.split(" - ");
			const startYear = periodParts[0].split("/")[1];
			const endYear = periodParts[1].split("/")[1];

			return [`Outubro/${startYear}`, `Setembro/${endYear}`];
		},

		async fetchCollectionData() {
		if (!this.selectedOptions.propriedade || !this.selectedOptions.areaHomogenea) {
			alert("Selecione a propriedade e a área homogênea!");
			return;
		}
		this.loading = true;

		const { propriedade, areaHomogenea } = this.selectedOptions;

		const requests = this.datePeriods.map(period => {
			const [startDate, endDate] = this.parseSelectedPeriod(period);
			return axios.post("/api/v1/get-collection-data-harvested-ha",
				{ propertyId: propriedade, areaHomogeneaId: areaHomogenea, startDate, endDate },
				{ headers: { authorization: `bearer ${this.authToken}` } }
			).then(res => ({
				safra: period,
				data: res.data // agora já é um array de inteiros ordenados
			}));
		});

		try {
			const responses = await Promise.all(requests);
			this.collectionData = responses;
			this.getTotalHarvestedForPeriod();
		} catch (err) {
			console.error("Erro ao obter dados:", err);
		} finally {
			this.loading = false;
		}
	},

		formatDate(dateString) {
			if (!dateString) return "";
			const date = new Date(dateString);
			const day = String(date.getDate()).padStart(2, '0');
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const year = date.getFullYear();
			return `${day}/${month}/${year}`;
		},

		getTotalHarvestedForPeriod() {
			this.chartData.labels = [];
			this.chartData.datasets = [];

			let maxColetas = 0;

			for (const safraObj of this.collectionData) {
				const totals = safraObj.data || [];
				if (totals.length > maxColetas) {
					maxColetas = totals.length;
				}
			}

			this.chartData.labels = Array.from({ length: maxColetas }, (_, i) => `${i + 1}ª Coleta`);

			const colors = ["green", "blue", "red", "orange", "purple", "teal", "brown", "pink"];
			let colorIndex = 0;

			for (const safraObj of this.collectionData) {
				const dataPoints = Array.from({ length: maxColetas }, (_, i) => safraObj.data[i] || 0);

				this.chartData.datasets.push({
					label: safraObj.safra,
					borderColor: colors[colorIndex % colors.length],
					backgroundColor: "transparent",
					pointBackgroundColor: colors[colorIndex % colors.length],
					pointBorderColor: "white",
					pointRadius: 5,
					data: dataPoints
				});

				colorIndex++;
			}

			this.chartKey++; // força re-render
		},
		// Continua o mesmo formatador de dd/mm
		formatDayMonth(dateString) {
			if (!dateString) return "";
			const date = new Date(dateString);
			const day = String(date.getDate()).padStart(2, '0');
			const month = String(date.getMonth() + 1).padStart(2, '0');
			return `${day}/${month}`;
		},


	}
};
