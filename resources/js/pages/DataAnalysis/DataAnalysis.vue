<template>
	<div class="data-analysis-container">
		<!-- Header Simples -->
		<div class="page-header">
			<div class="header-content">
				<back-button title="Voltar para propriedades"></back-button>
				<div class="header-text">
					<h1><PERSON><PERSON><PERSON><PERSON></h1>
					<p>Escolha uma análise para visualizar seus dados</p>
				</div>
			</div>
		</div>

		<!-- Grid de Cards -->
		<div class="cards-grid">
			<div
				v-for="(card, index) in cards"
				:key="index"
				class="analysis-card"
				@click="navigateTo(card.route)"
			>
				<div class="card-icon">
					<i :class="card.icon"></i>
				</div>
				<div class="card-content">
					<h3>{{ card.title }}</h3>
					<p>{{ card.description }}</p>
				</div>
				<div class="card-hover-effect"></div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: "AnalysisOptions",
	data() {
		return {
			cards: [
				{
					icon: "fas fa-table",
					title: "Resumo da Safra",
					description: "Visualize o resumo dos dados da safra",
					route: "harvest-summary"
				},
				{
					icon: "fas fa-chart-pie",
					title: "Resumo da Coleta",
					description: "Visualize o resumo dos dados de uma coleta",
					route: "data-percentage"
				},
				{
					icon: "fas fa-chart-line",
					title: "Frutos Colhidos por Área Homogênea",
					description: "Acompanhe a quantidade de frutos colhidos em cada Área Homogênea por coleta",
					route: "total-harvested"
				},
				{
					icon: "fas fa-chart-line",
					title: "Frutos Colhidos por Safra",
					description: "Acompanhe a quantidade de frutos colhidos em cada safra por Área Homogênea",
					route: "total-harvested-ha"
				},
				{
					icon: "fas fa-chart-bar",
					title: "Análise do Desenvolvimento dos Bilros",
					description: "Visualize a evolução dos bilros ao longo da safra",
					route: "safra-results"
				},
				{
					icon: "fas fa-chart-bar",
					title: "Quantidade de Frutos por Faixa",
					description: "Veja a distribuição dos frutos por faixa etária das árvores",
					route: "total-cocoa"
				},
				{
					icon: "fas fa-chart-line",
					title: "Frutos Perdidos por Área Homogênea",
					description: "Analise diferentes fatores de perda de frutos por Área Homogênea",
					route: "fruit-loss-by-area"
				},
				{
					icon: "fas fa-chart-line",
					title: "Frutos Perdidos por Safra",
					description: "Analise diferentes fatores de perda de frutos por Safra",
					route: "fruit-loss-by-harvest"
				},
			]
		};
	},
	methods: {
		navigateTo(path) {
			this.$router.push({ path });
			localStorage.setItem("path", this.$route.name);
		}
	}
};
</script>

<style lang="scss" scoped>
// Variáveis
$primary-color: #3d8160;
$primary-dark: #2d6047;
$text-color: #2c3e50;
$text-light: #6c757d;
$white: #ffffff;
$background: #f8f9fa;
$shadow: rgba(0, 0, 0, 0.08);
$shadow-hover: rgba(0, 0, 0, 0.15);

// Container principal
.data-analysis-container {
	min-height: 100vh;
	background: $background;
	padding: 2rem 1rem;
	font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

// Header limpo
.page-header {
	background: $white;
	border-radius: 16px;
	padding: 2rem;
	margin-bottom: 3rem;
	box-shadow: 0 4px 20px $shadow;
	max-width: 1200px;
	margin-left: auto;
	margin-right: auto;
	margin-bottom: 3rem;

	.header-content {
		display: flex;
		align-items: center;
		gap: 2rem;

		.header-text {
			h1 {
				font-size: 2.2rem;
				font-weight: 700;
				color: $text-color;
				margin: 0 0 0.5rem 0;
			}

			p {
				font-size: 1.1rem;
				color: $text-light;
				margin: 0;
			}
		}
	}
}

// Grid simples e limpo
.cards-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
	gap: 2rem;
	max-width: 1200px;
	margin: 0 auto;
}

// Cards elegantes e minimalistas
.analysis-card {
	background: $white;
	border-radius: 16px;
	padding: 2rem;
	box-shadow: 0 4px 20px $shadow;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	cursor: pointer;
	position: relative;
	overflow: hidden;
	border: 1px solid rgba(61, 129, 96, 0.1);

	&:hover {
		transform: translateY(-8px);
		box-shadow: 0 12px 40px $shadow-hover;
		border-color: rgba(61, 129, 96, 0.2);

		.card-icon {
			transform: scale(1.1);
			background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
		}

		.card-hover-effect {
			opacity: 1;
		}
	}

	.card-icon {
		width: 60px;
		height: 60px;
		background: rgba($primary-color, 0.1);
		border-radius: 12px;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 1.5rem;
		transition: all 0.3s ease;

		i {
			font-size: 1.8rem;
			color: $primary-color;
			transition: all 0.3s ease;
		}
	}

	&:hover .card-icon i {
		color: $white;
	}

	.card-content {
		h3 {
			font-size: 1.3rem;
			font-weight: 600;
			color: $text-color;
			margin: 0 0 1rem 0;
			line-height: 1.4;
		}

		p {
			font-size: 1rem;
			color: $text-light;
			margin: 0;
			line-height: 1.6;
		}
	}

	// Efeito sutil de hover
	.card-hover-effect {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 4px;
		background: linear-gradient(90deg, $primary-color 0%, $primary-dark 100%);
		opacity: 0;
		transition: opacity 0.3s ease;
	}
}

// Responsividade
@media (max-width: 768px) {
	.data-analysis-container {
		padding: 1rem 0.5rem;
	}

	.page-header {
		padding: 1.5rem;
		margin-bottom: 2rem;

		.header-content {
			flex-direction: column;
			text-align: center;
			gap: 1rem;

			.header-text h1 {
				font-size: 1.8rem;
			}

			.header-text p {
				font-size: 1rem;
			}
		}
	}

	.cards-grid {
		grid-template-columns: 1fr;
		gap: 1.5rem;
	}

	.analysis-card {
		padding: 1.5rem;

		.card-content h3 {
			font-size: 1.2rem;
		}

		.card-content p {
			font-size: 0.95rem;
		}
	}
}

@media (max-width: 480px) {
	.page-header .header-content .header-text h1 {
		font-size: 1.6rem;
	}

	.analysis-card {
		padding: 1.25rem;

		.card-icon {
			width: 50px;
			height: 50px;

			i {
				font-size: 1.5rem;
			}
		}
	}
}
</style>
