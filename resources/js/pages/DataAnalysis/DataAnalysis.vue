<template>
	<div id="reports" class="mt-5 dataAnalysisWrapper">
		<div class="admin-header d-flex justify-content-between align-items-center p-2">
			<div class="d-flex align-items-center">
				<back-button title="Voltar para propriedades"></back-button>
				<div class="d-flex flex-column">
					<h1>Selecione uma análise</h1>
					<p>
						Escolha uma análise para visualizar os dados de forma mais detalhada.
						As análises disponíveis incluem gráficos de barras, linhas e pizza,
						oferecendo uma visão abrangente do desempenho dos bilros e da
						incidência de doenças.
					</p>
				</div>
			</div>
		</div>

		<section id="analysis-options" class="container team">
			<div class="row justify-content-center">
				<div
					v-for="(card, index) in cards"
					:key="index"
					class="col-lg-3 col-md-4 col-sm-6 mb-4"
				>
					<div
						class="analysis-card rounded shadow"
						:style="{ backgroundColor: card.color }"
						@click="navigateTo(card.route)"
					>
						<h5 class="analysis-title m-0 text-center py-3 fw-semibold">
							{{ card.title }}
						</h5>
						<div class="analysis-content d-flex align-items-center justify-content-center">
							<div class="icon-wrap">
								<i :class="card.icon"></i>
							</div>
							<div class="text-wrap">
								<p class="text-center px-3">{{ card.description }}</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>
	</div>
</template>

<script>
export default {
	name: "AnalysisOptions",
	data() {
		return {
			cards: [
				{
					icon: "fas fa-table fa-lg",
					title: "Resumo da Safra",
					description: "Visualize o resumo dos dados da safra.",
					route: "harvest-summary",
					color: "#66bb6a"
				},
				{
					icon: "fas fa-chart-pie fa-lg",
					title: "Resumo da Coleta",
					description: "Visualize o resumo dos dados de uma coleta.",
					route: "data-percentage",
					color: "#66bb6a"
				},
				{
					icon: "fas fa-chart-line fa-lg",
					title: "Frutos Colhidos por Area Homogênea",
					description: "Acompanhe a quantidade de frutos colhidos em cada Área Homogênea por coleta com um gráfico de linhas.",
					route: "total-harvested",
					color: "#135d2a"
				},
				{
					icon: "fas fa-chart-line fa-lg",
					title: "Frutos Colhidos por Safra",
					description: "Acompanhe a quantidade de frutos colhidos em cada safra por Área Homogênea com um gráfico de linhas.",
					route: "total-harvested-ha",
					color: "#135d2a"
				},
				{
					icon: "fas fa-chart-bar fa-lg",
					title: "Análise do Desenvolvimento dos Bilros",
					description: "Visualize a evolução dos bilros ao longo da safra com um gráfico de barras empilhadas.",
					route: "safra-results",
					color: "#b89c30"
				},
				{
					icon: "fas fa-chart-bar fa-lg",
					title: "Quantidade de Frutos por Faixa",
					description: "Veja a distribuição dos frutos por faixa etária das árvores com um gráfico de barras agrupadas.",
					route: "total-cocoa",
					color: "#b89c30"
				},
				{
					icon: "fas fa-chart-line fa-lg",
					title: "Frutos Perdidos por Área Homogênea",
					description: "Analise diferentes fatores de perda de frutos (causas desconhecidas, vassoura de bruxa, peco, podridão parda e ratos) por Área Homogênea com gráfico de linhas.",
					route: "fruit-loss-by-area",
					color: "#e74c3c"
				},
				{
					icon: "fas fa-chart-line fa-lg",
					title: "Frutos Perdidos por Safra",
					description: "Analise diferentes fatores de perda de frutos (causas desconhecidas, vassoura de bruxa, peco, podridão parda e ratos) por Safra com gráfico de linhas.",
					route: "fruit-loss-by-harvest",
					color: "#e74c3c"
				},
			]
		};
	},
	methods: {
		navigateTo(path) {
			this.$router.push({ path });
			localStorage.setItem("path", this.$route.name);
		}
	}
};
</script>

<style lang="scss">
.dataAnalysisWrapper {
	background-color: #f5f8fd;
	border-radius: 20px;

	h1 {
		color: #3d8160;
		font-family: "Lexend", sans-serif;
		font-weight: 600;
		font-size: 24px;
		padding: 16px 0px 0px 16px;
	}

	p {
		font-family: "Lexend", sans-serif;
		font-size: 18px;
		padding: 0px 0px 0px 16px;
	}
}

.analysis-card {
	background-color: #135d2a;
	color: white;
	overflow: hidden;
	height: 260px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	transition: transform 0.3s ease, box-shadow 0.3s ease;
	cursor: pointer;

	&:hover {
		transform: translateY(-5px);
		box-shadow: 0 8px 16px rgba(0, 0, 0, 0.25);
	}

	.analysis-title {
		background-color: rgba(0, 0, 0, 0.15);
		font-size: 16px;
		font-weight: 600;
	}

	.analysis-content {
		flex: 1;
		position: relative;
		padding: 1rem;
		text-align: center;

		.icon-wrap,
		.text-wrap {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: opacity 0.4s ease, transform 0.4s ease;
		}

		.icon-wrap {
			opacity: 1;
			transform: translateY(0);
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 100%;

			i {
				font-size: 64px; // Aumente conforme necessário
			}
		}

		.text-wrap {
			opacity: 0;
			transform: translateY(20px);
			padding: 0 1rem;

			p {
				font-size: 14px;
			}
		}
	}

	&:hover .icon-wrap {
		opacity: 0;
		transform: translateY(-20px);
	}
	&:hover .text-wrap {
		opacity: 1;
		transform: translateY(0);
	}
}

@media (max-width: 576px) {
	.dataAnalysisWrapper {
		height: auto;
	}
}
</style>
