<template>
	<div class="data-analysis-container">
		<!-- Header Principal -->
		<div class="page-header">
			<div class="header-content">
				<div class="header-icon">
					<i class="fas fa-chart-line"></i>
				</div>
				<div class="header-text">
					<h1><PERSON><PERSON><PERSON><PERSON> de <PERSON>os</h1>
					<p>Escolha uma análise para visualizar os dados de forma detalhada e interativa</p>
				</div>
			</div>
		</div>

		<!-- Grid de Cards -->
		<div class="cards-grid">
			<div
				v-for="(card, index) in cards"
				:key="index"
				class="analysis-card"
				:class="card.category"
				@click="navigateTo(card.route)"
			>
				<div class="card-icon">
					<i :class="card.icon"></i>
				</div>
				<div class="card-content">
					<h3>{{ card.title }}</h3>
					<p>{{ card.description }}</p>
				</div>
				<div class="card-hover-effect"></div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: "AnalysisOptions",
	data() {
		return {
			cards: [
				{
					icon: "fas fa-table",
					title: "Resumo da Safra",
					description: "Visualize o resumo dos dados da safra",
					route: "harvest-summary",
					category: "summary"
				},
				{
					icon: "fas fa-chart-pie",
					title: "Resumo da Coleta",
					description: "Visualize o resumo dos dados de uma coleta",
					route: "data-percentage",
					category: "summary"
				},
				{
					icon: "fas fa-chart-line",
					title: "Frutos Colhidos por Área Homogênea",
					description: "Acompanhe a quantidade de frutos colhidos em cada Área Homogênea por coleta",
					route: "total-harvested",
					category: "production"
				},
				{
					icon: "fas fa-chart-line",
					title: "Frutos Colhidos por Safra",
					description: "Acompanhe a quantidade de frutos colhidos em cada safra por Área Homogênea",
					route: "total-harvested-ha",
					category: "production"
				},
				{
					icon: "fas fa-chart-bar",
					title: "Análise do Desenvolvimento dos Bilros",
					description: "Visualize a evolução dos bilros ao longo da safra",
					route: "safra-results",
					category: "production"
				},
				{
					icon: "fas fa-chart-bar",
					title: "Quantidade de Frutos por Faixa",
					description: "Veja a distribuição dos frutos por faixa etária das árvores",
					route: "total-cocoa",
					category: "production"
				},
				{
					icon: "fas fa-chart-line",
					title: "Frutos Perdidos por Área Homogênea",
					description: "Analise diferentes fatores de perda de frutos por Área Homogênea",
					route: "fruit-loss-by-area",
					category: "loss"
				},
				{
					icon: "fas fa-chart-line",
					title: "Frutos Perdidos por Safra",
					description: "Analise diferentes fatores de perda de frutos por Safra",
					route: "fruit-loss-by-harvest",
					category: "loss"
				},
			]
		};
	},
	methods: {
		navigateTo(path) {
			this.$router.push({ path });
			localStorage.setItem("path", this.$route.name);
		}
	}
};
</script>

<style lang="scss" scoped>
// Variáveis
$primary-color: #3d8160;
$primary-dark: #2d6047;
$summary-color: #17a2b8;
$production-color: #28a745;
$loss-color: #dc3545;
$text-color: #2c3e50;
$text-light: #6c757d;
$white: #ffffff;
$background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
$shadow: rgba(0, 0, 0, 0.1);
$shadow-hover: rgba(0, 0, 0, 0.15);

// Container principal
.data-analysis-container {
	min-height: 100vh;
	background: $background;
	padding: 2rem 1rem;
	font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

// Header no estilo da DataImport
.page-header {
	background: $white;
	border-radius: 20px;
	padding: 2rem;
	margin-bottom: 2rem;
	box-shadow: 0 10px 30px $shadow;
	border: 1px solid rgba(255, 255, 255, 0.2);
	backdrop-filter: blur(10px);

	.header-content {
		display: flex;
		align-items: center;
		gap: 1.5rem;
	}

	.header-icon {
		width: 70px;
		height: 70px;
		background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
		border-radius: 20px;
		display: flex;
		align-items: center;
		justify-content: center;
		color: $white;
		font-size: 2rem;
		box-shadow: 0 8px 25px rgba($primary-color, 0.3);
	}

	.header-text {
		h1 {
			font-size: 2.5rem;
			font-weight: 700;
			color: $text-color;
			margin: 0 0 0.5rem 0;
			background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			background-clip: text;
		}

		p {
			font-size: 1.1rem;
			color: $text-light;
			margin: 0;
			font-weight: 400;
		}
	}
}

// Grid para 4 cards por linha
.cards-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 2rem;
	max-width: 1400px;
	margin: 0 auto;
}

// Cards elegantes com cores por categoria
.analysis-card {
	background: $white;
	border-radius: 16px;
	padding: 2rem;
	box-shadow: 0 4px 20px $shadow;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	cursor: pointer;
	position: relative;
	overflow: hidden;
	border: 1px solid rgba(0, 0, 0, 0.05);

	// Cores por categoria
	&.summary {
		border: 1px solid rgba($summary-color, 0.1);

		.card-icon {
			background: rgba($summary-color, 0.1);

			i {
				color: $summary-color;
			}
		}

		&:hover {
			border-color: rgba($summary-color, 0.2);

			.card-icon {
				background: linear-gradient(135deg, $summary-color 0%, darken($summary-color, 10%) 100%);
			}

			.card-hover-effect {
				background: linear-gradient(90deg, $summary-color 0%, darken($summary-color, 10%) 100%);
			}
		}
	}

	&.production {
		border: 1px solid rgba($production-color, 0.1);

		.card-icon {
			background: rgba($production-color, 0.1);

			i {
				color: $production-color;
			}
		}

		&:hover {
			border-color: rgba($production-color, 0.2);

			.card-icon {
				background: linear-gradient(135deg, $production-color 0%, darken($production-color, 10%) 100%);
			}

			.card-hover-effect {
				background: linear-gradient(90deg, $production-color 0%, darken($production-color, 10%) 100%);
			}
		}
	}

	&.loss {
		border: 1px solid rgba($loss-color, 0.1);

		.card-icon {
			background: rgba($loss-color, 0.1);

			i {
				color: $loss-color;
			}
		}

		&:hover {
			border-color: rgba($loss-color, 0.2);

			.card-icon {
				background: linear-gradient(135deg, $loss-color 0%, darken($loss-color, 10%) 100%);
			}

			.card-hover-effect {
				background: linear-gradient(90deg, $loss-color 0%, darken($loss-color, 10%) 100%);
			}
		}
	}

	&:hover {
		transform: translateY(-8px);
		box-shadow: 0 12px 40px $shadow-hover;

		.card-icon {
			transform: scale(1.1);

			i {
				color: $white;
			}
		}

		.card-hover-effect {
			opacity: 1;
		}
	}

	.card-icon {
		width: 60px;
		height: 60px;
		border-radius: 12px;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 1.5rem;
		transition: all 0.3s ease;

		i {
			font-size: 1.8rem;
			transition: all 0.3s ease;
		}
	}

	.card-content {
		h3 {
			font-size: 1.3rem;
			font-weight: 600;
			color: $text-color;
			margin: 0 0 1rem 0;
			line-height: 1.4;
		}

		p {
			font-size: 1rem;
			color: $text-light;
			margin: 0;
			line-height: 1.6;
		}
	}

	// Efeito sutil de hover
	.card-hover-effect {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 4px;
		opacity: 0;
		transition: opacity 0.3s ease;
	}
}

// Responsividade
@media (max-width: 1200px) {
	.cards-grid {
		grid-template-columns: repeat(3, 1fr);
	}
}

@media (max-width: 900px) {
	.cards-grid {
		grid-template-columns: repeat(2, 1fr);
	}
}

@media (max-width: 768px) {
	.data-analysis-container {
		padding: 1rem 0.5rem;
	}

	.page-header {
		padding: 1.5rem;
		margin-bottom: 2rem;

		.header-content {
			flex-direction: column;
			text-align: center;
			gap: 1rem;
		}

		.header-icon {
			width: 60px;
			height: 60px;
			font-size: 1.8rem;
		}

		.header-text h1 {
			font-size: 2rem;
		}

		.header-text p {
			font-size: 1rem;
		}
	}

	.cards-grid {
		grid-template-columns: 1fr;
		gap: 1.5rem;
	}

	.analysis-card {
		padding: 1.5rem;

		.card-content h3 {
			font-size: 1.2rem;
		}

		.card-content p {
			font-size: 0.95rem;
		}
	}
}

@media (max-width: 480px) {
	.page-header {
		.header-text h1 {
			font-size: 1.75rem;
		}
	}

	.analysis-card {
		padding: 1.25rem;

		.card-icon {
			width: 50px;
			height: 50px;

			i {
				font-size: 1.5rem;
			}
		}
	}
}
</style>
