<template>
	<div class="data-analysis-container">
		<!-- Header Principal -->
		<div class="page-header">
			<div class="header-content">
				<div class="back-button-wrapper">
					<back-button title="Voltar para propriedades"></back-button>
				</div>
				<div class="header-main">
					<div class="header-icon">
						<i class="fas fa-chart-line"></i>
					</div>
					<div class="header-text">
						<h1>Análise de Dados</h1>
						<p>Escolha uma análise para visualizar os dados de forma detalhada e interativa</p>
					</div>
				</div>
			</div>
			<div class="header-description">
				<div class="description-item">
					<div class="description-icon">
						<i class="fas fa-chart-bar"></i>
					</div>
					<span>Gráficos de Barras</span>
				</div>
				<div class="description-item">
					<div class="description-icon">
						<i class="fas fa-chart-line"></i>
					</div>
					<span>Gráficos de Linhas</span>
				</div>
				<div class="description-item">
					<div class="description-icon">
						<i class="fas fa-chart-pie"></i>
					</div>
					<span>Gráficos de Pizza</span>
				</div>
				<div class="description-item">
					<div class="description-icon">
						<i class="fas fa-table"></i>
					</div>
					<span>Resumos Detalhados</span>
				</div>
			</div>
		</div>

		<!-- Grid de Análises -->
		<div class="analysis-grid">
			<div class="grid-container">
				<div
					v-for="(card, index) in cards"
					:key="index"
					class="analysis-card"
					:class="card.category"
					@click="navigateTo(card.route)"
				>
					<div class="card-header">
						<div class="card-category">
							<span class="category-badge">{{ card.categoryName }}</span>
						</div>
						<div class="card-icon">
							<i :class="card.icon"></i>
						</div>
					</div>

					<div class="card-content">
						<h3 class="card-title">{{ card.title }}</h3>
						<p class="card-description">{{ card.description }}</p>
					</div>

					<div class="card-footer">
						<div class="chart-type">
							<i :class="card.chartIcon"></i>
							<span>{{ card.chartType }}</span>
						</div>
						<div class="card-arrow">
							<i class="fas fa-arrow-right"></i>
						</div>
					</div>

					<div class="card-overlay">
						<div class="overlay-content">
							<div class="overlay-icon">
								<i :class="card.icon"></i>
							</div>
							<h4>{{ card.title }}</h4>
							<p>{{ card.description }}</p>
							<div class="overlay-button">
								<span>Visualizar Análise</span>
								<i class="fas fa-external-link-alt"></i>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Seção de Estatísticas -->
		<div class="stats-section">
			<div class="stats-container">
				<div class="stats-item">
					<div class="stats-icon summary">
						<i class="fas fa-chart-area"></i>
					</div>
					<div class="stats-content">
						<h4>Resumos</h4>
						<p>{{ summaryCount }} análises</p>
					</div>
				</div>
				<div class="stats-item">
					<div class="stats-icon production">
						<i class="fas fa-seedling"></i>
					</div>
					<div class="stats-content">
						<h4>Produção</h4>
						<p>{{ productionCount }} análises</p>
					</div>
				</div>
				<div class="stats-item">
					<div class="stats-icon loss">
						<i class="fas fa-exclamation-triangle"></i>
					</div>
					<div class="stats-content">
						<h4>Perdas</h4>
						<p>{{ lossCount }} análises</p>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: "AnalysisOptions",
	data() {
		return {
			cards: [
				{
					icon: "fas fa-table",
					title: "Resumo da Safra",
					description: "Visualize o resumo completo dos dados da safra com métricas detalhadas e indicadores de performance.",
					route: "harvest-summary",
					category: "summary",
					categoryName: "Resumo",
					chartIcon: "fas fa-table",
					chartType: "Tabela"
				},
				{
					icon: "fas fa-chart-pie",
					title: "Resumo da Coleta",
					description: "Analise o resumo dos dados de uma coleta específica com distribuição percentual dos resultados.",
					route: "data-percentage",
					category: "summary",
					categoryName: "Resumo",
					chartIcon: "fas fa-chart-pie",
					chartType: "Pizza"
				},
				{
					icon: "fas fa-seedling",
					title: "Frutos Colhidos por Área Homogênea",
					description: "Acompanhe a evolução da quantidade de frutos colhidos em cada Área Homogênea ao longo das coletas.",
					route: "total-harvested",
					category: "production",
					categoryName: "Produção",
					chartIcon: "fas fa-chart-line",
					chartType: "Linhas"
				},
				{
					icon: "fas fa-chart-area",
					title: "Frutos Colhidos por Safra",
					description: "Compare a quantidade de frutos colhidos entre diferentes safras por Área Homogênea.",
					route: "total-harvested-ha",
					category: "production",
					categoryName: "Produção",
					chartIcon: "fas fa-chart-line",
					chartType: "Linhas"
				},
				{
					icon: "fas fa-layer-group",
					title: "Desenvolvimento dos Bilros",
					description: "Visualize a evolução completa dos bilros ao longo da safra com análise detalhada de cada estágio.",
					route: "safra-results",
					category: "production",
					categoryName: "Produção",
					chartIcon: "fas fa-chart-bar",
					chartType: "Barras"
				},
				{
					icon: "fas fa-chart-column",
					title: "Frutos por Faixa Etária",
					description: "Analise a distribuição dos frutos por faixa etária das árvores com comparação entre diferentes períodos.",
					route: "total-cocoa",
					category: "production",
					categoryName: "Produção",
					chartIcon: "fas fa-chart-bar",
					chartType: "Barras"
				},
				{
					icon: "fas fa-exclamation-triangle",
					title: "Perdas por Área Homogênea",
					description: "Identifique e analise os diferentes fatores de perda de frutos por Área Homogênea com tendências temporais.",
					route: "fruit-loss-by-area",
					category: "loss",
					categoryName: "Perdas",
					chartIcon: "fas fa-chart-line",
					chartType: "Linhas"
				},
				{
					icon: "fas fa-chart-line-down",
					title: "Perdas por Safra",
					description: "Compare os fatores de perda de frutos entre diferentes safras para identificar padrões e oportunidades de melhoria.",
					route: "fruit-loss-by-harvest",
					category: "loss",
					categoryName: "Perdas",
					chartIcon: "fas fa-chart-line",
					chartType: "Linhas"
				},
			]
		};
	},
	computed: {
		summaryCount() {
			return this.cards.filter(card => card.category === 'summary').length;
		},
		productionCount() {
			return this.cards.filter(card => card.category === 'production').length;
		},
		lossCount() {
			return this.cards.filter(card => card.category === 'loss').length;
		}
	},
	methods: {
		navigateTo(path) {
			this.$router.push({ path });
			localStorage.setItem("path", this.$route.name);
		}
	}
};
</script>

<style lang="scss" scoped>
// Variáveis de cores
$primary-color: #3d8160;
$primary-dark: #2d6047;
$primary-light: #e8f5e8;
$secondary-color: #6c757d;
$success-color: #28a745;
$info-color: #17a2b8;
$warning-color: #ffc107;
$danger-color: #dc3545;
$light-color: #f8f9fa;
$dark-color: #343a40;
$white: #ffffff;
$border-color: #dee2e6;
$shadow-light: rgba(0, 0, 0, 0.1);
$shadow-medium: rgba(0, 0, 0, 0.15);
$shadow-dark: rgba(0, 0, 0, 0.25);

// Cores das categorias
$summary-color: #17a2b8;
$production-color: #28a745;
$loss-color: #dc3545;

// Container principal
.data-analysis-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	padding: 1rem;
	font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

// Header da página
.page-header {
	background: $white;
	border-radius: 24px;
	padding: 2rem;
	margin-bottom: 2rem;
	box-shadow: 0 15px 35px $shadow-light;
	border: 1px solid rgba(255, 255, 255, 0.2);
	backdrop-filter: blur(10px);

	.header-content {
		display: flex;
		align-items: center;
		gap: 2rem;
		margin-bottom: 2rem;

		.back-button-wrapper {
			flex-shrink: 0;
		}

		.header-main {
			display: flex;
			align-items: center;
			gap: 1.5rem;
			flex: 1;
		}

		.header-icon {
			width: 70px;
			height: 70px;
			background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
			border-radius: 20px;
			display: flex;
			align-items: center;
			justify-content: center;
			color: $white;
			font-size: 2rem;
			box-shadow: 0 8px 25px rgba($primary-color, 0.3);
		}

		.header-text {
			h1 {
				font-size: 2.5rem;
				font-weight: 700;
				color: $dark-color;
				margin: 0 0 0.5rem 0;
				background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
				background-clip: text;
			}

			p {
				font-size: 1.1rem;
				color: $secondary-color;
				margin: 0;
				font-weight: 400;
			}
		}
	}

	.header-description {
		display: flex;
		gap: 2rem;
		flex-wrap: wrap;

		.description-item {
			display: flex;
			align-items: center;
			gap: 0.75rem;
			padding: 0.75rem 1.25rem;
			background: rgba($primary-color, 0.05);
			border-radius: 12px;
			border: 1px solid rgba($primary-color, 0.1);

			.description-icon {
				width: 32px;
				height: 32px;
				background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
				border-radius: 8px;
				display: flex;
				align-items: center;
				justify-content: center;
				color: $white;
				font-size: 0.9rem;
			}

			span {
				font-weight: 500;
				color: $dark-color;
				font-size: 0.95rem;
			}
		}
	}
}

// Grid de análises
.analysis-grid {
	margin-bottom: 3rem;

	.grid-container {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
		gap: 2rem;
		max-width: 1400px;
		margin: 0 auto;
	}
}

// Cards de análise
.analysis-card {
	background: $white;
	border-radius: 20px;
	overflow: hidden;
	box-shadow: 0 10px 30px $shadow-light;
	border: 1px solid rgba(255, 255, 255, 0.2);
	backdrop-filter: blur(10px);
	transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
	cursor: pointer;
	position: relative;
	height: 280px;

	&:hover {
		transform: translateY(-8px) scale(1.02);
		box-shadow: 0 25px 50px $shadow-medium;

		.card-overlay {
			opacity: 1;
			visibility: visible;
		}
	}

	// Cores por categoria
	&.summary {
		border-top: 4px solid $summary-color;

		.card-icon {
			background: linear-gradient(135deg, $summary-color 0%, darken($summary-color, 10%) 100%);
		}

		.category-badge {
			background: rgba($summary-color, 0.1);
			color: $summary-color;
		}
	}

	&.production {
		border-top: 4px solid $production-color;

		.card-icon {
			background: linear-gradient(135deg, $production-color 0%, darken($production-color, 10%) 100%);
		}

		.category-badge {
			background: rgba($production-color, 0.1);
			color: $production-color;
		}
	}

	&.loss {
		border-top: 4px solid $loss-color;

		.card-icon {
			background: linear-gradient(135deg, $loss-color 0%, darken($loss-color, 10%) 100%);
		}

		.category-badge {
			background: rgba($loss-color, 0.1);
			color: $loss-color;
		}
	}

	.card-header {
		padding: 1.5rem 1.5rem 1rem 1.5rem;
		display: flex;
		justify-content: space-between;
		align-items: flex-start;

		.card-category {
			.category-badge {
				padding: 0.25rem 0.75rem;
				border-radius: 20px;
				font-size: 0.75rem;
				font-weight: 600;
				text-transform: uppercase;
				letter-spacing: 0.5px;
			}
		}

		.card-icon {
			width: 50px;
			height: 50px;
			border-radius: 12px;
			display: flex;
			align-items: center;
			justify-content: center;
			color: $white;
			font-size: 1.5rem;
			box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
		}
	}

	.card-content {
		padding: 0 1.5rem 1rem 1.5rem;

		.card-title {
			font-size: 1.25rem;
			font-weight: 600;
			color: $dark-color;
			margin: 0 0 0.75rem 0;
			line-height: 1.3;
		}

		.card-description {
			font-size: 0.9rem;
			color: $secondary-color;
			margin: 0;
			line-height: 1.5;
			display: -webkit-box;
			-webkit-line-clamp: 3;
			-webkit-box-orient: vertical;
			overflow: hidden;
		}
	}

	.card-footer {
		padding: 1rem 1.5rem 1.5rem 1.5rem;
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: auto;

		.chart-type {
			display: flex;
			align-items: center;
			gap: 0.5rem;
			color: $secondary-color;
			font-size: 0.85rem;
			font-weight: 500;

			i {
				font-size: 1rem;
			}
		}

		.card-arrow {
			width: 32px;
			height: 32px;
			background: rgba($primary-color, 0.1);
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			color: $primary-color;
			transition: all 0.3s ease;

			i {
				font-size: 0.9rem;
			}
		}
	}

	// Overlay de hover
	.card-overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, rgba($primary-color, 0.95) 0%, rgba($primary-dark, 0.95) 100%);
		display: flex;
		align-items: center;
		justify-content: center;
		opacity: 0;
		visibility: hidden;
		transition: all 0.3s ease;
		color: $white;
		text-align: center;
		padding: 2rem;

		.overlay-content {
			.overlay-icon {
				font-size: 3rem;
				margin-bottom: 1rem;
				opacity: 0.9;
			}

			h4 {
				font-size: 1.5rem;
				font-weight: 600;
				margin: 0 0 1rem 0;
			}

			p {
				font-size: 1rem;
				margin: 0 0 1.5rem 0;
				opacity: 0.9;
				line-height: 1.5;
			}

			.overlay-button {
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 0.5rem;
				background: rgba($white, 0.2);
				padding: 0.75rem 1.5rem;
				border-radius: 25px;
				font-weight: 500;
				transition: all 0.3s ease;

				&:hover {
					background: rgba($white, 0.3);
					transform: translateY(-2px);
				}
			}
		}
	}
}

// Seção de estatísticas
.stats-section {
	.stats-container {
		display: flex;
		justify-content: center;
		gap: 2rem;
		max-width: 800px;
		margin: 0 auto;
	}

	.stats-item {
		background: $white;
		border-radius: 16px;
		padding: 1.5rem;
		display: flex;
		align-items: center;
		gap: 1rem;
		box-shadow: 0 8px 25px $shadow-light;
		border: 1px solid rgba(255, 255, 255, 0.2);
		backdrop-filter: blur(10px);
		flex: 1;
		transition: all 0.3s ease;

		&:hover {
			transform: translateY(-3px);
			box-shadow: 0 12px 35px $shadow-medium;
		}

		.stats-icon {
			width: 50px;
			height: 50px;
			border-radius: 12px;
			display: flex;
			align-items: center;
			justify-content: center;
			color: $white;
			font-size: 1.5rem;

			&.summary {
				background: linear-gradient(135deg, $summary-color 0%, darken($summary-color, 10%) 100%);
			}

			&.production {
				background: linear-gradient(135deg, $production-color 0%, darken($production-color, 10%) 100%);
			}

			&.loss {
				background: linear-gradient(135deg, $loss-color 0%, darken($loss-color, 10%) 100%);
			}
		}

		.stats-content {
			h4 {
				font-size: 1.1rem;
				font-weight: 600;
				color: $dark-color;
				margin: 0 0 0.25rem 0;
			}

			p {
				font-size: 0.9rem;
				color: $secondary-color;
				margin: 0;
				font-weight: 500;
			}
		}
	}
}

// Responsividade
@media (max-width: 1200px) {
	.analysis-grid .grid-container {
		grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
		gap: 1.5rem;
	}
}

@media (max-width: 768px) {
	.data-analysis-container {
		padding: 0.5rem;
	}

	.page-header {
		padding: 1.5rem;
		margin-bottom: 1.5rem;

		.header-content {
			flex-direction: column;
			gap: 1.5rem;
			margin-bottom: 1.5rem;

			.header-main {
				flex-direction: column;
				text-align: center;
				gap: 1rem;
			}

			.header-text h1 {
				font-size: 2rem;
			}
		}

		.header-description {
			justify-content: center;
			gap: 1rem;

			.description-item {
				flex-direction: column;
				text-align: center;
				padding: 1rem;
				gap: 0.5rem;

				span {
					font-size: 0.85rem;
				}
			}
		}
	}

	.analysis-grid .grid-container {
		grid-template-columns: 1fr;
		gap: 1.5rem;
	}

	.analysis-card {
		height: auto;
		min-height: 250px;
	}

	.stats-section .stats-container {
		flex-direction: column;
		gap: 1rem;
	}
}

@media (max-width: 480px) {
	.page-header {
		.header-content .header-text h1 {
			font-size: 1.75rem;
		}

		.header-description {
			flex-direction: column;
		}
	}

	.analysis-card {
		.card-content .card-title {
			font-size: 1.1rem;
		}

		.card-overlay {
			padding: 1.5rem;

			.overlay-content {
				.overlay-icon {
					font-size: 2.5rem;
				}

				h4 {
					font-size: 1.25rem;
				}

				p {
					font-size: 0.9rem;
				}
			}
		}
	}
}
</style>
