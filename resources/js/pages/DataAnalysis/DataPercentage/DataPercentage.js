import axios from "axios";
import <PERSON><PERSON><PERSON> from "../../../components/PieChart.vue";

export default {
  components: {
    PieChart,
  },
	data() {
		return {
			authToken: null,
			properties: [],
			homogeneousAreas: [],
			selectedOptions: {
				propriedade: "",
				areaHomogenea: "",
				safra: "",
				coleta: "",
			},
			loading: true,
			datePeriods: [],
			startDate: "",
			endDate: "",
			collectionData: null,
			visitDates: [],
			formatedVisitDates: [],
			fruitDistribution: [],

			chartKey: 0,
			chartData: {
				labels: [],
				datasets: [
				  {
					label: "Proporção de Podres por Coleta",
					backgroundColor: [],
					data: [],
				  }
				]
			  },
			  chartOptions: {
				responsive: true,
				maintainAspectRatio: false,
				legend: {
				  position: 'bottom',
				  labels: {
					fontSize: 14
				  }
				},
				title: null,
			  }
		};
	},

	computed: {
		totalProperties() {
			return this.properties.length > 0;
		},
		isFormValid() {
			return this.selectedOptions.propriedade &&
				   this.selectedOptions.areaHomogenea &&
				   this.selectedOptions.safra &&
				   this.selectedOptions.coleta;
		}
	},

		watch: {
		"selectedOptions.propriedade": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				// Quando a propriedade mudar, limpar os campos AH e Safra
				this.selectedOptions.areaHomogenea = "";
				this.selectedOptions.safra = "";
				this.homogeneousAreas = [];
				this.datePeriods = [];
				this.fetchHomogeneousAreas();
				this.fetchVisitDates();
			}
		},

		"selectedOptions.areaHomogenea": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				this.selectedOptions.safra = "";
				this.selectedOptions.coleta = "";
				this.datePeriods = [];
				this.fetchVisitDates();
			}
		},

		"selectedOptions.safra": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				this.selectedOptions.coleta = "";
				this.fetchCollectionData();
			}
		},
	},

	created() {
		this.authToken = window.token;
		this.loading = true;
		this.fetchProperties();
	},

	methods: {
		updateChartDatasets() {
			const [dataColeta, valores] = Array.from(this.fruitDistribution.entries())[0] || [];
			if (!valores) return;

			const {
				rat,
				total,
				piece,
				rotten,
				wb,
				loss,
				harvested
			} = valores;

			const sum = harvested + piece + rat + wb + rotten + loss + total;
			if (sum === 0) return;

			const percent = (val) => (val / sum) * 100;

			this.chartData = {
				labels: [
					`Sadios: ${percent(total).toFixed(2)}%`,
					`Colhidos: ${percent(harvested).toFixed(2)}%`,
					`Peco: ${percent(piece).toFixed(2)}%`,
					`Rato: ${percent(rat).toFixed(2)}%`,
					`Vassoura de Bruxa: ${percent(wb).toFixed(2)}%`,
					`Podres: ${percent(rotten).toFixed(2)}%`,
					`Perdidos: ${percent(loss).toFixed(2)}%`,
				],
				datasets: [
					{
						label: `Distribuição de Frutos - ${this.formatDate(dataColeta)}`,
						backgroundColor: [
							"#e6d66a", // No pé - amarelo claro
							"#4caf50", // Colhidos - verde médio
							"#5c6bc0", // Peco - azul suave
							"#e57373", // Rato - vermelho claro
							"#bcaaa4", // Vassoura de Bruxa - marrom claro
							"#ba68c8", // Podres - roxo claro
							"#9e9e9e", // Perdidos - cinza médio
						],
						data: [total, harvested, piece, rat, wb, rotten, loss],
						borderWidth: 2,
					}
				]
			};

			if (rat === 0) this.chartData.datasets[0].borderWidth = 0;
			if (piece === 0) this.chartData.datasets[0].borderWidth = 0;
			if (rotten === 0) this.chartData.datasets[0].borderWidth = 0;
			if (wb === 0) this.chartData.datasets[0].borderWidth = 0;
			if (loss === 0) this.chartData.datasets[0].borderWidth = 0;
			if (harvested === 0) this.chartData.datasets[0].borderWidth = 0;

			this.chartOptions.title = {
				display: true,
				text: `Distribuição de Frutos - ${this.formatDate(dataColeta)}`,
				fontSize: 24,
				fontStyle: 'bold'
			};

			this.chartKey++;
		},

		fetchProperties() {
			this.loading = true;
			axios
				.get("/api/v1/properties", {
					headers: { authorization: `bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						this.properties = response.data.data;
						if (this.properties.length > 0) {
							this.selectedOptions.propriedade = this.properties[0].id;
							this.fetchHomogeneousAreas(true); // Passa true para diferenciar a inicialização
							this.fetchVisitDates(true);
						}
					}
				})
				.catch((err) => {
					console.log(err);
					this.loading = false;
				});
		},

		fetchHomogeneousAreas(isInit = false) {
			if (!this.selectedOptions.propriedade) return;

			this.loading = true;
			axios
				.get(`/api/v1/homogeneous-area?propid=${this.selectedOptions.propriedade}`, {
					headers: { authorization: `Bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						this.homogeneousAreas = response.data.data;
						if (isInit && this.homogeneousAreas.length > 0) {
							this.selectedOptions.areaHomogenea = this.homogeneousAreas[0].id;
						}
					}
				})
				.catch((err) => {
					console.error(err);
					this.loading = false;
				});
		},

		async fetchVisitDates(isInit = false) {
			try {
				const response = await axios.get("/api/v1/get-visit-dates", {
					headers: { authorization: `bearer ${this.authToken}` },
				});
				const { oldestVisit, latestVisit } = response.data;

				if (oldestVisit && latestVisit) {
					this.generateDateOptions(oldestVisit, latestVisit);
					if (isInit && this.datePeriods.length > 0) {
						this.selectedOptions.safra = this.datePeriods[this.datePeriods.length - 1];
						this.fetchCollectionData(isInit);
					}
				} else {
					this.datePeriods = [];
				}
			} catch (error) {
				if (error.response && error.response.status === 404) {
					this.datePeriods = [];
				} else {
					console.error(error);
				}
			}
		},

		generateDateOptions(oldestVisit, latestVisit) {
			const oldestYear = new Date(oldestVisit).getFullYear();
			const latestYear = new Date(latestVisit).getFullYear();
			const currentYear = new Date().getFullYear();
			const currentMonth = new Date().getMonth();
			const limitYear = currentMonth <= 8 ? currentYear - 1 : currentYear; // Ajuste aqui

			this.datePeriods = [];

			for (
				let year = oldestYear;
				year <= latestYear && year <= limitYear;
				year++
			) {
				const nextYear = year + 1;
				this.datePeriods.push(`Outubro/${year} - Setembro/${nextYear}`);
			}
		},

		fetchCollectionData(isInit = false) {
			if (!this.selectedOptions.propriedade || !this.selectedOptions.areaHomogenea || !this.selectedOptions.safra) {
				alert("Selecione todos os filtros necessários!");
				return;
			}

			const { propriedade, areaHomogenea, safra } = this.selectedOptions;
			const [startDate, endDate] = this.parseSelectedPeriod();

			axios
				.post(
					"/api/v1/get-fruit-distribution",
					{ propertyId: propriedade, areaHomogeneaId: areaHomogenea, startDate, endDate },
					{ headers: { authorization: `bearer ${this.authToken}` } }
				)
				.then((response) => {
					this.collectionData = response.data; // Armazena os dados para uso no gráfico
					this.getVisitDates();
					this.loading = false;
				})
				.catch((err) => {
					console.error("Erro ao obter dados:", err);
				});

		},

		parseSelectedPeriod() {
			if (!this.selectedOptions.safra) return ["", ""];

			const periodParts = this.selectedOptions.safra.split(" - ");
			const startYear = periodParts[0].split("/")[1];
			const endYear = periodParts[1].split("/")[1];

			return [`Outubro/${startYear}`, `Setembro/${endYear}`];
		},

		getVisitDates() {
			if (!this.collectionData || !this.collectionData.visitDates) {
				console.warn("Nenhum dado de visita encontrado.");
				this.visitDates = [];
				this.formatedVisitDates = [];
				return;
			}

			this.visitDates = this.collectionData.visitDates;
			this.formatedVisitDates = [];
			this.visitDates.forEach(date => {
				this.formatedVisitDates.push(this.formatDate(date));
			});
		},

		formatDate(dateString) {
			if (!dateString) return "";
			const date = new Date(dateString);
			const day = String(date.getDate()).padStart(2, '0');
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const year = date.getFullYear();
			return `${day}/${month}/${year}`;
		},

		getSelectedcoletaIndex() {
			for (let i = 0; i < this.formatedVisitDates.length; i++) {
				if (this.formatedVisitDates[i] === this.selectedOptions.coleta) {
					return i;
				}
			}
		},

		getFruitDistributionForSelectedColeta() {
			this.loading = true;
			this.fruitDistribution = new Map();

			const selectedDateIndex = this.getSelectedcoletaIndex();
			if (selectedDateIndex === undefined) return;

			const dataColeta = this.visitDates[selectedDateIndex];

			// Inicializa contadores
			let rat = this.collectionData.rat || 0;
			let totalFrutos = this.collectionData.total || 0;
			let piece = this.collectionData.piece || 0;
			let rotten = this.collectionData.rotten || 0;
			let wb = this.collectionData.wb || 0;
			let loss = this.collectionData.loss || 0;
			let harvested = this.collectionData.harvested || 0;

			this.fruitDistribution.set(dataColeta, {
				rat,
				total: totalFrutos,
				piece,
				rotten,
				wb,
				loss,
				harvested
			});

			this.updateChartDatasets();
			this.chartKey++;
			this.loading = false;
		}
	}
};
