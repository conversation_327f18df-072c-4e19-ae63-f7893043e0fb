import axios from "axios";
import <PERSON><PERSON>hart from '../../../../components/BarChart.vue';
import { get } from "jquery";
import { BREAK, f } from "vue-mapbox";

	export default {
	components: {
		BarChart
	},
	data() {
		return {
			authToken: null,
			properties: [],
			homogeneousAreas: [],
			selectedOptions: {
				propriedade: "",
				areaHomogenea: "",
				safra: "",
				coleta: "",
			},
			loading: true,
			datePeriods: [],
			startDate: "",
			endDate: "",
			faixas: [
				"0 - 21", "21 - 42", "42 - 63", "63 - 84", "84 - 105",
				"105 - 126", "126 - 147", "147 - 168", "168 - 189", "189 - 210", "> 210"
			],
			collectionData: [],
			visitDates: [],
			formatedVisitDates: [],
			totalbobbins: 0,
			resultsSafra: [],
			resultsSafraArray: [],
			collectionDataNext: [],

			chartKey: 0,
			chartData: {
				labels: [],
				datasets: [],
			},
			chartOptions: {
				responsive: true,
				maintainAspectRatio: false, // Melhora a renderização
				legend: { position: 'top' },
				title: null,
				scales: {
				xAxes: [{ stacked: true }],
				yAxes: [{ stacked: true }]
				}
			}
		};
	},

	computed: {
		totalProperties() {
			return this.properties.length > 0;
		}
	},

		watch: {
		"selectedOptions.propriedade": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				// Quando a propriedade mudar, limpar os campos AH e Safra
				this.selectedOptions.areaHomogenea = "";
				this.selectedOptions.safra = "";
				this.homogeneousAreas = [];
				this.datePeriods = [];
				this.fetchHomogeneousAreas();
				this.fetchVisitDates();
			}
		},

		"selectedOptions.areaHomogenea": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				this.selectedOptions.safra = "";
				this.selectedOptions.coleta = "";
				this.datePeriods = [];
				this.fetchVisitDates();
			}
		},

		"selectedOptions.safra": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				this.selectedOptions.coleta = "";
				this.fetchCollectionData();
			}
		},
	},

	created() {
		this.authToken = window.token;
		this.fetchProperties();
	},

	methods: {
		updateChartTitle() {
			const totalHarvested = this.resultsSafraArray.length > 0
				? this.resultsSafraArray[this.resultsSafraArray.length - 1].harvested
				: 0;

			const totalFruits = this.totalbobbins;

			const rendimento = totalFruits > 0 ? ((totalHarvested / totalFruits) * 100).toFixed(2) : 0;
			let title = `Taxa de Aproveitamento dos Frutos Colhidos: ${rendimento}%`;
			if(this.totalbobbins === 0){
				title = "Não houve bilros na coleta selecionada";
			}

			this.chartOptions.title = {
				display: true,
				text: title,
				fontSize: 32,
				fontStyle: 'bold'
			};
		},

		updateChartDataLabels() {
			if (!this.resultsSafraArray.length) {
				this.chartData.labels = [];
				return;
			}

			// Encontra o índice da primeira data em resultsSafraArray dentro de visitDates
			const firstDate = this.resultsSafraArray[0].date;
			const firstIndex = this.visitDates.indexOf(firstDate);

			// Define o label inicial como a data anterior (coleta selecionada)
			const initialLabel = firstIndex > 0 ? this.formatDate(this.visitDates[firstIndex - 1]) : this.formatDate(this.visitDates[0]);

			// Monta os labels: começa com a data da coleta selecionada, seguida pelas datas dos resultados
			const rawLabels = [
				initialLabel,
				...this.resultsSafraArray.map(entry => this.formatDate(entry.date))
			];

			// Remove o último (se necessário, como você fez antes)
			rawLabels.pop();

			// Quebra de linha: passa cada label como array com data + faixa
			this.chartData.labels = rawLabels.map((label, index) => {
				const faixa = this.faixas[index] || "";
				return [label.replace(/-/g, " - "), faixa]; // <- array com 2 linhas
			});
		},

		updateChartDatasets() {
			const categories = [
				{ key: "bobbins", label: "Frutos", color: "yellow" },
				{ key: "harvested", label: "Colhido", color: "green" },
				{ key: "piece", label: "Peco", color: "blue" },
				{ key: "loss", label: "Perda", color: "gray" },
				{ key: "witchsBroom", label: "Vassoura de Bruxa", color: "brown" },
				{ key: "rat", label: "Rato", color: "red" },
				{ key: "rotten", label: "Podre", color: "purple" },

			];

			this.chartData.datasets = categories.map((cat, index) => ({
				label: cat.label,
				data: this.resultsSafraArray.map(entry => entry[cat.key] || 0),
				backgroundColor: cat.color,
				stack: 'a'
			}));
		},

		fetchProperties() {
			this.loading = true;
			axios
				.get("/api/v1/properties", {
					headers: { authorization: `bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						this.properties = response.data.data;
						if (this.properties.length > 0) {
							this.selectedOptions.propriedade = this.properties[0].id;
							this.fetchHomogeneousAreas(true); // Passa true para diferenciar a inicialização
							this.fetchVisitDates(true);
						}
					}
				})
				.catch((err) => {
					console.log(err);
					this.loading = false;
				});
		},

		fetchHomogeneousAreas(isInit = false) {
			if (!this.selectedOptions.propriedade) return;

			this.loading = true;
			axios
				.get(`/api/v1/homogeneous-area?propid=${this.selectedOptions.propriedade}`, {
					headers: { authorization: `Bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						this.homogeneousAreas = response.data.data;
						if (isInit && this.homogeneousAreas.length > 0) {
							this.selectedOptions.areaHomogenea = this.homogeneousAreas[0].id;
						}
					}
				})
				.catch((err) => {
					console.error(err);
					this.loading = false;
				});
		},

		async fetchVisitDates(isInit = false) {
			try {
				const response = await axios.get("/api/v1/get-visit-dates", {
					headers: { authorization: `bearer ${this.authToken}` },
				});
				const { oldestVisit, latestVisit } = response.data;

				if (oldestVisit && latestVisit) {
					this.generateDateOptions(oldestVisit, latestVisit);
					if (isInit && this.datePeriods.length > 0) {
						this.selectedOptions.safra = this.datePeriods[this.datePeriods.length - 1];
						this.fetchCollectionData(isInit);
					}
				} else {
					this.datePeriods = [];
				}
			} catch (error) {
				if (error.response && error.response.status === 404) {
					this.datePeriods = [];
				} else {
					console.error(error);
				}
			}
		},

		generateDateOptions(oldestVisit, latestVisit) {
			const oldestYear = new Date(oldestVisit).getFullYear();
			const latestYear = new Date(latestVisit).getFullYear();
			const currentYear = new Date().getFullYear();
			const currentMonth = new Date().getMonth();
			const limitYear = currentMonth <= 8 ? currentYear - 1 : currentYear; // Ajuste aqui

			this.datePeriods = [];

			for (
				let year = oldestYear;
				year <= latestYear && year <= limitYear;
				year++
			) {
				const nextYear = year + 1;
				this.datePeriods.push(`Outubro/${year} - Setembro/${nextYear}`);
			}
		},

		fetchCollectionData(isInit = false) {
			if (!this.selectedOptions.propriedade || !this.selectedOptions.areaHomogenea || !this.selectedOptions.safra) {
				alert("Selecione todos os filtros necessários!");
				return;
			}

			const { propriedade, areaHomogenea, safra } = this.selectedOptions;
			const [startDate, endDate] = this.parseSelectedPeriod();

			axios
				.post(
					"/api/v1/get-collection-data",
					{ propertyId: propriedade, areaHomogeneaId: areaHomogenea, startDate, endDate },
					{ headers: { authorization: `bearer ${this.authToken}` } }
				)
				.then((response) => {
					this.collectionData = response.data; // Armazena os dados para uso no gráfico
					this.getVisitDates();
					this.fetchCollectionDataNext();
				})
				.catch((err) => {
					console.error("Erro ao obter dados:", err);
				});

		},

		fetchCollectionDataNext(){
			this.collectionDataNext = [];
			const { propriedade, areaHomogenea, safra } = this.selectedOptions;
			let safraIndex = 0;
			for(let i = 0; i < this.datePeriods.length; i++){
				if(this.datePeriods[i].coleta === this.selectedOptions.coleta){
					safraIndex = i;
					break;
				}
			}
			if(this.datePeriods.length >= safraIndex + 1){
				safraIndex++;
			}
			const periodParts = this.datePeriods[safraIndex].split(" - ");
			const startYear = periodParts[0].split("/")[1];
			const endYear = periodParts[1].split("/")[1];
			const startDate = `Outubro/${startYear}`;
			const endDate = `Setembro/${endYear}`;

			axios
				.post(
					"/api/v1/get-collection-data",
					{ propertyId: propriedade, areaHomogeneaId: areaHomogenea, startDate, endDate },
					{ headers: { authorization: `bearer ${this.authToken}` } }
				)
				.then((response) => {
					this.collectionDataNext = response.data; // Armazena os dados para uso no gráfico
					this.loading = false;
				})
				.catch((err) => {
					console.error("Erro ao obter dados:", err);
				});

		},

		parseSelectedPeriod() {
			if (!this.selectedOptions.safra) return ["", ""];

			const periodParts = this.selectedOptions.safra.split(" - ");
			const startYear = periodParts[0].split("/")[1];
			const endYear = periodParts[1].split("/")[1];

			return [`Outubro/${startYear}`, `Setembro/${endYear}`];
		},

		getVisitDates() {
			if (!this.collectionData || this.collectionData.length === 0) {
				console.warn("Nenhum dado de visita encontrado.");
				return;
			}
			this.visitDates = [];
			this.formatedVisitDates = [];
			//Percorre collectionData e pega as datas de visita, não duplicadas
			for (let i = 0; i < this.collectionData.length; i++) {
				const date = this.collectionData[i].date;
				if (!this.visitDates.includes(date)) {
					this.visitDates.push(date);
					this.formatedVisitDates.push(this.formatDate(date));
				}
			}
		},

		formatDate(dateString) {
			if (!dateString) return "";
			const date = new Date(dateString);
			const day = String(date.getDate()).padStart(2, '0');
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const year = date.getFullYear();
			return `${day}/${month}/${year}`;
		},

		getSelectedcoletaIndex() {
			for (let i = 0; i < this.formatedVisitDates.length; i++) {
				if (this.formatedVisitDates[i] === this.selectedOptions.coleta) {
					return i;
				}
			}
		},

		setTotalbobbins() {
			this.totalbobbins = 0;
			const coletaIndex = this.getSelectedcoletaIndex();

			for (let i = 0; i < this.collectionData.length; i++) {
				const dataColeta = this.collectionData[i].date;

				if (dataColeta === this.visitDates[coletaIndex]) {
					const treeVisit = this.collectionData[i];
					this.totalbobbins += treeVisit.bobbin.total;
				}
			}
		},

		getTotalsForRange(index, treeVisit){
			let results = {
				bobbins: 0,
				piece: 0,
				loss: 0,
				witchsBroom: 0,
				rat: 0,
				rotten: 0,
				harvested: 0,
			};

			switch(index){
				case 0:
					results.bobbins = this.totalbobbins;
					break;
				case 1:
					results.piece = treeVisit.small.piece;
					results.loss = treeVisit.small.loss;
					results.witchsBroom = treeVisit.small.witchs_broom;
					break;
				case 2:
					results.piece = treeVisit.medium.piece;
					results.loss = treeVisit.medium.loss;
					break;
				case 3:
					results.piece = treeVisit.medium2.piece;
					results.loss = treeVisit.medium2.loss;
					results.witchsBroom = treeVisit.medium2.witchs_broom;
					results.rat = treeVisit.medium2.rat;
					results.rotten = treeVisit.medium2.rotten;
					break;
				case 4:
					results.piece = treeVisit.medium3.piece;
					results.loss = treeVisit.medium3.loss;
					results.witchsBroom = treeVisit.medium3.witchs_broom;
					results.rat = treeVisit.medium3.rat;
					results.rotten = treeVisit.medium3.rotten;
					results.harvested = treeVisit.medium3.harvested;
					break;
				case 5:
					results.loss = treeVisit.adult.loss;
					results.witchsBroom = treeVisit.adult.witchs_broom;
					results.rat = treeVisit.adult.rat;
					results.rotten = treeVisit.adult.rotten;
					results.harvested = treeVisit.adult.harvested;
					break;
				case 6:
					results.loss = treeVisit.adult2.loss;
					results.witchsBroom = treeVisit.adult2.witchs_broom;
					results.rat = treeVisit.adult2.rat;
					results.rotten = treeVisit.adult2.rotten;
					results.harvested = treeVisit.adult2.harvested;
					break;
				case 7:
					results.loss = treeVisit.mature.loss;
					results.witchsBroom = treeVisit.mature.witchs_broom;
					results.rat = treeVisit.mature.rat;
					results.rotten = treeVisit.mature.rotten;
					results.harvested = treeVisit.mature.harvested;
					break;
				case 8:
					results.loss = treeVisit.mature2.loss;
					results.witchsBroom = treeVisit.mature2.witchs_broom;
					results.rat = treeVisit.mature2.rat;
					results.rotten = treeVisit.mature2.rotten;
					results.harvested = treeVisit.mature2.harvested;
					break;
				case 9:
					results.loss = treeVisit.mature3.loss;
					results.witchsBroom = treeVisit.mature3.witchs_broom;
					results.rat = treeVisit.mature3.rat;
					results.rotten = treeVisit.mature3.rotten;
					results.harvested = treeVisit.mature3.harvested;
					break;
				case 10:
					results.loss = treeVisit.mature4.loss;
					results.witchsBroom = treeVisit.mature4.witchs_broom;
					results.rat = treeVisit.mature4.rat;
					results.rotten = treeVisit.mature4.rotten;
					results.harvested = treeVisit.mature4.harvested;
					break;
				default:
					console.error("Invalid index:", index);
					break;
			}
			return results;
		},

		setResultsSafra() {
			this.chartData.labels = [];
			this.chartData.datasets = [];
			this.loading = true;
			this.resultsSafra = new Map();
			this.setTotalbobbins();

			if (this.totalbobbins === 0) {
				const coletaIndex = this.getSelectedcoletaIndex();
				const startDate = this.visitDates[coletaIndex] || "";

				this.resultsSafraArray = [{
					date: startDate,
					bobbins: 0,
					piece: 0,
					loss: 0,
					witchsBroom: 0,
					rat: 0,
					rotten: 0,
					harvested: 0
				}];

				this.updateChartTitle();
				this.updateChartDataLabels();
				this.updateChartDatasets();
				this.chartKey++;
				this.loading = false;
				return;
			}

			const coletaIndex = this.getSelectedcoletaIndex();
			const startDate = this.visitDates[coletaIndex];

			const groupedByDate = this.groupVisitsByDate(this.collectionData);
			const orderedDates = Array.from(groupedByDate.keys()).sort();
			const startIndex = orderedDates.indexOf(startDate);

			if (startIndex === -1) {
				console.error("Data de coleta selecionada não encontrada em collectionData.");
				return;
			}

			let faixa = 0;
			const acumulate = {
				piece: 0,
				loss: 0,
				witchsBroom: 0,
				rat: 0,
				rotten: 0,
				harvested: 0,
			};

			faixa = this.processSafra(groupedByDate, orderedDates, startIndex + 1, 11, faixa, acumulate);

			if (faixa < 11) {
				const groupedByDateNext = this.groupVisitsByDate(this.collectionDataNext);
				const orderedDatesNext = Array.from(groupedByDateNext.keys()).sort();

				this.processSafra(groupedByDateNext, orderedDatesNext, 0, 11, faixa, acumulate);
			}


						// Transforma Map em array
			const originalArray = Array.from(this.resultsSafra.entries()).map(([date, data]) => ({
				date,
				...data
			}));

			// Prepara o novo array com valores deslocados
			const shiftedArray = [];

			// Adiciona a primeira posição com bobbins cheio e os demais zerados
			if (originalArray.length > 0) {
				shiftedArray.push({
					date: originalArray[0].date,
					bobbins: this.totalbobbins,
					piece: 0,
					loss: 0,
					witchsBroom: 0,
					rat: 0,
					rotten: 0,
					harvested: 0
				});
			}

			// Adiciona os valores deslocados (desconsidera o último)
			for (let i = 0; i < originalArray.length - 1; i++) {
				const next = originalArray[i];
				shiftedArray.push({
					date: originalArray[i + 1].date,
					bobbins: next.bobbins,
					piece: next.piece,
					loss: next.loss,
					witchsBroom: next.witchsBroom,
					rat: next.rat,
					rotten: next.rotten,
					harvested: next.harvested
				});
			}

			this.resultsSafraArray = shiftedArray;

			console.log(this.resultsSafraArray);

			this.updateChartTitle();
			this.updateChartDataLabels();
			this.updateChartDatasets();
			this.chartKey++;
			this.loading = false;
		},

		groupVisitsByDate(visits) {
			const grouped = new Map();
			for (const visit of visits) {
				const date = visit.date;
				if (!grouped.has(date)) grouped.set(date, []);
				grouped.get(date).push(visit);
			}
			return grouped;
		},

		processSafra(groupedByDate, orderedDates, startIndex, maxFaixa, faixaInicial, acumulate) {
			let faixa = faixaInicial;

			for (let i = startIndex; i < orderedDates.length && faixa < maxFaixa; i++) {
				const currentDate = orderedDates[i];
				const visits = groupedByDate.get(currentDate);

				for (const visit of visits) {
					const result = this.getTotalsForRange(faixa, visit);

					if (!this.resultsSafra.has(currentDate)) {
						this.resultsSafra.set(currentDate, {
							bobbins: faixa === 0 ? this.totalbobbins : 0,
							...result
						});
					} else {
						const existing = this.resultsSafra.get(currentDate);
						Object.keys(result).forEach(key => {
							existing[key] += result[key];
						});
					}
				}
				faixa++;
			}

			// Acumula resultados por data (continua de onde parou)
			for (let i = startIndex; i < orderedDates.length; i++) {
				const date = orderedDates[i];
				if (this.resultsSafra.has(date)) {
					const data = this.resultsSafra.get(date);

					acumulate.piece += data.piece;
					acumulate.loss += data.loss;
					acumulate.witchsBroom += data.witchsBroom;
					acumulate.rat += data.rat;
					acumulate.rotten += data.rotten;
					acumulate.harvested += data.harvested;

					data.piece = acumulate.piece;
					data.loss = acumulate.loss;
					data.witchsBroom = acumulate.witchsBroom;
					data.rat = acumulate.rat;
					data.rotten = acumulate.rotten;
					data.harvested = acumulate.harvested;

					const totalFinished = data.piece + data.loss + data.witchsBroom + data.rat + data.rotten + data.harvested;
					data.bobbins = Math.max(0, this.totalbobbins - totalFinished);

					this.resultsSafra.set(date, data);
				}
			}

			return faixa;
		}




	}
};
