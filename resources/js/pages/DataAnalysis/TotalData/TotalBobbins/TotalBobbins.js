import axios from "axios";
import <PERSON><PERSON>hart from '../../../../components/BarChart.vue';

	export default {
	components: {
		BarChart
	},
	data() {
		return {
			authToken: null,
			properties: [],
			homogeneousAreas: [],
			selectedOptions: {
				propriedade: "",
				areaHomogenea: "",
				safra: "",
				coleta: "",
			},
			loading: true,
			datePeriods: [],
			startDate: "",
			endDate: "",
			faixas: [
				"0 - 21", "21 - 42", "42 - 63", "63 - 84", "84 - 105",
				"105 - 126", "126 - 147", "147 - 168", "168 - 189", "189 - 210", "> 210"
			],
			collectionData: [],
			visitDates: [],
			formatedVisitDates: [],
			totalbobbins: 0,
			resultsSafra: [],
			resultsSafraArray: [],
			collectionDataNext: [],

			chartKey: 0,
			chartData: {
				labels: [],
				datasets: [],
			},
			chartOptions: {
				responsive: true,
				maintainAspectRatio: false, // Melhora a renderização
				legend: { position: 'top' },
				title: null,
				scales: {
				xAxes: [{ stacked: true }],
				yAxes: [{ stacked: true }]
				}
			}
		};
	},

	computed: {
		totalProperties() {
			return this.properties.length > 0;
		}
	},

		watch: {
		"selectedOptions.propriedade": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				// Quando a propriedade mudar, limpar os campos AH e Safra
				this.selectedOptions.areaHomogenea = "";
				this.selectedOptions.safra = "";
				this.homogeneousAreas = [];
				this.datePeriods = [];
				this.fetchHomogeneousAreas();
				this.fetchVisitDates();
			}
		},

		"selectedOptions.areaHomogenea": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				this.selectedOptions.safra = "";
				this.selectedOptions.coleta = "";
				this.datePeriods = [];
				this.fetchVisitDates();
			}
		},

		"selectedOptions.safra": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				this.selectedOptions.coleta = "";
				this.fetchCollectionData();
			}
		},
	},

	created() {
		this.authToken = window.token;
		this.fetchProperties();
	},

	methods: {
		// Métodos removidos - processamento movido para o backend

		fetchProperties() {
			this.loading = true;
			axios
				.get("/api/v1/properties", {
					headers: { authorization: `bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						this.properties = response.data.data;
						if (this.properties.length > 0) {
							this.selectedOptions.propriedade = this.properties[0].id;
							this.fetchHomogeneousAreas(true); // Passa true para diferenciar a inicialização
							this.fetchVisitDates(true);
						}
					}
				})
				.catch((err) => {
					console.log(err);
					this.loading = false;
				});
		},

		fetchHomogeneousAreas(isInit = false) {
			if (!this.selectedOptions.propriedade) return;

			this.loading = true;
			axios
				.get(`/api/v1/homogeneous-area?propid=${this.selectedOptions.propriedade}`, {
					headers: { authorization: `Bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						this.homogeneousAreas = response.data.data;
						if (isInit && this.homogeneousAreas.length > 0) {
							this.selectedOptions.areaHomogenea = this.homogeneousAreas[0].id;
						}
					}
				})
				.catch((err) => {
					console.error(err);
					this.loading = false;
				});
		},

		async fetchVisitDates(isInit = false) {
			try {
				const response = await axios.get("/api/v1/get-visit-dates", {
					headers: { authorization: `bearer ${this.authToken}` },
				});
				const { oldestVisit, latestVisit } = response.data;

				if (oldestVisit && latestVisit) {
					this.generateDateOptions(oldestVisit, latestVisit);
					if (isInit && this.datePeriods.length > 0) {
						this.selectedOptions.safra = this.datePeriods[this.datePeriods.length - 1];
						this.fetchCollectionData();
					}
				} else {
					this.datePeriods = [];
				}
			} catch (error) {
				if (error.response && error.response.status === 404) {
					this.datePeriods = [];
				} else {
					console.error(error);
				}
			}
		},

		generateDateOptions(oldestVisit, latestVisit) {
			const oldestYear = new Date(oldestVisit).getFullYear();
			const latestYear = new Date(latestVisit).getFullYear();
			const currentYear = new Date().getFullYear();
			const currentMonth = new Date().getMonth();
			const limitYear = currentMonth <= 8 ? currentYear - 1 : currentYear; // Ajuste aqui

			this.datePeriods = [];

			for (
				let year = oldestYear;
				year <= latestYear && year <= limitYear;
				year++
			) {
				const nextYear = year + 1;
				this.datePeriods.push(`Outubro/${year} - Setembro/${nextYear}`);
			}
		},

		fetchCollectionData() {
			if (!this.selectedOptions.propriedade || !this.selectedOptions.areaHomogenea || !this.selectedOptions.safra) {
				alert("Selecione todos os filtros necessários!");
				return;
			}

			const { propriedade, areaHomogenea } = this.selectedOptions;
			const [startDate, endDate] = this.parseSelectedPeriod();

			axios
				.post(
					"/api/v1/get-collection-dates",
					{ propertyId: propriedade, areaHomogeneaId: areaHomogenea, startDate, endDate },
					{ headers: { authorization: `bearer ${this.authToken}` } }
				)
				.then((response) => {
					this.visitDates = response.data;
					this.formatedVisitDates = this.visitDates.map(this.formatDate);
					this.loading = false;
				})
				.catch((err) => {
					console.error("Erro ao obter dados:", err);
				});

		},

		fetchCollectionDataNext(){
			this.collectionDataNext = [];
			const { propriedade, areaHomogenea } = this.selectedOptions;
			let safraIndex = 0;
			for(let i = 0; i < this.datePeriods.length; i++){
				if(this.datePeriods[i].coleta === this.selectedOptions.coleta){
					safraIndex = i;
					break;
				}
			}
			if(this.datePeriods.length >= safraIndex + 1){
				safraIndex++;
			}
			const periodParts = this.datePeriods[safraIndex].split(" - ");
			const startYear = periodParts[0].split("/")[1];
			const endYear = periodParts[1].split("/")[1];
			const startDate = `Outubro/${startYear}`;
			const endDate = `Setembro/${endYear}`;

			axios
				.post(
					"/api/v1/get-collection-data",
					{ propertyId: propriedade, areaHomogeneaId: areaHomogenea, startDate, endDate },
					{ headers: { authorization: `bearer ${this.authToken}` } }
				)
				.then((response) => {
					this.collectionDataNext = response.data; // Armazena os dados para uso no gráfico
					this.loading = false;
				})
				.catch((err) => {
					console.error("Erro ao obter dados:", err);
				});

		},

		parseSelectedPeriod() {
			if (!this.selectedOptions.safra) return ["", ""];

			const periodParts = this.selectedOptions.safra.split(" - ");
			const startYear = periodParts[0].split("/")[1];
			const endYear = periodParts[1].split("/")[1];

			return [`Outubro/${startYear}`, `Setembro/${endYear}`];
		},

		getVisitDates() {
			if (!this.collectionData || this.collectionData.length === 0) {
				console.warn("Nenhum dado de visita encontrado.");
				return;
			}
			this.visitDates = [];
			this.formatedVisitDates = [];
			//Percorre collectionData e pega as datas de visita, não duplicadas
			for (let i = 0; i < this.collectionData.length; i++) {
				const date = this.collectionData[i].date;
				if (!this.visitDates.includes(date)) {
					this.visitDates.push(date);
					this.formatedVisitDates.push(this.formatDate(date));
				}
			}
		},

		formatDate(dateString) {
			if (!dateString) return "";
			const date = new Date(dateString);
			const day = String(date.getDate()).padStart(2, '0');
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const year = date.getFullYear();
			return `${day}/${month}/${year}`;
		},

		getSelectedcoletaIndex() {
			for (let i = 0; i < this.formatedVisitDates.length; i++) {
				if (this.formatedVisitDates[i] === this.selectedOptions.coleta) {
					return i;
				}
			}
		},

		// Métodos removidos - processamento movido para o backend

		async setResultsSafra() {
			if (!this.selectedOptions.propriedade ||
				!this.selectedOptions.areaHomogenea ||
				!this.selectedOptions.safra ||
				!this.selectedOptions.coleta) {
				alert("Selecione todos os filtros necessários!");
				return;
			}

			this.loading = true;

			try {
				const { propriedade, areaHomogenea, coleta } = this.selectedOptions;
				const [startDate, endDate] = this.parseSelectedPeriod();

				const response = await axios.post(
					"/api/v1/get-total-bobbins-by-period",
					{
						propertyId: propriedade,
						areaHomogeneaId: areaHomogenea,
						startDate,
						endDate,
						coletaDate: coleta
					},
					{ headers: { authorization: `bearer ${this.authToken}` } }
				);

				if (response.data.success) {
					// Atualizar dados do gráfico com a resposta do backend
					const data = response.data.data;

					this.chartData.labels = data.labels;
					this.chartData.datasets = data.datasets;
					this.totalbobbins = data.totalBobbins;

					// Atualizar título do gráfico
					this.chartOptions.title = {
						display: true,
						text: data.chartTitle,
						fontSize: 32,
						fontStyle: 'bold'
					};

					// Forçar re-render do gráfico
					this.chartKey++;
				} else {
					console.error("Erro na resposta do servidor:", response.data);
					alert("Erro ao processar os dados no servidor");
				}
			} catch (error) {
				console.error("Erro ao obter dados dos bilros:", error);
				alert("Erro ao carregar os dados. Tente novamente.");
			} finally {
				this.loading = false;
			}
		},
	}
};
