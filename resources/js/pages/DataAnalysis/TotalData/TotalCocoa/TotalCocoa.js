import axios from "axios";
import Bar<PERSON>hart from '../../../../components/BarChart.vue';


	export default {
	components: {
		BarChart
	},
	data() {
		return {
			authToken: null,
			properties: [],
			homogeneousAreas: [],
			selectedOptions: {
				propriedade: "",
				areaHomogenea: "",
				safra: "",
				faixaInicial: "",
				faixaFinal: "",
				coletaInicial: "",
				coletaFinal: ""
			},
			loading: true,
			datePeriods: [],
			startDate: "",
			endDate: "",
			faixas: [
				"0 - 21", "21 - 42", "42 - 63", "63 - 84", "84 - 105",
				"105 - 126", "126 - 147", "147 - 168", "168 - 189", "189 - 210", "> 210"
			],
			faixaFinalOptions: [],
			collectionData: [],
			visitDates: [],
			lastVisitDates: [],
			formatedVisitDates: [],
			formatedLastVisitDates: [],
			totalCocoa: [],

			chartKey: 0,
			chartData: {
				labels: [],
				datasets: [],
			},
			chartOptions: {
				responsive: true,
				maintainAspectRatio: false, // Melhora a renderização
				legend: { position: 'top' },
				scales: {
				xAxes: [{ stacked: false }],
				yAxes: [{ stacked: false }]
				}
			}
		};
	},

	computed: {
		totalProperties() {
			return this.properties.length > 0;
		}
	},

		watch: {
		"selectedOptions.propriedade": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				// Quando a propriedade mudar, limpar os campos AH e Safra
				this.selectedOptions.areaHomogenea = "";
				this.selectedOptions.safra = "";
				this.homogeneousAreas = [];
				this.datePeriods = [];
				this.fetchHomogeneousAreas();
				this.fetchVisitDates();
			}
		},

		"selectedOptions.areaHomogenea": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				this.selectedOptions.safra = "";
				this.selectedOptions.coletaInicial = "";
				this.selectedOptions.coletaFinal = "";
				this.selectedOptions.faixaInicial = "";
				this.selectedOptions.faixaFinal = "";
				this.datePeriods = [];
				this.fetchVisitDates();
			}
		},

		"selectedOptions.safra": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				this.selectedOptions.coletaInicial = "";
				this.selectedOptions.coletaFinal = "";
				this.selectedOptions.faixaInicial = "";
				this.selectedOptions.faixaFinal = "";
				this.fetchVisitDates();
				this.fetchCollectionData();
			}
		},

		"selectedOptions.coletaInicial": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				this.updateLastVisitDates();
			}
		},

		"selectedOptions.faixaInicial": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				this.updateFaixaFinalOptions();
			}
		},
	},

	created() {
		this.authToken = window.token;
		this.fetchProperties();
		this.selectedOptions.faixaInicial = this.faixas[0];
        this.updateFaixaFinalOptions();
        if (this.faixaFinalOptions.length > 0) {
            this.selectedOptions.faixaFinal = this.faixaFinalOptions[1];
        }
	},

	methods: {
		updateChartDataLabels() {
			this.chartData.labels = [];
			for (const date of this.totalCocoa.keys()) {
				this.chartData.labels.push(this.formatDate(date));
			}
		},

		updateChartDataDatasets() {
			this.chartData.datasets = [];

			if (this.totalCocoa.size === 0) return;

			const firstEntry = this.totalCocoa.values().next().value;
			if (!firstEntry) return;

			for (let i = 0; i < firstEntry.length; i++) {
				this.chartData.datasets.push({
					label: this.faixas[this.getSelectedFaixaInicialIndex() + i],
					backgroundColor: this.getColor(i),
					data: []
				});
			}

			for (const values of this.totalCocoa.values()) {
				values.forEach((value, index) => {
					this.chartData.datasets[index].data.push(value);
				});
			}
		},

		getColor(index) {
			const colors = ["orange", "green", "yellow", "red", "blue", "purple", "brown", "pink", "gray", "cyan", "black"];
			return colors[index % colors.length];
		},

		updateFaixaFinalOptions() {
			const selectedIndex = this.selectedOptions.faixaInicial
				? this.faixas.indexOf(this.selectedOptions.faixaInicial)
				: 0;
			this.faixaFinalOptions = this.faixas.slice(selectedIndex);
			this.selectedOptions.faixaFinal = "";
		},

		fetchProperties() {
			this.loading = true;
			axios
				.get("/api/v1/properties", {
					headers: { authorization: `bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						this.properties = response.data.data;
						if (this.properties.length > 0) {
							this.selectedOptions.propriedade = this.properties[0].id;
							this.fetchHomogeneousAreas(true); // Passa true para diferenciar a inicialização
							this.fetchVisitDates(true);
						}
					}
				})
				.catch((err) => {
					console.log(err);
					this.loading = false;
				});
		},

		fetchHomogeneousAreas(isInit = false) {
			if (!this.selectedOptions.propriedade) return;

			this.loading = true;
			axios
				.get(`/api/v1/homogeneous-area?propid=${this.selectedOptions.propriedade}`, {
					headers: { authorization: `Bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						this.homogeneousAreas = response.data.data;
						if (isInit && this.homogeneousAreas.length > 0) {
							this.selectedOptions.areaHomogenea = this.homogeneousAreas[0].id;
						}
					}
				})
				.catch((err) => {
					console.error(err);
					this.loading = false;
				});
		},

		async fetchVisitDates(isInit = false) {
			try {
				const response = await axios.get("/api/v1/get-visit-dates", {
					headers: { authorization: `bearer ${this.authToken}` },
				});
				const { oldestVisit, latestVisit } = response.data;

				if (oldestVisit && latestVisit) {
					this.generateDateOptions(oldestVisit, latestVisit);
					if (isInit && this.datePeriods.length > 0) {
						this.selectedOptions.safra = this.datePeriods[this.datePeriods.length - 1];
						this.fetchCollectionData(isInit);
					}
				} else {
					this.datePeriods = [];
				}
			} catch (error) {
				if (error.response && error.response.status === 404) {
					this.datePeriods = [];
				} else {
					console.error(error);
				}
			}
		},

		generateDateOptions(oldestVisit, latestVisit) {
			const oldestYear = new Date(oldestVisit).getFullYear();
			const latestYear = new Date(latestVisit).getFullYear();
			const currentYear = new Date().getFullYear();
			const currentMonth = new Date().getMonth();
			const limitYear = currentMonth <= 8 ? currentYear - 1 : currentYear; // Ajuste aqui

			this.datePeriods = [];

			for (
				let year = oldestYear;
				year <= latestYear && year <= limitYear;
				year++
			) {
				const nextYear = year + 1;
				this.datePeriods.push(`Outubro/${year} - Setembro/${nextYear}`);
			}
		},

		fetchCollectionData(isInit = false) {
			if (!this.selectedOptions.propriedade || !this.selectedOptions.areaHomogenea || !this.selectedOptions.safra) {
				alert("Selecione todos os filtros necessários!");
				return;
			}

			const { propriedade, areaHomogenea, safra } = this.selectedOptions;
			const [startDate, endDate] = this.parseSelectedPeriod();

			axios
				.post(
					"/api/v1/get-collection-data",
					{ propertyId: propriedade, areaHomogeneaId: areaHomogenea, startDate, endDate },
					{ headers: { authorization: `bearer ${this.authToken}` } }
				)
				.then((response) => {
					this.collectionData = response.data; // Armazena os dados para uso no gráfico
					this.getVisitDates();
					this.updateLastVisitDates();
					this.loading = false;
				})
				.catch((err) => {
					console.error("Erro ao obter dados:", err);
				});

		},

		parseSelectedPeriod() {
			if (!this.selectedOptions.safra) return ["", ""];

			const periodParts = this.selectedOptions.safra.split(" - ");
			const startYear = periodParts[0].split("/")[1];
			const endYear = periodParts[1].split("/")[1];

			return [`Outubro/${startYear}`, `Setembro/${endYear}`];
		},

		getVisitDates() {
			if (!this.collectionData || this.collectionData.length === 0) {
				console.warn("Nenhum dado de visita encontrado.");
				return;
			}
			this.visitDates = [];
			this.formatedVisitDates = [];
			//Percorre collectionData e pega as datas de visita, não duplicadas
			for (let i = 0; i < this.collectionData.length; i++) {
				const date = this.collectionData[i].date;
				if (!this.visitDates.includes(date)) {
					this.visitDates.push(date);
					this.formatedVisitDates.push(this.formatDate(date));
				}
			}
		},

		updateLastVisitDates() {
			const coletaInicialIndex = this.selectedOptions.coletaInicial
				? this.getSelectedColetaInicialIndex()
				: 0;
			this.lastVisitDates = [];
			this.formatedLastVisitDates = [];
			for (let i = coletaInicialIndex; i < this.visitDates.length; i++) {
				this.lastVisitDates.push(this.visitDates[i]);
				this.formatedLastVisitDates.push(this.formatedVisitDates[i]);
			}
			this.selectedOptions.coletaFinal = "";
		},

		formatDate(dateString) {
			if (!dateString) return "";
			const date = new Date(dateString);
			const day = String(date.getDate()).padStart(2, '0');
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const year = date.getFullYear();
			return `${day}/${month}/${year}`;
		},

		getSelectedFaixaInicialIndex() {
			for (let i = 0; i < this.faixas.length; i++) {
				if (this.faixas[i] === this.selectedOptions.faixaInicial) {
					return i;
				}
			}
		},

		getSelectedFaixaFinalIndex() {
			for (let i = 0; i < this.faixaFinalOptions.length; i++) {
				if (this.faixaFinalOptions[i] === this.selectedOptions.faixaFinal) {
					return i;
				}
			}
		},

		getSelectedColetaInicialIndex() {
			for (let i = 0; i < this.formatedVisitDates.length; i++) {
				if (this.formatedVisitDates[i] === this.selectedOptions.coletaInicial) {
					return i;
				}
			}
		},

		getSelectedColetaFinalIndex() {
			for (let i = 0; i < this.formatedVisitDates.length; i++) {
				if (this.formatedVisitDates[i] === this.selectedOptions.coletaFinal) {
					return i;
				}
			}
		},

		getTotalCocoaForRange(index, treeVisit){
			switch(index){
				case 0:
					return treeVisit.bobbin.total;
				case 1:
					return treeVisit.small.total;
				case 2:
					return treeVisit.medium.total;
				case 3:
					return treeVisit.medium2.total;
				case 4:
					return treeVisit.medium3.total;
				case 5:
					return treeVisit.adult.total;
				case 6:
					return treeVisit.adult2.total;
				case 7:
					return treeVisit.mature.total;
				case 8:
					return treeVisit.mature2.total;
				case 9:
					return treeVisit.mature3.total;
				case 10:
					return treeVisit.mature4.total;
				default:
					return 0;
			}
		},

		getTotalCocoaForPeriod() {
			this.chartData.labels = [];
			this.chartData.datasets = [];
			this.loading = true;
			this.totalCocoa = new Map();
			const faixaInicialIndex = this.getSelectedFaixaInicialIndex();
			const faixaFinalIndex = this.getSelectedFaixaFinalIndex();
			const coletaInicialIndex = this.getSelectedColetaInicialIndex();
			const coletaFinalIndex = this.getSelectedColetaFinalIndex();

			for (let i = 0; i < this.collectionData.length; i++) {
				const dataColeta = this.collectionData[i].date;

				if (dataColeta >= this.visitDates[coletaInicialIndex] &&
					dataColeta <= this.visitDates[coletaFinalIndex]) {

					// Obtém ou inicializa um array de totais para essa data
					if (!this.totalCocoa.has(dataColeta)) {
						this.totalCocoa.set(dataColeta, new Array((faixaFinalIndex+1) - faixaInicialIndex).fill(0));
					}

					const totalCocoaForRange = this.totalCocoa.get(dataColeta);

					for (let j = faixaInicialIndex; j <= faixaFinalIndex; j++) {
						const total = this.getTotalCocoaForRange(j, this.collectionData[i]) || 0;
						totalCocoaForRange[j - faixaInicialIndex] += total;
					}
				}
			}
			this.updateChartDataLabels();
			this.updateChartDataDatasets();
			this.chartKey++;
			this.loading = false;
		}

	}
};
