import axios from "axios";
import Bar<PERSON>hart from '../../../../components/BarChart.vue';


	export default {
	components: {
		BarChart
	},
	data() {
		return {
			authToken: null,
			properties: [],
			homogeneousAreas: [],
			selectedOptions: {
				propriedade: "",
				areaHomogenea: "",
				safra: "",
				faixaInicial: "",
				faixaFinal: "",
				coletaInicial: "",
				coletaFinal: ""
			},
			loading: true,
			datePeriods: [],
			startDate: "",
			endDate: "",
			faixas: [
				"0 - 21", "21 - 42", "42 - 63", "63 - 84", "84 - 105",
				"105 - 126", "126 - 147", "147 - 168", "168 - 189", "189 - 210", "> 210"
			],
			faixaFinalOptions: [],
			collectionData: [],
			visitDates: [],
			lastVisitDates: [],
			formatedVisitDates: [],
			formatedLastVisitDates: [],
			totalCocoa: [],

			chartKey: 0,
			chartData: {
				labels: [],
				datasets: [],
			},
			chartOptions: {
				responsive: true,
				maintainAspectRatio: false, // Melhora a renderização
				legend: { position: 'top' },
				scales: {
				xAxes: [{ stacked: false }],
				yAxes: [{ stacked: false }]
				}
			}
		};
	},

	computed: {
		totalProperties() {
			return this.properties.length > 0;
		}
	},

		watch: {
		"selectedOptions.propriedade": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				// Quando a propriedade mudar, limpar os campos AH e Safra
				this.selectedOptions.areaHomogenea = "";
				this.selectedOptions.safra = "";
				this.homogeneousAreas = [];
				this.datePeriods = [];
				this.fetchHomogeneousAreas();
				this.fetchVisitDates();
			}
		},

		"selectedOptions.areaHomogenea": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				this.selectedOptions.safra = "";
				this.selectedOptions.coletaInicial = "";
				this.selectedOptions.coletaFinal = "";
				this.selectedOptions.faixaInicial = "";
				this.selectedOptions.faixaFinal = "";
				this.datePeriods = [];
				this.fetchVisitDates();
			}
		},

		"selectedOptions.safra": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				this.selectedOptions.coletaInicial = "";
				this.selectedOptions.coletaFinal = "";
				this.selectedOptions.faixaInicial = "";
				this.selectedOptions.faixaFinal = "";
				this.fetchVisitDates();
				this.fetchCollectionData();
			}
		},

		"selectedOptions.coletaInicial": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				this.updateLastVisitDates();
			}
		},

		"selectedOptions.faixaInicial": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				this.updateFaixaFinalOptions();
			}
		},
	},

	created() {
		this.authToken = window.token;
		this.fetchProperties();
		this.selectedOptions.faixaInicial = this.faixas[0];
        this.updateFaixaFinalOptions();
        if (this.faixaFinalOptions.length > 0) {
            this.selectedOptions.faixaFinal = this.faixaFinalOptions[1];
        }
	},

	methods: {
		// Métodos removidos - processamento movido para o backend

		updateFaixaFinalOptions() {
			const selectedIndex = this.selectedOptions.faixaInicial
				? this.faixas.indexOf(this.selectedOptions.faixaInicial)
				: 0;
			this.faixaFinalOptions = this.faixas.slice(selectedIndex);
			this.selectedOptions.faixaFinal = "";
		},

		fetchProperties() {
			this.loading = true;
			axios
				.get("/api/v1/properties", {
					headers: { authorization: `bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						this.properties = response.data.data;
						if (this.properties.length > 0) {
							this.selectedOptions.propriedade = this.properties[0].id;
							this.fetchHomogeneousAreas(true); // Passa true para diferenciar a inicialização
							this.fetchVisitDates(true);
						}
					}
				})
				.catch((err) => {
					console.log(err);
					this.loading = false;
				});
		},

		fetchHomogeneousAreas(isInit = false) {
			if (!this.selectedOptions.propriedade) return;

			this.loading = true;
			axios
				.get(`/api/v1/homogeneous-area?propid=${this.selectedOptions.propriedade}`, {
					headers: { authorization: `Bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						this.homogeneousAreas = response.data.data;
						if (isInit && this.homogeneousAreas.length > 0) {
							this.selectedOptions.areaHomogenea = this.homogeneousAreas[0].id;
						}
					}
				})
				.catch((err) => {
					console.error(err);
					this.loading = false;
				});
		},

		async fetchVisitDates(isInit = false) {
			try {
				const response = await axios.get("/api/v1/get-visit-dates", {
					headers: { authorization: `bearer ${this.authToken}` },
				});
				const { oldestVisit, latestVisit } = response.data;

				if (oldestVisit && latestVisit) {
					this.generateDateOptions(oldestVisit, latestVisit);
					if (isInit && this.datePeriods.length > 0) {
						this.selectedOptions.safra = this.datePeriods[this.datePeriods.length - 1];
						this.fetchCollectionData();
					}
				} else {
					this.datePeriods = [];
				}
			} catch (error) {
				if (error.response && error.response.status === 404) {
					this.datePeriods = [];
				} else {
					console.error(error);
				}
			}
		},

		generateDateOptions(oldestVisit, latestVisit) {
			const oldestYear = new Date(oldestVisit).getFullYear();
			const latestYear = new Date(latestVisit).getFullYear();
			const currentYear = new Date().getFullYear();
			const currentMonth = new Date().getMonth();
			const limitYear = currentMonth <= 8 ? currentYear - 1 : currentYear; // Ajuste aqui

			this.datePeriods = [];

			for (
				let year = oldestYear;
				year <= latestYear && year <= limitYear;
				year++
			) {
				const nextYear = year + 1;
				this.datePeriods.push(`Outubro/${year} - Setembro/${nextYear}`);
			}
		},

		fetchCollectionData() {
			if (!this.selectedOptions.propriedade || !this.selectedOptions.areaHomogenea || !this.selectedOptions.safra) {
				alert("Selecione todos os filtros necessários!");
				return;
			}

			const { propriedade, areaHomogenea } = this.selectedOptions;
			const [startDate, endDate] = this.parseSelectedPeriod();

			axios
				.post(
					"/api/v1/get-collection-data",
					{ propertyId: propriedade, areaHomogeneaId: areaHomogenea, startDate, endDate },
					{ headers: { authorization: `bearer ${this.authToken}` } }
				)
				.then((response) => {
					this.collectionData = response.data; // Armazena os dados para uso no gráfico
					this.getVisitDates();
					this.updateLastVisitDates();
					this.loading = false;
				})
				.catch((err) => {
					console.error("Erro ao obter dados:", err);
				});

		},

		parseSelectedPeriod() {
			if (!this.selectedOptions.safra) return ["", ""];

			const periodParts = this.selectedOptions.safra.split(" - ");
			const startYear = periodParts[0].split("/")[1];
			const endYear = periodParts[1].split("/")[1];

			return [`Outubro/${startYear}`, `Setembro/${endYear}`];
		},

		getVisitDates() {
			if (!this.collectionData || this.collectionData.length === 0) {
				console.warn("Nenhum dado de visita encontrado.");
				return;
			}
			this.visitDates = [];
			this.formatedVisitDates = [];
			//Percorre collectionData e pega as datas de visita, não duplicadas
			for (let i = 0; i < this.collectionData.length; i++) {
				const date = this.collectionData[i].date;
				if (!this.visitDates.includes(date)) {
					this.visitDates.push(date);
					this.formatedVisitDates.push(this.formatDate(date));
				}
			}
		},

		updateLastVisitDates() {
			const coletaInicialIndex = this.selectedOptions.coletaInicial
				? this.getSelectedColetaInicialIndex()
				: 0;
			this.lastVisitDates = [];
			this.formatedLastVisitDates = [];
			for (let i = coletaInicialIndex; i < this.visitDates.length; i++) {
				this.lastVisitDates.push(this.visitDates[i]);
				this.formatedLastVisitDates.push(this.formatedVisitDates[i]);
			}
			this.selectedOptions.coletaFinal = "";
		},

		formatDate(dateString) {
			if (!dateString) return "";
			const date = new Date(dateString);
			const day = String(date.getDate()).padStart(2, '0');
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const year = date.getFullYear();
			return `${day}/${month}/${year}`;
		},

		getSelectedFaixaInicialIndex() {
			for (let i = 0; i < this.faixas.length; i++) {
				if (this.faixas[i] === this.selectedOptions.faixaInicial) {
					return i;
				}
			}
		},

		getSelectedFaixaFinalIndex() {
			for (let i = 0; i < this.faixaFinalOptions.length; i++) {
				if (this.faixaFinalOptions[i] === this.selectedOptions.faixaFinal) {
					return i;
				}
			}
		},

		getSelectedColetaInicialIndex() {
			for (let i = 0; i < this.formatedVisitDates.length; i++) {
				if (this.formatedVisitDates[i] === this.selectedOptions.coletaInicial) {
					return i;
				}
			}
		},

		getSelectedColetaFinalIndex() {
			for (let i = 0; i < this.formatedVisitDates.length; i++) {
				if (this.formatedVisitDates[i] === this.selectedOptions.coletaFinal) {
					return i;
				}
			}
		},

		// Método removido - processamento movido para o backend

		async getTotalCocoaForPeriod() {
			if (!this.selectedOptions.propriedade ||
				!this.selectedOptions.areaHomogenea ||
				!this.selectedOptions.safra ||
				!this.selectedOptions.faixaInicial ||
				!this.selectedOptions.faixaFinal ||
				!this.selectedOptions.coletaInicial ||
				!this.selectedOptions.coletaFinal) {
				alert("Selecione todos os filtros necessários!");
				return;
			}

			this.loading = true;

			try {
				const { propriedade, areaHomogenea, coletaInicial, coletaFinal } = this.selectedOptions;
				const [startDate, endDate] = this.parseSelectedPeriod();

				const faixaInicialIndex = this.getSelectedFaixaInicialIndex();
				const faixaFinalIndex = this.getSelectedFaixaFinalIndex();

				const response = await axios.post(
					"/api/v1/get-total-cocoa-by-range",
					{
						propertyId: propriedade,
						areaHomogeneaId: areaHomogenea,
						startDate,
						endDate,
						faixaInicial: faixaInicialIndex,
						faixaFinal: faixaFinalIndex,
						coletaInicial,
						coletaFinal
					},
					{ headers: { authorization: `bearer ${this.authToken}` } }
				);

				if (response.data.success) {
					// Atualizar dados do gráfico com a resposta do backend
					this.chartData.labels = response.data.data.labels;
					this.chartData.datasets = response.data.data.datasets;
					this.totalCocoa = response.data.data.totalCocoa;

					// Forçar re-render do gráfico
					this.chartKey++;
				} else {
					console.error("Erro na resposta do servidor:", response.data);
					alert("Erro ao processar os dados no servidor");
				}
			} catch (error) {
				console.error("Erro ao obter dados do total de cacau:", error);
				alert("Erro ao carregar os dados. Tente novamente.");
			} finally {
				this.loading = false;
			}
		}

	}
};
