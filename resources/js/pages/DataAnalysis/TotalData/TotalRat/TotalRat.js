import axios from "axios";
import LineChart from "../../../../components/LineChart.vue";

	export default {
	components: {
		LineChart,
	},
	data() {
		return {
			authToken: null,
			properties: [],
			homogeneousAreas: [],
			selectedOptions: {
				propriedade: "",
				safra: "",
			},
			loading: true,
			datePeriods: [],
			startDate: "",
			endDate: "",
			collectionData: [],
			totalRat: [],

			chartKey: 0,
			chartData: {
				labels: [],
				datasets: [
					{
						label: "Frutos perdidos por ocorrência de Ratos",
						borderColor: "green", // Define a cor da linha
						backgroundColor: "transparent", // Remove o preenchimento
						pointBackgroundColor: "green", // Cor dos pontos
						pointBorderColor: "white", // Borda dos pontos para destacar
						pointRadius: 5, // Tamanho dos pontos
						data: [],
					},
				]
			},
			chartOptions: {
				responsive: true,
				maintainAspectRatio: false, // Melhora a renderização
				}

		};
	},

	computed: {
		totalProperties() {
			return this.properties.length > 0;
		}
	},

		watch: {
		"selectedOptions.propriedade": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				this.selectedOptions.safra = "";
				this.homogeneousAreas = [];
				this.datePeriods = [];
				this.fetchHomogeneousAreas();
				this.fetchVisitDates();
			}
		},

		"selectedOptions.safra": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				this.fetchCollectionData();
			}
		},
	},

	created() {
		this.authToken = window.token;
		this.loading = true;
		this.fetchProperties();
	},

	methods: {
		updateChartDatasets() {
			this.chartData.labels = [];
			for (const date of this.totalRat.keys()) {
				this.chartData.labels.push(this.formatDate(date));
			}
			this.chartData.datasets[0].data = [];
			for (const value of this.totalRat.values()) {
				this.chartData.datasets[0].data.push(value);
			}
		},

		fetchProperties() {
			this.loading = true;
			axios
				.get("/api/v1/properties", {
					headers: { authorization: `bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						this.properties = response.data.data;
						if (this.properties.length > 0) {
							this.selectedOptions.propriedade = this.properties[0].id;
							this.fetchHomogeneousAreas(true); // Passa true para diferenciar a inicialização
							this.fetchVisitDates(true);
						}
					}
				})
				.catch((err) => {
					console.log(err);
					this.loading = false;
				});
		},

		fetchHomogeneousAreas(isInit = false) {
			if (!this.selectedOptions.propriedade) return;

			this.loading = true;
			axios
				.get(`/api/v1/homogeneous-area?propid=${this.selectedOptions.propriedade}`, {
					headers: { authorization: `Bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						this.homogeneousAreas = response.data.data;
						if (isInit && this.homogeneousAreas.length > 0) {
							this.selectedOptions.areaHomogenea = this.homogeneousAreas[0].id;
						}
					}
				})
				.catch((err) => {
					console.error(err);
					this.loading = false;
				});
		},

		async fetchVisitDates(isInit = false) {
			try {
				const response = await axios.get("/api/v1/get-visit-dates", {
					headers: { authorization: `bearer ${this.authToken}` },
				});
				const { oldestVisit, latestVisit } = response.data;

				if (oldestVisit && latestVisit) {
					this.generateDateOptions(oldestVisit, latestVisit);
					if (isInit && this.datePeriods.length > 0) {
						this.selectedOptions.safra = this.datePeriods[this.datePeriods.length - 1];
						this.fetchCollectionData(isInit);
					}
				} else {
					this.datePeriods = [];
				}
			} catch (error) {
				if (error.response && error.response.status === 404) {
					this.datePeriods = [];
				} else {
					console.error(error);
				}
			}
		},

		generateDateOptions(oldestVisit, latestVisit) {
			const oldestYear = new Date(oldestVisit).getFullYear();
			const latestYear = new Date(latestVisit).getFullYear();
			const currentYear = new Date().getFullYear();
			const currentMonth = new Date().getMonth();
			const limitYear = currentMonth <= 8 ? currentYear - 1 : currentYear; // Ajuste aqui

			this.datePeriods = [];

			for (
				let year = oldestYear;
				year <= latestYear && year <= limitYear;
				year++
			) {
				const nextYear = year + 1;
				this.datePeriods.push(`Outubro/${year} - Setembro/${nextYear}`);
			}
			this.datePeriods.pop(); // Remove o último ano, pois não é necessário
		},

		async fetchCollectionData(isInit = false) {
			if (!this.selectedOptions.propriedade || !this.selectedOptions.safra) {
				alert("Selecione todos os filtros necessários!");
				return;
			}
			this.loading = true;

			const { propriedade, safra } = this.selectedOptions;
			const [startDate, endDate] = this.parseSelectedPeriod();

			if (!this.homogeneousAreas.length) {
				await this.fetchHomogeneousAreas();
			}

			const requests = this.homogeneousAreas.map(area =>
				axios.post("/api/v1/get-collection-data-rat",
					{ propertyId: propriedade, areaHomogeneaId: area.id, startDate, endDate },
					{ headers: { authorization: `bearer ${this.authToken}` } }
				).then(res => ({
					areaId: area.id,
					areaName: area.name || area.nome || `AH ${area.id}`,
					data: res.data.totals
				}))
			);

			try {
				const responses = await Promise.all(requests);
				this.collectionData = responses;
				this.getTotalRatForPeriod();
			} catch (err) {
				console.error("Erro ao obter dados:", err);
			} finally {
				this.loading = false;
			}
		},

		parseSelectedPeriod() {
			if (!this.selectedOptions.safra) return ["", ""];

			const periodParts = this.selectedOptions.safra.split(" - ");
			const startYear = periodParts[0].split("/")[1];
			const endYear = periodParts[1].split("/")[1];

			return [`Outubro/${startYear}`, `Setembro/${endYear}`];
		},

		formatDate(dateString) {
			if (!dateString) return "";
			const date = new Date(dateString);
			const day = String(date.getDate()).padStart(2, '0');
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const year = date.getFullYear();
			return `${day}/${month}/${year}`;
		},

		getTotalRatForPeriod() {
			this.chartData.labels = [];
			this.chartData.datasets = [];

			const dateSet = new Set();
			const areaDataMap = new Map();

			for (const areaObj of this.collectionData) {
				const areaName = areaObj.areaName;
				if (!areaName) continue;

				const dateMap = new Map();

				for (const [date, total] of Object.entries(areaObj.data)) {
					dateSet.add(date);
					dateMap.set(date, total);
				}

				areaDataMap.set(areaName, dateMap);
			}

			const sortedDates = Array.from(dateSet).sort();
			this.chartData.labels = sortedDates.map(this.formatDate);

			const colors = ["green", "blue", "red", "orange", "purple", "teal", "brown", "pink"];
			let colorIndex = 0;

			for (const [areaName, dateMap] of areaDataMap.entries()) {
				const dataPoints = sortedDates.map(date => dateMap.get(date) || 0);

				this.chartData.datasets.push({
					label: areaName,
					borderColor: colors[colorIndex % colors.length],
					backgroundColor: "transparent",
					pointBackgroundColor: colors[colorIndex % colors.length],
					pointBorderColor: "white",
					pointRadius: 5,
					data: dataPoints
				});

				colorIndex++;
			}

			this.chartKey++; // força o re-render
		}


	}
};
