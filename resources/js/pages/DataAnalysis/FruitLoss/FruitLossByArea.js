import axios from "axios";
import LineChart from "../../../components/LineChart.vue";

export default {
	components: {
		LineChart,
	},
	data() {
		return {
			authToken: null,
			properties: [],
			homogeneousAreas: [],
			selectedOptions: {
				propriedade: "",
				safra: "",
			},
			loading: true,
			datePeriods: [],
			startDate: "",
			endDate: "",
			collectionData: [],
			totalLoss: [],
			totalPiece: [],
			totalRat: [],
			totalRotten: [],
			totalWitchBroom: [],
			selectedAnalysis: null,

			chartKey: 0,
			chartData: {
				labels: [],
				datasets: []
			},
			chartOptions: {
				responsive: true,
				maintainAspectRatio: false,
			},

			// Tipos de análise disponíveis (por Área Homogênea)
			analysisTypes: [
				{
					key: 'loss',
					title: 'Perdas por Causas Desconhecidas',
					shortDescription: 'Frutos perdidos por causa indeterminada',
					description: 'Selecione os itens abaixo para visualizar por <PERSON><PERSON>, em um gráfico de linhas, a incidência de frutos perdidos por causa indeterminada.',
					color: '#4f4f4f',
					icon: 'fas fa-question-circle'
				},
				{
					key: 'witch-broom',
					title: 'Incidência de Vassoura de Bruxa',
					shortDescription: 'Frutos infectados com vassoura de bruxa',
					description: 'Selecione os itens abaixo para visualizar por Área Homogênea, em um gráfico de linhas, a incidência de frutos infectados por vassoura de bruxa.',
					color: '#5e3d26',
					icon: 'fas fa-tree'
				},
				{
					key: 'piece',
					title: 'Incidência de Peco',
					shortDescription: 'Frutos com incidência de Peco',
					description: 'Selecione os itens abaixo para visualizar por Área Homogênea, em um gráfico de linhas, a incidência de frutos com Peco.',
					color: '#2a3b73',
					icon: 'fas fa-bug'
				},
				{
					key: 'rotten',
					title: 'Incidência de Podridão Parda',
					shortDescription: 'Frutos com podridão parda',
					description: 'Selecione os itens abaixo para visualizar por Área Homogênea, em um gráfico de linhas, a incidência de frutos com podridão parda.',
					color: '#4a2e5c',
					icon: 'fas fa-virus'
				},
				{
					key: 'rat',
					title: 'Incidência de Ratos',
					shortDescription: 'Frutos perdidos por ataque de ratos',
					description: 'Selecione os itens abaixo para visualizar por Área Homogênea, em um gráfico de linhas, a quantidade de frutos perdidos por ataque de ratos.',
					color: '#7c2b2b',
					icon: 'fas fa-paw'
				}
			]
		};
	},

	computed: {
		currentAnalysis() {
			if (!this.selectedAnalysis) {
				return {
					title: 'Frutos Perdidos por Área Homogênea',
					description: 'Selecione um fator de análise para visualizar os dados por Área Homogênea.'
				};
			}
			return this.analysisTypes.find(analysis => analysis.key === this.selectedAnalysis);
		},

		totalProperties() {
			return this.properties.length > 0;
		}
	},

	watch: {
		"selectedOptions.propriedade": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				this.selectedOptions.safra = "";
				this.homogeneousAreas = [];
				this.datePeriods = [];
				this.fetchHomogeneousAreas();
				this.fetchVisitDates();
			}
		},

		"selectedOptions.safra": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				this.fetchCollectionData();
			}
		},
	},

	created() {
		this.authToken = window.token;
		this.loading = true;
		this.fetchProperties();
	},

	methods: {
		selectAnalysis(analysisKey) {
			this.selectedAnalysis = analysisKey;
			this.collectionData = [];
			this.chartData = {
				labels: [],
				datasets: []
			};

			// Se já tem propriedade e safra selecionadas, busca os dados
			if (this.selectedOptions.propriedade && this.selectedOptions.safra) {
				this.getDataForPeriod();
			}
		},

		fetchProperties() {
			this.loading = true;
			axios
				.get("/api/v1/properties", {
					headers: { authorization: `bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						this.properties = response.data.data;
						if (this.properties.length > 0) {
							this.selectedOptions.propriedade = this.properties[0].id;
							this.fetchHomogeneousAreas(true); // Passa true para diferenciar a inicialização
							this.fetchVisitDates(true);
						}
					}
				})
				.catch((err) => {
					console.log(err);
					this.loading = false;
				});
		},

		fetchHomogeneousAreas(isInit = false) {
			if (!this.selectedOptions.propriedade) return;

			this.loading = true;
			axios
				.get(`/api/v1/homogeneous-area?propid=${this.selectedOptions.propriedade}`, {
					headers: { authorization: `Bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						this.homogeneousAreas = response.data.data;
					}
				})
				.catch((err) => {
					console.error(err);
					this.loading = false;
				});
		},

		async fetchVisitDates(isInit = false) {
			try {
				const response = await axios.get("/api/v1/get-visit-dates", {
					headers: { authorization: `bearer ${this.authToken}` },
				});
				const { oldestVisit, latestVisit } = response.data;

				if (oldestVisit && latestVisit) {
					this.generateDateOptions(oldestVisit, latestVisit);
					if (isInit && this.datePeriods.length > 0) {
						this.selectedOptions.safra = this.datePeriods[this.datePeriods.length - 1];
						this.fetchCollectionData(isInit);
					}
				} else {
					this.datePeriods = [];
				}
			} catch (error) {
				if (error.response && error.response.status === 404) {
					this.datePeriods = [];
				} else {
					console.error(error);
				}
			}
		},

		generateDateOptions(oldestVisit, latestVisit) {
			const oldestYear = new Date(oldestVisit).getFullYear();
			const latestYear = new Date(latestVisit).getFullYear();
			const currentYear = new Date().getFullYear();
			const currentMonth = new Date().getMonth();
			const limitYear = currentMonth <= 8 ? currentYear - 1 : currentYear; // Ajuste aqui

			this.datePeriods = [];

			for (
				let year = oldestYear;
				year <= latestYear && year <= limitYear;
				year++
			) {
				const nextYear = year + 1;
				this.datePeriods.push(`Outubro/${year} - Setembro/${nextYear}`);
			}
		},

		async fetchCollectionData(isInit = false, analysisKey = 'loss') {
			if (!this.selectedOptions.propriedade || !this.selectedOptions.safra) {
				alert("Selecione todos os filtros necessários!");
				return;
			}
			this.loading = true;

			const { propriedade, safra } = this.selectedOptions;
			const [startDate, endDate] = this.parseSelectedPeriod();

			if (!this.homogeneousAreas.length) {
				await this.fetchHomogeneousAreas();
			}

			const apiEndpoints = {
				'loss': '/api/v1/get-collection-data-loss',
				'rat': '/api/v1/get-collection-data-rat',
				'rotten': '/api/v1/get-collection-data-rotten',
				'piece': '/api/v1/get-collection-data-piece',
				'witch-broom': '/api/v1/get-collection-data-wb'
			};

			const requests = this.homogeneousAreas.map(area =>
				axios.post(apiEndpoints[analysisKey],
					{ propertyId: propriedade, areaHomogeneaId: area.id, startDate, endDate },
					{ headers: { authorization: `bearer ${this.authToken}` } }
				).then(res => ({
					areaId: area.id,
					areaName: area.name || area.nome || `AH ${area.id}`,
					data: res.data.totals
				}))
			);

			try {
				const responses = await Promise.all(requests);
				this.collectionData = responses;
			} catch (err) {
				console.error("Erro ao obter dados:", err);
			}
		},

		parseSelectedPeriod() {
			if (!this.selectedOptions.safra) return ["", ""];

			const periodParts = this.selectedOptions.safra.split(" - ");
			const startYear = periodParts[0].split("/")[1];
			const endYear = periodParts[1].split("/")[1];

			return [`Outubro/${startYear}`, `Setembro/${endYear}`];
		},

		formatDate(dateString) {
			if (!dateString) return "";
			const date = new Date(dateString);
			const day = String(date.getDate()).padStart(2, '0');
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const year = date.getFullYear();
			return `${day}/${month}/${year}`;
		},

		getTotalLossForPeriod() {
			this.chartData.labels = [];
			this.chartData.datasets = [];

			const dateSet = new Set();
			const areaDataMap = new Map();

			for (const areaObj of this.collectionData) {
				const areaName = areaObj.areaName;
				const dateMap = new Map();

				for (const [date, total] of Object.entries(areaObj.data)) {
					dateSet.add(date);
					dateMap.set(date, total);
				}

				areaDataMap.set(areaName, dateMap);
			}

			const sortedDates = Array.from(dateSet).sort();
			this.chartData.labels = sortedDates.map(this.formatDate);

			const colors = ["green", "blue", "red", "orange", "purple", "teal", "brown", "pink"];
			let colorIndex = 0;

			for (const [areaName, dateMap] of areaDataMap.entries()) {
				const dataPoints = sortedDates.map(date => dateMap.get(date) || 0);

				this.chartData.datasets.push({
					label: areaName,
					borderColor: colors[colorIndex % colors.length],
					backgroundColor: "transparent",
					pointBackgroundColor: colors[colorIndex % colors.length],
					pointBorderColor: "white",
					pointRadius: 5,
					data: dataPoints
				});

				colorIndex++;
			}

			this.chartKey++; // força o re-render
		},

		getTotalPieceForPeriod() {
			this.chartData.labels = [];
			this.chartData.datasets = [];

			const dateSet = new Set();
			const areaDataMap = new Map();

			for (const areaObj of this.collectionData) {
				const areaName = areaObj.areaName;
				if (!areaName) continue;

				const dateMap = new Map();

				for (const [date, total] of Object.entries(areaObj.data)) {
					dateSet.add(date);
					dateMap.set(date, total);
				}

				areaDataMap.set(areaName, dateMap);
			}

			const sortedDates = Array.from(dateSet).sort();
			this.chartData.labels = sortedDates.map(this.formatDate);

			const colors = ["green", "blue", "red", "orange", "purple", "teal", "brown", "pink"];
			let colorIndex = 0;

			for (const [areaName, dateMap] of areaDataMap.entries()) {
				const dataPoints = sortedDates.map(date => dateMap.get(date) || 0);

				this.chartData.datasets.push({
					label: areaName,
					borderColor: colors[colorIndex % colors.length],
					backgroundColor: "transparent",
					pointBackgroundColor: colors[colorIndex % colors.length],
					pointBorderColor: "white",
					pointRadius: 5,
					data: dataPoints
				});

				colorIndex++;
			}

			this.chartKey++; // força o re-render
		},

		getTotalRatForPeriod() {
			this.chartData.labels = [];
			this.chartData.datasets = [];

			const dateSet = new Set();
			const areaDataMap = new Map();

			for (const areaObj of this.collectionData) {
				const areaName = areaObj.areaName;
				if (!areaName) continue;

				const dateMap = new Map();

				for (const [date, total] of Object.entries(areaObj.data)) {
					dateSet.add(date);
					dateMap.set(date, total);
				}

				areaDataMap.set(areaName, dateMap);
			}

			const sortedDates = Array.from(dateSet).sort();
			this.chartData.labels = sortedDates.map(this.formatDate);

			const colors = ["green", "blue", "red", "orange", "purple", "teal", "brown", "pink"];
			let colorIndex = 0;

			for (const [areaName, dateMap] of areaDataMap.entries()) {
				const dataPoints = sortedDates.map(date => dateMap.get(date) || 0);

				this.chartData.datasets.push({
					label: areaName,
					borderColor: colors[colorIndex % colors.length],
					backgroundColor: "transparent",
					pointBackgroundColor: colors[colorIndex % colors.length],
					pointBorderColor: "white",
					pointRadius: 5,
					data: dataPoints
				});

				colorIndex++;
			}

			this.chartKey++; // força o re-render
		},

		getTotalRottenForPeriod() {
			this.chartData.labels = [];
			this.chartData.datasets = [];

			const dateSet = new Set();
			const areaDataMap = new Map();

			for (const areaObj of this.collectionData) {
				const areaName = areaObj.areaName;
				if (!areaName) continue;

				const dateMap = new Map();

				for (const [date, total] of Object.entries(areaObj.data)) {
					dateSet.add(date);
					dateMap.set(date, total);
				}

				areaDataMap.set(areaName, dateMap);
			}

			const sortedDates = Array.from(dateSet).sort();
			this.chartData.labels = sortedDates.map(this.formatDate);

			const colors = ["green", "blue", "red", "orange", "purple", "teal", "brown", "pink"];
			let colorIndex = 0;

			for (const [areaName, dateMap] of areaDataMap.entries()) {
				const dataPoints = sortedDates.map(date => dateMap.get(date) || 0);

				this.chartData.datasets.push({
					label: areaName,
					borderColor: colors[colorIndex % colors.length],
					backgroundColor: "transparent",
					pointBackgroundColor: colors[colorIndex % colors.length],
					pointBorderColor: "white",
					pointRadius: 5,
					data: dataPoints
				});

				colorIndex++;
			}

			this.chartKey++; // força o re-render
		},

		getTotalWitchBroomForPeriod() {
			this.chartData.labels = [];
			this.chartData.datasets = [];

			const dateSet = new Set();
			const areaDataMap = new Map();

			for (const areaObj of this.collectionData) {
				const areaName = areaObj.areaName;
				const dateMap = new Map();

				for (const [date, total] of Object.entries(areaObj.data)) {
					dateSet.add(date);
					dateMap.set(date, total);
				}

				areaDataMap.set(areaName, dateMap);
			}

			const sortedDates = Array.from(dateSet).sort();
			this.chartData.labels = sortedDates.map(this.formatDate);

			const colors = ["green", "blue", "red", "orange", "purple", "teal", "brown", "pink"];
			let colorIndex = 0;

			for (const [areaName, dateMap] of areaDataMap.entries()) {
				const dataPoints = sortedDates.map(date => dateMap.get(date) || 0);

				this.chartData.datasets.push({
					label: areaName,
					borderColor: colors[colorIndex % colors.length],
					backgroundColor: "transparent",
					pointBackgroundColor: colors[colorIndex % colors.length],
					pointBorderColor: "white",
					pointRadius: 5,
					data: dataPoints
				});

				colorIndex++;
			}

			this.chartKey++; // força o re-render
		},

		async getDataForPeriod() {
			if (!this.selectedAnalysis || !this.selectedOptions.propriedade || !this.selectedOptions.safra) {
				return;
			}

			this.loading = true;

			switch(this.selectedAnalysis) {
				case 'loss':
					await this.fetchCollectionData(false, 'loss');
					this.getTotalLossForPeriod();
					break;
				case 'rat':
					await this.fetchCollectionData(false, 'rat');
					this.getTotalRatForPeriod();
					break;
				case 'rotten':
					await this.fetchCollectionData(false, 'rotten');
					this.getTotalRottenForPeriod();
					break;
				case 'witch-broom':
					await this.fetchCollectionData(false, 'witch-broom'); // atenção para o nome diferente aqui!
					this.getTotalWitchBroomForPeriod();
					break;
				case 'piece':
					await this.fetchCollectionData(false, 'piece');
					this.getTotalPieceForPeriod();
					break;
				default:
					break;
			}

			this.loading = false;
		}
	}
};

