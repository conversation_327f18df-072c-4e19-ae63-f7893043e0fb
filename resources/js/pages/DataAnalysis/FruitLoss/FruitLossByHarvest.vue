<template>
	<div id="fruitLossByHarvest" class="mt-5 fruitLossWrapper">
		<div class="admin-content">
			<div class="admin-header d-flex justify-content-between align-items-center p-2">
				<div class="d-flex align-items-center">
					<back-button title="Voltar para análise de dados"></back-button>
					<div class="d-flex flex-column">
						<h1>{{ currentAnalysis.title }}</h1>
						<p>{{ currentAnalysis.description }}</p>
					</div>
				</div>
			</div>

			<!-- Seleção do Fator de Análise -->
			<div class="analysis-selector">
				<div class="selector-container">
					<h3>Selecione o Fator de Análise</h3>
					<div class="analysis-options">
						<div
							v-for="analysis in analysisTypes"
							:key="analysis.key"
							class="analysis-option"
							:class="{ active: selectedAnalysis === analysis.key }"
							@click="selectAnalysis(analysis.key)"
						>
							<div class="option-icon" :style="{ backgroundColor: analysis.color }">
								<i :class="analysis.icon"></i>
							</div>
							<div class="option-content">
								<h4>{{ analysis.title }}</h4>
								<p>{{ analysis.shortDescription }}</p>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- Controles do Gráfico -->
			<div class="graph-control" v-if="selectedAnalysis">
				<form @submit.prevent="getDataForPeriod" class="graph-form">
					<div class="row top-row">
						<div class="form-group">
							<label for="propriedade">Propriedade</label>
							<select id="propriedade" v-model="selectedOptions.propriedade">
								<option v-for="(property, index) in properties" :key="property.id" :value="property.id" :selected="index === 0">
									{{ property.name }}
								</option>
							</select>
						</div>
						<div class="form-group">
							<label for="area">Área Homogênea</label>
							<select id="area" v-model="selectedOptions.areaHomogenea">
								<option v-if="homogeneousAreas.length === 0">
									Não há áreas cadastradas
								</option>
								<option v-for="area in homogeneousAreas" :key="area.id" :value="area.id">
									{{ area.label }}
								</option>
							</select>
						</div>
					</div>
				</form>
			</div>

			<!-- Gráfico -->
			<div v-if="collectionData.length > 0 && !loading && selectedAnalysis" class="chart-wrapper">
				<div class="chart-container">
					<LineChart :key="chartKey" :chart-data="chartData" :chart-options="chartOptions" />
				</div>
			</div>

			<!-- Loading -->
			<div v-else-if="loading && selectedAnalysis" class="chart-wrapper">
				<span
					class="spinner-border spinner-border-sm"
					role="status"
					aria-hidden="true"
					style="width: 3rem; height: 3rem; position: absolute; top: 50%; left: 50%;"
				></span>
			</div>

			<!-- Mensagem quando nenhuma análise está selecionada -->
			<div v-else-if="!selectedAnalysis" class="no-selection-message">
				<div class="message-content">
					<i class="fas fa-chart-line fa-3x"></i>
					<h3>Selecione um Fator de Análise</h3>
					<p>Escolha uma das opções acima para visualizar os dados por Safra em gráfico de linhas.</p>
				</div>
			</div>
		</div>
	</div>
</template>

<script src="./FruitLossByHarvest.js"></script>

<style lang="scss">
.fruitLossWrapper {
	background-color: #f5f8fd;
	border-radius: 20px;

	.admin-content {
		height: 90vh;
		overflow-y: auto;
	}

	h1 {
		color: #3d8160;
		font-family: "Lexend", sans-serif;
		font-weight: 600;
		font-size: 24px;
		padding: 16px 0px 0px 16px;
		text-align: left;
	}

	p {
		font-family: "Lexend", sans-serif;
		font-size: 18px;
		padding: 0px 0px 0px 16px;
		text-align: left;
	}

	.btn {
		height: auto;
		width: auto;
	}
}

.analysis-selector {
	padding: 20px;
	background: white;
	margin: 20px;
	border-radius: 15px;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

	.selector-container {
		h3 {
			color: #3d8160;
			font-family: "Lexend", sans-serif;
			font-weight: 600;
			font-size: 20px;
			margin-bottom: 20px;
			text-align: center;
			padding: 0;
		}
	}

	.analysis-options {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
		gap: 15px;
	}

	.analysis-option {
		display: flex;
		align-items: center;
		padding: 15px;
		border: 2px solid #e9ecef;
		border-radius: 10px;
		cursor: pointer;
		transition: all 0.3s ease;
		background: white;

		&:hover {
			border-color: #3d8160;
			box-shadow: 0 4px 15px rgba(61, 129, 96, 0.1);
			transform: translateY(-2px);
		}

		&.active {
			border-color: #3d8160;
			background: rgba(61, 129, 96, 0.05);
			box-shadow: 0 4px 15px rgba(61, 129, 96, 0.2);

			.option-content h4 {
				color: #3d8160;
			}
		}

		.option-icon {
			width: 50px;
			height: 50px;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 15px;
			flex-shrink: 0;

			i {
				color: white;
				font-size: 1.2rem;
			}
		}

		.option-content {
			flex: 1;

			h4 {
				font-family: "Lexend", sans-serif;
				font-weight: 600;
				font-size: 16px;
				margin: 0 0 5px 0;
				color: #333;
				transition: color 0.3s ease;
			}

			p {
				font-family: "Lexend", sans-serif;
				font-size: 14px;
				margin: 0;
				color: #666;
				padding: 0;
				line-height: 1.4;
			}
		}
	}
}

.graph-control {
	padding: 16px;
	display: flex;
	justify-content: center;
	align-items: center;

	.graph-form {
		display: flex;
		flex-direction: column;
		align-items: center;

		.top-row {
			display: flex;
			gap: 20px;
			margin-bottom: 10px;
			justify-content: center;
		}

		.form-group {
			display: flex;
			align-items: center;
			gap: 10px;
		}

		label {
			white-space: nowrap;
			font-family: "Lexend", sans-serif;
			font-weight: 500;
			color: #3d8160;
		}

		select {
			padding: 8px 12px;
			border: 2px solid #e9ecef;
			border-radius: 8px;
			min-width: 150px;
			font-family: "Lexend", sans-serif;
			transition: border-color 0.3s ease;

			&:focus {
				outline: none;
				border-color: #3d8160;
				box-shadow: 0 0 0 0.2rem rgba(61, 129, 96, 0.25);
			}
		}
	}
}

.chart-wrapper {
	margin: 1rem 3rem;
	background: white;
	border-radius: 15px;
	padding: 20px;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.no-selection-message {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 300px;
	margin: 2rem;

	.message-content {
		text-align: center;
		color: #6c757d;

		i {
			margin-bottom: 20px;
			opacity: 0.5;
		}

		h3 {
			font-family: "Lexend", sans-serif;
			font-weight: 600;
			margin-bottom: 10px;
			color: #495057;
		}

		p {
			font-family: "Lexend", sans-serif;
			margin: 0;
			padding: 0;
		}
	}
}

canvas {
	width: 90% !important;
	height: 90% !important;
}

// Responsividade
@media (max-width: 768px) {
	.analysis-options {
		grid-template-columns: 1fr;
	}

	.graph-control .top-row {
		flex-direction: column;
		gap: 15px;
	}

	.chart-wrapper {
		margin: 1rem;
		padding: 15px;
	}
}
</style>
