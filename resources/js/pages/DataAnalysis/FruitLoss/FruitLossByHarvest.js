import axios from "axios";
import LineChart from "../../../components/LineChart.vue";

export default {
	components: {
		LineChart,
	},
	data() {
		return {
			authToken: null,
			properties: [],
			homogeneousAreas: [],
			selectedOptions: {
				propriedade: "",
				areaHomogenea: "",
			},
			loading: true,
			datePeriods: [], // Vai armazenar as safras disponíveis
			collectionData: [],
			selectedAnalysis: null,

			chartKey: 0,
			chartData: {
				labels: [],
				datasets: []
			},
			chartOptions: {
				responsive: true,
				maintainAspectRatio: false,
			},

			// Tipos de análise disponíveis (por Safra)
			analysisTypes: [
				{
					key: 'loss',
					title: 'Perdas por Causas Desconhecidas',
					shortDescription: 'Frutos perdidos por causa indeterminada',
					description: 'Selecione os itens abaixo para visualizar por Safra, em um gráfico de linhas, a incidência de frutos perdidos por causa indeterminada.',
					color: '#4f4f4f',
					icon: 'fas fa-question-circle'
				},
				{
					key: 'witch-broom',
					title: 'Incidência de Vassoura de Bruxa',
					shortDescription: 'Frutos infectados com vassoura de bruxa',
					description: 'Selecione os itens abaixo para visualizar por Safra, em um gráfico de linhas, a incidência de frutos infectados por vassoura de bruxa.',
					color: '#5e3d26',
					icon: 'fas fa-tree'
				},
				{
					key: 'piece',
					title: 'Incidência de Peco',
					shortDescription: 'Frutos com incidência de Peco',
					description: 'Selecione os itens abaixo para visualizar por Safra, em um gráfico de linhas, a incidência de frutos com Peco.',
					color: '#2a3b73',
					icon: 'fas fa-bug'
				},
				{
					key: 'rotten',
					title: 'Incidência de Podridão Parda',
					shortDescription: 'Frutos com podridão parda',
					description: 'Selecione os itens abaixo para visualizar por Safra, em um gráfico de linhas, a incidência de frutos com podridão parda.',
					color: '#4a2e5c',
					icon: 'fas fa-virus'
				},
				{
					key: 'rat',
					title: 'Incidência de Ratos',
					shortDescription: 'Frutos perdidos por ataque de ratos',
					description: 'Selecione os itens abaixo para visualizar por Safra, em um gráfico de linhas, a quantidade de frutos perdidos por ataque de ratos.',
					color: '#7c2b2b',
					icon: 'fas fa-paw'
				}
			]
		};
	},

	computed: {
		currentAnalysis() {
			if (!this.selectedAnalysis) {
				return {
					title: 'Frutos Perdidos por Safra',
					description: 'Selecione um fator de análise para visualizar os dados por Safra.'
				};
			}
			return this.analysisTypes.find(analysis => analysis.key === this.selectedAnalysis);
		},

		totalProperties() {
			return this.properties.length > 0;
		}
	},

	watch: {
		"selectedOptions.propriedade": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				this.selectedOptions.areaHomogenea = "";
				this.homogeneousAreas = [];
				this.datePeriods = [];
				this.fetchHomogeneousAreas();
				this.fetchVisitDates();
			}
		},

		"selectedOptions.areaHomogenea": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				this.fetchCollectionData();
			}
		},
	},

	created() {
		this.authToken = window.token;
		this.loading = true;
		this.fetchProperties();
	},

	methods: {
		selectAnalysis(analysisKey) {
			this.selectedAnalysis = analysisKey;
			this.collectionData = [];
			this.chartData = {
				labels: [],
				datasets: []
			};

			// Se já tem propriedade e área selecionadas, busca os dados
			if (this.selectedOptions.propriedade && this.selectedOptions.areaHomogenea) {
				this.getDataForPeriod();
			}
		},

		fetchProperties() {
			this.loading = true;
			axios
				.get("/api/v1/properties", {
					headers: { authorization: `bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						this.properties = response.data.data;
						if (this.properties.length > 0) {
							this.selectedOptions.propriedade = this.properties[0].id;
							this.fetchHomogeneousAreas(true);
							this.fetchVisitDates(true);
						}
					}
				})
				.catch((err) => {
					console.log(err);
					this.loading = false;
				});
		},

		fetchHomogeneousAreas(isInit = false) {
			if (!this.selectedOptions.propriedade) return;

			this.loading = true;
			axios
				.get(`/api/v1/homogeneous-area?propid=${this.selectedOptions.propriedade}`, {
					headers: { authorization: `Bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						this.homogeneousAreas = response.data.data;
						if (isInit && this.homogeneousAreas.length > 0) {
							this.selectedOptions.areaHomogenea = this.homogeneousAreas[0].id;
						}
					}
				})
				.catch((err) => {
					console.error(err);
					this.loading = false;
				});
		},

		async fetchVisitDates(isInit = false) {
			try {
				const response = await axios.get("/api/v1/get-visit-dates", {
					headers: { authorization: `bearer ${this.authToken}` },
				});
				const { oldestVisit, latestVisit } = response.data;

				if (oldestVisit && latestVisit) {
					this.generateDateOptions(oldestVisit, latestVisit);
					if (isInit && this.datePeriods.length > 0) {
						this.fetchCollectionData();
					}
				} else {
					this.datePeriods = [];
				}
			} catch (error) {
				if (error.response && error.response.status === 404) {
					this.datePeriods = [];
				} else {
					console.error(error);
				}
			}
		},

		generateDateOptions(oldestVisit, latestVisit) {
			const oldestYear = new Date(oldestVisit).getFullYear();
			const latestYear = new Date(latestVisit).getFullYear();
			const currentYear = new Date().getFullYear();
			const currentMonth = new Date().getMonth();
			const limitYear = currentMonth <= 8 ? currentYear - 1 : currentYear;

			this.datePeriods = [];

			for (let year = oldestYear; year <= latestYear && year <= limitYear; year++) {
				const nextYear = year + 1;
				this.datePeriods.push(`Outubro/${year} - Setembro/${nextYear}`);
			}
			this.datePeriods.pop();
		},

		parseSelectedPeriod(period) {
			const periodParts = period.split(" - ");
			const startYear = periodParts[0].split("/")[1];
			const endYear = periodParts[1].split("/")[1];

			return [`Outubro/${startYear}`, `Setembro/${endYear}`];
		},

		async fetchCollectionData(analysisKey = 'loss') {
			if (!this.selectedOptions.propriedade || !this.selectedOptions.areaHomogenea) {
				alert("Selecione a propriedade e a área homogênea!");
				return;
			}
			this.loading = true;

			const { propriedade, areaHomogenea } = this.selectedOptions;

			const apiEndpoints = {
				'loss': '/api/v1/get-collection-data-loss-ha',
				'rat': '/api/v1/get-collection-data-rat-ha',
				'rotten': '/api/v1/get-collection-data-rotten-ha',
				'piece': '/api/v1/get-collection-data-piece-ha',
				'witch-broom': '/api/v1/get-collection-data-wb-ha'
			};

			const requests = this.datePeriods.map(period => {
				const [startDate, endDate] = this.parseSelectedPeriod(period);
				return axios.post(apiEndpoints[analysisKey],
					{ propertyId: propriedade, areaHomogeneaId: areaHomogenea, startDate, endDate },
					{ headers: { authorization: `bearer ${this.authToken}` } }
				).then(res => ({
					safra: period,
					data: res.data // array de totais já ordenado
				}));
			});

			try {
				const responses = await Promise.all(requests);
				this.collectionData = responses;
			} catch (err) {
				console.error("Erro ao obter dados:", err);
			}
		},


		formatDate(dateString) {
			if (!dateString) return "";
			const date = new Date(dateString);
			const day = String(date.getDate()).padStart(2, '0');
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const year = date.getFullYear();
			return `${day}/${month}/${year}`;
		},

		getTotalLossForPeriod() {
			this.chartData.labels = [];
			this.chartData.datasets = [];

			let maxColetas = 0;

			for (const safraObj of this.collectionData) {
				if (safraObj.data.length > maxColetas) {
					maxColetas = safraObj.data.length;
				}
			}

			this.chartData.labels = Array.from({ length: maxColetas }, (_, i) => `${i + 1}ª Coleta`);

			const colors = ["green", "blue", "red", "orange", "purple", "teal", "brown", "pink"];
			let colorIndex = 0;

			for (const safraObj of this.collectionData) {
				const dataPoints = Array.from({ length: maxColetas }, (_, i) => safraObj.data[i] || 0);

				this.chartData.datasets.push({
					label: safraObj.safra,
					borderColor: colors[colorIndex % colors.length],
					backgroundColor: "transparent",
					pointBackgroundColor: colors[colorIndex % colors.length],
					pointBorderColor: "white",
					pointRadius: 5,
					data: dataPoints
				});

				colorIndex++;
			}

			this.chartKey++;
		},

		getTotalPieceForPeriod() {
			this.chartData.labels = [];
			this.chartData.datasets = [];

			let maxColetas = 0;

			for (const safraObj of this.collectionData) {
				if (safraObj.data.length > maxColetas) {
					maxColetas = safraObj.data.length;
				}
			}

			this.chartData.labels = Array.from({ length: maxColetas }, (_, i) => `${i + 1}ª Coleta`);

			const colors = ["green", "blue", "red", "orange", "purple", "teal", "brown", "pink"];
			let colorIndex = 0;

			for (const safraObj of this.collectionData) {
				const dataPoints = Array.from({ length: maxColetas }, (_, i) => safraObj.data[i] || 0);

				this.chartData.datasets.push({
					label: safraObj.safra,
					borderColor: colors[colorIndex % colors.length],
					backgroundColor: "transparent",
					pointBackgroundColor: colors[colorIndex % colors.length],
					pointBorderColor: "white",
					pointRadius: 5,
					data: dataPoints
				});

				colorIndex++;
			}

			this.chartKey++;
		},

		getTotalRatForPeriod() {
			this.chartData.labels = [];
			this.chartData.datasets = [];

			let maxColetas = 0;

			for (const safraObj of this.collectionData) {
				if (safraObj.data.length > maxColetas) {
					maxColetas = safraObj.data.length;
				}
			}

			this.chartData.labels = Array.from({ length: maxColetas }, (_, i) => `${i + 1}ª Coleta`);

			const colors = ["green", "blue", "red", "orange", "purple", "teal", "brown", "pink"];
			let colorIndex = 0;

			for (const safraObj of this.collectionData) {
				const dataPoints = Array.from({ length: maxColetas }, (_, i) => safraObj.data[i] || 0);

				this.chartData.datasets.push({
					label: safraObj.safra,
					borderColor: colors[colorIndex % colors.length],
					backgroundColor: "transparent",
					pointBackgroundColor: colors[colorIndex % colors.length],
					pointBorderColor: "white",
					pointRadius: 5,
					data: dataPoints
				});

				colorIndex++;
			}

			this.chartKey++;
		},

		getTotalRottenForPeriod() {
			this.chartData.labels = [];
			this.chartData.datasets = [];

			let maxColetas = 0;

			for (const safraObj of this.collectionData) {
				if (safraObj.data.length > maxColetas) {
					maxColetas = safraObj.data.length;
				}
			}

			this.chartData.labels = Array.from({ length: maxColetas }, (_, i) => `${i + 1}ª Coleta`);

			const colors = ["green", "blue", "red", "orange", "purple", "teal", "brown", "pink"];
			let colorIndex = 0;

			for (const safraObj of this.collectionData) {
				const dataPoints = Array.from({ length: maxColetas }, (_, i) => safraObj.data[i] || 0);

				this.chartData.datasets.push({
					label: safraObj.safra,
					borderColor: colors[colorIndex % colors.length],
					backgroundColor: "transparent",
					pointBackgroundColor: colors[colorIndex % colors.length],
					pointBorderColor: "white",
					pointRadius: 5,
					data: dataPoints
				});

				colorIndex++;
			}

			this.chartKey++;
			this.loading = false;
		},

		getTotalWBForPeriod() {
			this.chartData.labels = [];
			this.chartData.datasets = [];

			let maxColetas = 0;

			for (const safraObj of this.collectionData) {
				if (safraObj.data.length > maxColetas) {
					maxColetas = safraObj.data.length;
				}
			}

			this.chartData.labels = Array.from({ length: maxColetas }, (_, i) => `${i + 1}ª Coleta`);

			const colors = ["green", "blue", "red", "orange", "purple", "teal", "brown", "pink"];
			let colorIndex = 0;

			for (const safraObj of this.collectionData) {
				const dataPoints = Array.from({ length: maxColetas }, (_, i) => safraObj.data[i] || 0);

				this.chartData.datasets.push({
					label: safraObj.safra,
					borderColor: colors[colorIndex % colors.length],
					backgroundColor: "transparent",
					pointBackgroundColor: colors[colorIndex % colors.length],
					pointBorderColor: "white",
					pointRadius: 5,
					data: dataPoints
				});

				colorIndex++;
			}

			this.chartKey++;
		},

		// Continua o mesmo formatador de dd/mm
		formatDayMonth(dateString) {
			if (!dateString) return "";
			const date = new Date(dateString);
			const day = String(date.getDate()).padStart(2, '0');
			const month = String(date.getMonth() + 1).padStart(2, '0');
			return `${day}/${month}`;
		},

		async getDataForPeriod() {
			if (!this.selectedAnalysis || !this.selectedOptions.propriedade || !this.selectedOptions.areaHomogenea) {
				return;
			}

			this.loading = true;

			switch(this.selectedAnalysis) {
				case 'loss':
					await this.fetchCollectionData('loss');
					this.getTotalLossForPeriod();
					break;
				case 'rat':
					await this.fetchCollectionData('rat');
					this.getTotalRatForPeriod();
					break;
				case 'rotten':
					await this.fetchCollectionData('rotten');
					this.getTotalRottenForPeriod();
					break;
				case 'witch-broom':
					await this.fetchCollectionData('witch-broom');
					this.getTotalWBForPeriod();
					break;
				case 'piece':
					await this.fetchCollectionData('piece');
					this.getTotalPieceForPeriod();
					break;
				default:
					break;
			}
			this.loading = false;
		},
	}
};
