import { update } from "lodash";
import <PERSON><PERSON><PERSON> from "../../../components/PieChart.vue";
import <PERSON><PERSON>hart from "../../../components/BarChart.vue";
import Alert from "../../../components/Alert.vue";
import Swal from "sweetalert2";

export default {
	name: "AnalysisOptions",
	components: {
		<PERSON><PERSON><PERSON>,
		<PERSON><PERSON><PERSON>,
		Alert,
	},
	data() {
	return {
		selectedView: 'distribution', // 'distribution' ou 'monthly'
		authToken: null,
		properties: [],
		allProperties: [], // Para admin
		isAdmin: false,
		homogeneousAreas: [],
		selectedOptions: {
			propriedade: "",
			safra: "",
		},
		loading: true,
		datePeriods: [],
		startDate: "",
		endDate: "",
		collectionData: null,

		lossEstimate:0,
		totalCocoa: 0,
		totalWitchBroom: 0,
		totalHarvested: 0,
		totalRotten: 0,
		totalLoss: 0,
		totalRat: 0,
		totalPiece: 0,
		harvestedByMonth: null,
		totalWitchBroomPercentage: 0,
		chartKey: 0,
		chartData: {
			labels: [],
			datasets: [
				{
				label: "<PERSON><PERSON><PERSON> da Safra",
				backgroundColor: [],
				data: [],
				}
			]
		},
		chartOptions: {
			responsive: true,
			maintainAspectRatio: false,
			legend: {
				position: 'top',
				labels: {
				fontSize: 14,
				boxWidth: 20,
				}
			},
		},

		harvestChartData: {
			labels: [
				'Outubro', 'Novembro', 'Dezembro',
				'Janeiro', 'Fevereiro', 'Março',
				'Abril', 'Maio', 'Junho',
				'Julho', 'Agosto', 'Setembro'
			],
			datasets: [
				{
					label: 'Frutos Colhidos',
					backgroundColor: '#4CAF50',
					data: []
				}
			]
		},

		summaryColumns: [
			{
				field: "data",
				label: " ",
				sortable: false,
				class: "text-center",
			},
			{
				field: "witchs_broom",
				label: "Vassoura de Bruxa (VB)",
				sortable: false,
				class: "text-center",
			},
			{
				field: "rotten",
				label: "Podridão Parda (PP)",
				sortable: false,
				class: "text-center",
			},
			{
				field: "rat",
				label: "Rato (RA)",
				sortable: false,
				class: "text-center",
			},
			{
				field: "piece",
				label: "Peco (PE)",
				sortable: false,
				class: "text-center",
			},
			{
				field: "loss",
				label: "Perdidos (PD)",
				sortable: false,
				class: "text-center",
			},
			{
				field: "lossEstimate",
				label: "Total das Perdas (TP*)",
				sortable: false,
				class: "text-center",
			},
			{
				field: "harvested",
				label: "Colhidos (TC)",
				sortable: false,
				class: "text-center",
			},
			{
				field: "totalCocoa",
				label: "Total de Frutos (TF)",
				sortable: false,
				class: "text-center",
			}
		],
		summaryRows: [],
		harvestColumns: [],
		harvestRows: [],
		priceArrobaPerMonth: {
			"2022-10": 178.3,
			"2022-11": 190.35,
			"2022-12": 196.57,
			"2023-01": 201.0,
			"2023-02": 206.27,
			"2023-03": 213.82,
			"2023-04": 212.61,
			"2023-05": 215.04,
			"2023-06": 226.66,
			"2023-07": 239.53,
			"2023-08": 256.04,
			"2023-09": 273.1,
			"2023-10": 278.75,
			"2023-11": 306.57,
			"2023-12": 324.45,
			"2024-01": 337.81,
			"2024-02": 420.26,
			"2024-03": 537.5,
			"2024-04": 804.22,
			"2024-05": 649.47,
			"2024-06": 821.25,
			"2024-07": 794.09,
			"2024-08": 765.90,
			"2024-09": 750.19,
			"2024-10": 698.40,
			"2024-11": 836.57,
			"2024-12": 893.42,
			"2025-01": 848.27,
			"2025-02": 804.55,
			"2025-03": 711.44,
			"2025-04": 815.22,
			"2025-05": 796.25
		},

		// Tipos de visualização disponíveis
		viewTypes: [
			{
				key: 'distribution',
				title: 'Distribuição de Frutos',
				description: 'Visualize a distribuição dos frutos por categoria em gráfico de pizza e tabela resumo',
				color: '#66bb6a',
				icon: 'fas fa-chart-pie'
			},
			{
				key: 'monthly',
				title: 'Frutos Colhidos por Mês',
				description: 'Acompanhe a evolução mensal da colheita em gráfico de barras e tabela temporal',
				color: '#3d8160',
				icon: 'fas fa-chart-bar'
			}
		]

	};
  },
	computed: {
		totalProperties() {
			return this.properties.length > 0;
		}
	},

	watch: {
		"selectedOptions.propriedade": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				this.selectedOptions.safra = "";
				this.homogeneousAreas = [];
				this.datePeriods = [];
				this.fetchHomogeneousAreas();
				this.fetchVisitDates();
			}
		},

		"selectedOptions.safra": function (newVal, oldVal) {
			if (newVal && newVal !== oldVal) {
				this.fetchCollectionData();
			}
		},
	},

	async created() {
		this.authToken = window.token;
		this.loading = true;
		await this.checkUserRole();
		this.fetchProperties();
	},

	methods: {

		selectView(viewKey) {
			this.selectedView = viewKey;
		},

		fetchProperties() {
			this.loading = true;

			const endpoint = this.isAdmin ? "/api/v1/shared-properties" : "/api/v1/properties";

			axios
				.get(endpoint, {
					headers: { authorization: `bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						this.properties = response.data.data;
						this.allProperties = [...this.properties]; // Backup para admin
						if (this.properties.length > 0) {
							this.selectedOptions.propriedade = this.properties[0].id;
							this.fetchHomogeneousAreas(true);
							this.fetchVisitDates(true);
						}
					}
				})
				.catch((err) => {
					console.log(err);
					this.loading = false;
				});
		},

		fetchHomogeneousAreas(isInit = false) {
			if (!this.selectedOptions.propriedade) return;

			this.loading = true;
			axios
				.get(`/api/v1/homogeneous-area?propid=${this.selectedOptions.propriedade}`, {
					headers: { authorization: `Bearer ${this.authToken}` },
				})
				.then((response) => {
					if (response.status === 200) {
						this.homogeneousAreas = response.data.data;
					}
				})
				.catch((err) => {
					console.error(err);
					this.loading = false;
				});
		},

		async fetchVisitDates(isInit = false) {
			try {
				const response = await axios.get("/api/v1/get-visit-dates", {
					headers: { authorization: `bearer ${this.authToken}` },
				});
				const { oldestVisit, latestVisit } = response.data;

				if (oldestVisit && latestVisit) {
					this.generateDateOptions(oldestVisit, latestVisit);
					if (isInit && this.datePeriods.length > 0) {
						this.selectedOptions.safra = this.datePeriods[this.datePeriods.length - 1];
						this.fetchCollectionData(isInit);
					}
				} else {
					this.datePeriods = [];
				}
			} catch (error) {
				if (error.response && error.response.status === 404) {
					this.datePeriods = [];
				} else {
					console.error(error);
				}
			}
		},

		generateDateOptions(oldestVisit, latestVisit) {
			const oldestYear = new Date(oldestVisit).getFullYear();
			const latestYear = new Date(latestVisit).getFullYear();
			const currentYear = new Date().getFullYear();
			const currentMonth = new Date().getMonth();
			const limitYear = currentMonth <= 8 ? currentYear - 1 : currentYear;

			this.datePeriods = [];

			for (
				let year = oldestYear;
				year <= latestYear && year <= limitYear;
				year++
			) {
				const nextYear = year + 1;
				this.datePeriods.push(`Outubro/${year} - Setembro/${nextYear}`);
			}
		},

		async fetchCollectionData() {
			if (!this.selectedOptions.propriedade || !this.selectedOptions.safra) {
				alert("Selecione todos os filtros necessários!");
				return;
			}
			this.collectionData = [];
			this.loading = true;

			const { propriedade, safra } = this.selectedOptions;
			const [startDate, endDate] = this.parseSelectedPeriod();

			if (!this.homogeneousAreas.length) {
				await this.fetchHomogeneousAreas();
			}

			const areaIds = this.homogeneousAreas.map(area => area.id);

			try {
				const res = await axios.post("/api/v1/get-collection-data-multi-area", {
					propertyId: propriedade,
					areaHomogeneaIds: areaIds,
					startDate,
					endDate
				}, {
					headers: { authorization: `bearer ${this.authToken}` }
				});

				const totals = res.data.totalCocoaData;
				const harvestedByMonth = res.data.totalHarvested;

				this.totalCocoa = totals.totalCocoa;
				this.totalHarvested = totals.totalHarvested;
				this.totalLoss = totals.totalLoss;
				this.totalPiece = totals.totalPiece;
				this.totalRat = totals.totalRat;
				this.totalRotten = totals.totalRotten;
				this.totalWitchBroom = totals.totalWitchBroom;
				this.lossEstimate = totals.lossEstimate;

				this.harvestedByMonth = harvestedByMonth;

				// Atualizações de UI
				this.updateChartDatasets();
				this.summaryRowsUpdate();
				this.harvestRowsUpdate();
				this.harvestColumnsUpdate();
				this.updateHarvestChartData();

			} catch (err) {
				console.error("Erro ao obter dados:", err);
			} finally {
				this.loading = false;
			}
		},


		parseSelectedPeriod() {
			if (!this.selectedOptions.safra) return ["", ""];

			const periodParts = this.selectedOptions.safra.split(" - ");
			const startYear = periodParts[0].split("/")[1];
			const endYear = periodParts[1].split("/")[1];

			return [`Outubro/${startYear}`, `Setembro/${endYear}`];
		},

		formatDate(dateString) {
			if (!dateString) return "";
			const date = new Date(dateString);
			const day = String(date.getDate()).padStart(2, '0');
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const year = date.getFullYear();
			return `${day}/${month}/${year}`;
		},

		updateChartDatasets() {
			const percent = (val) => (val / this.totalCocoa) * 100;
			this.totalWitchBroomPercentage = (this.totalWitchBroom / this.totalCocoa) * 100;
			this.chartData = {
				labels: [
					`Colhidos: ${percent(this.totalHarvested).toFixed(2)}%`,
					`Peco: ${percent(this.totalPiece).toFixed(2)}%`,
					`Rato: ${percent(this.totalRat).toFixed(2)}%`,
					`Vassoura de Bruxa: ${percent(this.totalWitchBroom).toFixed(2)}%`,
					`Podres: ${percent(this.totalRotten).toFixed(2)}%`,
					`Perdidos: ${percent(this.totalLoss).toFixed(2)}%`,
				],
				datasets: [
					{
						label: `Distribuição de Frutos na safra`,
						backgroundColor: [
							"#4caf50",
							"#5c6bc0",
							"#bcaaa4",
							"#8e24aa",
							"#ba68c8",
							"#40E0D0",
						],
						data: [this.totalHarvested, this.totalPiece, this.totalRat, this.totalWitchBroom, this.totalRotten, this.totalLoss],
						borderWidth: 2,
					}
				]
			};

			if (this.totalRat === 0) this.chartData.datasets[0].borderWidth = 0;
			if (this.totalPiece === 0) this.chartData.datasets[0].borderWidth = 0;
			if (this.totalRotten === 0) this.chartData.datasets[0].borderWidth = 0;
			if (this.totalWitchBroom === 0) this.chartData.datasets[0].borderWidth = 0;
			if (this.totalLoss === 0) this.chartData.datasets[0].borderWidth = 0;
			if (this.totalHarvested === 0) this.chartData.datasets[0].borderWidth = 0;

			this.chartKey++;
		},

		getTotalHarvestedForPeriod() {
			const monthlyTotals = Array(12).fill(0);
			monthlyTotals[0] = this.harvestedByMonth.out || 0;
			monthlyTotals[1] = this.harvestedByMonth.nov || 0;
			monthlyTotals[2] = this.harvestedByMonth.dez || 0;
			monthlyTotals[3] = this.harvestedByMonth.jan || 0;
			monthlyTotals[4] = this.harvestedByMonth.fev || 0;
			monthlyTotals[5] = this.harvestedByMonth.mar || 0;
			monthlyTotals[6] = this.harvestedByMonth.abr || 0;
			monthlyTotals[7] = this.harvestedByMonth.mai || 0;
			monthlyTotals[8] = this.harvestedByMonth.jun || 0;
			monthlyTotals[9] = this.harvestedByMonth.jul || 0;
			monthlyTotals[10] = this.harvestedByMonth.ago || 0;
			monthlyTotals[11] = this.harvestedByMonth.set || 0;
			return monthlyTotals;
		},

		updateHarvestChartData() {
			const monthlyTotals = this.getTotalHarvestedForPeriod();
			this.harvestChartData.datasets[0].data = monthlyTotals;
			this.harvestChartData.datasets[0].backgroundColor = Array(12).fill("#4CAF50");
			this.harvestChartData.labels = [
				'Outubro', 'Novembro', 'Dezembro',
				'Janeiro', 'Fevereiro', 'Março',
				'Abril', 'Maio', 'Junho',
				'Julho', 'Agosto', 'Setembro'
			];
			this.chartKey++;
		},

		summaryRowsUpdate() {
			const prices = this.getPricesForSelectedSafra();
			const monthsKeys = ['out', 'nov', 'dez', 'jan', 'fev', 'mar', 'abr', 'mai', 'jun', 'jul', 'ago', 'set'];

			// Usar os dados diretos do objeto harvestedByMonth
			const monthlyTotals = monthsKeys.map(month => this.harvestedByMonth[month] || 0);
			const totalHarvested = monthlyTotals.reduce((sum, q) => sum + q, 0) || 1;

			const getEstimatedValue = (categoryTotal) => {
				let totalEstimation = 0;
				monthlyTotals.forEach((monthTotal, i) => {
					const proporcional = (monthTotal / totalHarvested) * categoryTotal;
					const peso = proporcional / 375;
					const preco = prices[i] ?? 0;
					totalEstimation += peso * preco;
				});
				return `R$ ${totalEstimation.toFixed(2)}`;
			};

			this.summaryRows = [
				{
					data: "Quant. de Frutos",
					witchs_broom: this.totalWitchBroom,
					rotten: this.totalRotten,
					rat: this.totalRat,
					piece: this.totalPiece,
					loss: this.totalLoss,
					lossEstimate: this.lossEstimate,
					harvested: totalHarvested,
					totalCocoa: this.totalCocoa,
				},
				{
					data: "Peso (@)",
					witchs_broom: `${(this.totalWitchBroom / 375).toFixed(2)} @`,
					rotten: `${(this.totalRotten / 375).toFixed(2)} @`,
					rat: `${(this.totalRat / 375).toFixed(2)} @`,
					piece: `${(this.totalPiece / 375).toFixed(2)} @`,
					loss: `${(this.totalLoss / 375).toFixed(2)} @`,
					lossEstimate: `${(this.lossEstimate / 375).toFixed(2)} @`,
					harvested: `${(totalHarvested / 375).toFixed(2)} @`,
					totalCocoa: `${(this.totalCocoa / 375).toFixed(2)} @`,
				},
				{
					data: "Estimativa (R$)",
					witchs_broom: getEstimatedValue(this.totalWitchBroom),
					rotten: getEstimatedValue(this.totalRotten),
					rat: getEstimatedValue(this.totalRat),
					piece: getEstimatedValue(this.totalPiece),
					loss: getEstimatedValue(this.totalLoss),
					lossEstimate: getEstimatedValue(this.lossEstimate),
					harvested: getEstimatedValue(totalHarvested),
					totalCocoa: getEstimatedValue(this.totalCocoa),
				}
			];
		},

		harvestRowsUpdate() {
			const prices = this.getPricesForSelectedSafra();
			const monthsKeys = ['out', 'nov', 'dez', 'jan', 'fev', 'mar', 'abr', 'mai', 'jun', 'jul', 'ago', 'set'];

			const monthlyTotals = monthsKeys.map(month => this.harvestedByMonth[month] || 0);
			const totalHarvested = monthlyTotals.reduce((sum, q) => sum + q, 0);

			this.harvestRows = [
				{
					data: "Quant. de Frutos",
					...Object.fromEntries(monthsKeys.map((k, i) => [k, monthlyTotals[i]])),
					total: totalHarvested,
				},
				{
					data: "Peso (@)",
					...Object.fromEntries(monthsKeys.map((k, i) => {
						const peso = (monthlyTotals[i] / 375).toFixed(2);
						return [k, `${peso} @`];
					})),
					total: `${(totalHarvested / 375).toFixed(2)} @`,
				},
				{
					data: "Valor (R$/@)",
					...Object.fromEntries(monthsKeys.map((k, i) => {
						const price = prices[i]?.toFixed(2) ?? '0.00';
						return [k, `R$ ${price}`];
					})),
					total: '-',
				},
				{
					data: "Estimativa (R$)",
					...Object.fromEntries(monthsKeys.map((k, i) => {
						const peso = monthlyTotals[i] / 375;
						const estimativa = (peso * prices[i]).toFixed(2);
						return [k, `R$ ${estimativa}`];
					})),
					total: `R$ ${prices.reduce((acc, price, i) => {
						const peso = monthlyTotals[i] / 375;
						return acc + peso * price;
					}, 0).toFixed(2)}`,
				}
			];
		},

		getPricesForSelectedSafra() {
			const safra = this.selectedOptions.safra;
			if (!safra) return [];

			const [start, end] = safra.split(' - ');
			const startYear = parseInt(start.split('/')[1]);
			const endYear = parseInt(end.split('/')[1]);

			const months = [
				"10", "11", "12",
				"01", "02", "03", "04", "05", "06", "07", "08", "09"
			];

			const fullKeys = months.map((month, i) => {
				const year = i < 3 ? startYear : endYear;
				return `${year}-${month}`;
			});

			return fullKeys.map(key => this.priceArrobaPerMonth[key] || 0);
		},

		harvestColumnsUpdate() {
			if (!this.selectedOptions.safra) return;

			const [startMonthYear] = this.selectedOptions.safra.split(" - ");
			const startYearSuffix = parseInt(startMonthYear.split("/")[1].trim()); // ex: 23
			const fullStartYear = 2000 + startYearSuffix;

			const monthLabels = [
				{ key: "out", label: "Out" },
				{ key: "nov", label: "Nov" },
				{ key: "dez", label: "Dez" },
				{ key: "jan", label: "Jan" },
				{ key: "fev", label: "Fev" },
				{ key: "mar", label: "Mar" },
				{ key: "abr", label: "Abr" },
				{ key: "mai", label: "Mai" },
				{ key: "jun", label: "Jun" },
				{ key: "jul", label: "Jul" },
				{ key: "ago", label: "Ago" },
				{ key: "set", label: "Set" }
			];

			const columns = [
				{
					field: "data",
					label: " ",
					sortable: false,
				}
			];

			monthLabels.forEach((month, index) => {
				const isNextYear = index >= 3; // Janeiro (index 3) em diante
				const year = isNextYear ? fullStartYear + 1 : fullStartYear;
				columns.push({
					field: month.key,
					label: `${month.label}/${String(year).slice(-2)}`,
					sortable: false,
				});
			});

			columns.push({
				field: "total",
				label: "Total Colhido",
				sortable: false,
			});

			this.harvestColumns = columns;
		},

		showAlertModal() {
			Swal.fire({
			  title: "Alerta de Vassoura de Bruxa",
			  text: "A incidência da praga ultrapassou 5% da produção total. Monitoramento e controle imediato são essenciais para proteger sua lavoura.",
			  icon: "warning",
			  showCancelButton: true,
			  confirmButtonText: "Saiba Mais",
			  cancelButtonText: "Voltar",
			  reverseButtons: true,
			}).then((result) => {
			  if (result.isConfirmed) {
				this.$router.push("/panel/witch-broom");
			  } else if (result.dismiss === Swal.DismissReason.cancel) {
				Swal.close();
			  }
			});
		  }
	},

	// Verificar se usuário é admin
	async checkUserRole() {
		try {
			const response = await axios.get("/api/v1/user", {
				headers: { authorization: `bearer ${this.authToken}` },
			});

			if (response.data && response.data.roles) {
				this.isAdmin = response.data.roles.some(role => role.label === 'admin');
			}
		} catch (err) {
			console.error('Erro ao verificar papel do usuário:', err);
			this.isAdmin = false;
		}
	},

};
