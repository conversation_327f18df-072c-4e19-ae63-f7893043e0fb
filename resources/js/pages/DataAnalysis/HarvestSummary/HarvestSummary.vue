<template>
	<div id="reports" class="mt-5 harvestSummaryWrapper">
		<div class="admin-content">
			<div class="admin-header d-flex justify-content-between align-items-center p-2">
				<div class="d-flex align-items-center">
					<back-button title="Voltar para propriedades"></back-button>
					<div class="d-flex flex-column">
						<h1>Resumo da Safra</h1>
						<p>Acompanhe, a seguir, o resumo parcial do desenvolvimento da safra.</p>
					</div>
				</div>
			</div>

			<div class="graph-control w-100">
				<form @submit.prevent="calculateTotalCocoaData" class="graph-form">
					<div class="row top-row">
						<div class="form-group">
							<label for="propriedade">Propriedade</label>
							<select id="propriedade" v-model="selectedOptions.propriedade">
								<option v-for="(property, index) in properties" :key="property.id" :value="property.id" :selected="index === 0">
									{{ property.name }}
								</option>
							</select>
						</div>
						<div class="form-group">
							<label for="safra">Safra</label>
							<select id="safra" v-model="selectedOptions.safra">
								<option v-if="datePeriods.length === 0">Não há visitas cadastradas</option>
								<option v-for="(date) in datePeriods" :key="date" :value="date">
									{{ date }}
								</option>
							</select>
						</div>
					</div>
				</form>
			</div>
			<template>
				<div v-if="!loading" class="chart-wrapper">
					<!-- Seleção do Tipo de Visualização -->
					<div class="analysis-selector">
						<div class="selector-container">
							<h3>Selecione o Tipo de Visualização</h3>
							<div class="analysis-options">
								<div
									v-for="viewType in viewTypes"
									:key="viewType.key"
									class="analysis-option"
									:class="{ active: selectedView === viewType.key }"
									@click="selectView(viewType.key)"
								>
									<div class="option-icon" :style="{ backgroundColor: viewType.color }">
										<i :class="viewType.icon"></i>
									</div>
									<div class="option-content">
										<h4>{{ viewType.title }}</h4>
										<p>{{ viewType.description }}</p>
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- Gráfico e Tabela: Distribuição de Frutos -->
					<div v-if="selectedView === 'distribution'">
						<h2 class="center-title">{{'Gráfico de Distribuição de Frutos na Safra: ' + selectedOptions.safra }}</h2>
						<div class="chart-container">
							<PieChart :key="chartKey" :chart-data="chartData" :chart-options="chartOptions" />
						</div>
						<h2 class="center-title">{{'Distribuição de Frutos na Safra: ' + selectedOptions.safra}}</h2>
						<div class="center-first-table">
							<vue-good-table
								title="Resumo da safra"
								:columns="summaryColumns"
								:rows="summaryRows"
								:style="{ textAlign: 'center' , verticalAlign: 'middle' , marginBottom: '1rem' }"
							>
								<template slot="table-row" slot-scope="props">
									<span v-if="props.column.field == 'data'">{{ props.row.data }}</span>
									<span v-if="props.column.field == 'witchs_broom'">{{ props.row.witchs_broom }}</span>
									<span v-if="props.column.field == 'rotten'">{{ props.row.rotten }}</span>
									<span v-if="props.column.field == 'rat'">{{ props.row.rat }}</span>
									<span v-if="props.column.field == 'piece'">{{ props.row.piece }}</span>
									<span v-if="props.column.field == 'loss'">{{ props.row.loss }}</span>
									<span v-if="props.column.field == 'lossEstimate'">{{ props.row.lossEstimate }}</span>
									<span v-if="props.column.field == 'harvested'">{{ props.row.harvested }}</span>
									<span v-if="props.column.field == 'totalCocoa'">{{ props.row.totalCocoa }}</span>
								</template>
							</vue-good-table>
						</div>
						<div class="table-footer">
							TP = (VB + PP + RA + PE + PD)
							<br>
							TF = TP + TC
						</div>
					</div>

					<!-- Gráfico e Tabela: Frutos Colhidos por Mês -->
					<div v-if="selectedView === 'monthly'">
						<h2 class="center-title">{{'Gráfico de Frutos Colhidos por Mês na Safra: ' + selectedOptions.safra }}</h2>
						<div class="chart-container">
							<BarChart :key="chartKey" :chart-data="harvestChartData" :chart-options="chartOptions" />
						</div>
						<h2 class="center-title">{{'Total de Frutos Colhidos por Mês na Safra: ' + selectedOptions.safra }}</h2>
						<div class="center-second-table">
							<vue-good-table
								title="Frutos Colhidos"
								:columns="harvestColumns"
								:rows="harvestRows"
								:style="{ textAlign: 'center' , verticalAlign: 'middle' , marginBottom: '1rem' }"
							>
								<template slot="table-row" slot-scope="props">
									<span v-if="props.column.field == 'data'">{{ props.row.data }}</span>
									<span v-if="props.column.field == 'out'">{{ props.row.out }}</span>
									<span v-if="props.column.field == 'nov'">{{ props.row.nov }}</span>
									<span v-if="props.column.field == 'dez'">{{ props.row.dez }}</span>
									<span v-if="props.column.field == 'jan'">{{ props.row.jan }}</span>
									<span v-if="props.column.field == 'fev'">{{ props.row.fev }}</span>
									<span v-if="props.column.field == 'mar'">{{ props.row.mar }}</span>
									<span v-if="props.column.field == 'abr'">{{ props.row.abr }}</span>
									<span v-if="props.column.field == 'mai'">{{ props.row.mai }}</span>
									<span v-if="props.column.field == 'jun'">{{ props.row.jun }}</span>
									<span v-if="props.column.field == 'jul'">{{ props.row.jul }}</span>
									<span v-if="props.column.field == 'ago'">{{ props.row.ago }}</span>
									<span v-if="props.column.field == 'set'">{{ props.row.set }}</span>
									<span v-if="props.column.field == 'total'">{{ props.row.total }}</span>
								</template>
							</vue-good-table>
						</div>
					</div>
				</div>

				<!-- Div de loading permanece -->
				<div v-else-if="loading" class="chart-wrapper">
					<span
						class="spinner-border spinner-border-sm"
						role="status"
						aria-hidden="true"
						style="width: 3rem; height: 3rem; position: absolute; top: 50%; left: 50%;"
					></span>
				</div>
			</template>
			<div v-if="!loading && totalWitchBroomPercentage > 5.0" @click="showAlertModal">
				<Alert text="Alerta" />
			</div>
		</div>
	</div>
</template>

<script src="./HarvestSummary"> </script>

<style lang="scss">
	.harvestSummaryWrapper {
		background-color: #f5f8fd;
		border-radius: 20px;

		.admin-content {
  			min-height: 90vh;
			height: auto;
			overflow: visible;
		}


		h1 {
		color: #3d8160;
		font-family: "Lexend", sans-serif;
		font-weight: 600;
		font-size: 24px;
		padding: 16px 0px 0px 16px;
		text-align: left;
		}

		p {
		font-family: "Lexend", sans-serif;
		font-size: 18px;
		padding: 0px 0px 0px 16px;
		text-align: left;
		}
	}

	.graph-control {
		padding: 16px;
		display: flex;
		justify-content: center;
		align-items: center;


		.graph-form {
			display: flex;
			flex-direction: column;
			align-items: center;

			.top-row {
				display: flex;
				gap: 20px;
				margin-bottom: 10px;
				justify-content: center;
			}

			.faixas, .coletas {
				display: flex;
				flex-direction: column;
				gap: 10px;
				align-items: center;
			}

			.form-group {
				display: flex;
				align-items: center;
				gap: 10px;
			}

			label {
				white-space: nowrap;
			}

			select {
				padding: 6px;
				border: 1px solid #ccc;
				border-radius: 4px;
				min-width: 5rem;
			}
		}
	}

	.chart-wrapper {
		margin: 1rem 3rem;
	}

	.center-title {
	text-align: center;
	margin-top: 2rem;
	color: #3d8160;
	font-family: "Lexend", sans-serif;
	font-weight: 600;
	}

	.center-first-table {
		display: flex;
		justify-content: center;
		margin: 2 0 0 0;
		overflow-x: auto;
		width: 100%;
		> * {
			min-width: 700px;
		}
	}

	.center-title {
		text-align: center;
		margin-top: 2rem;
		color: #3d8160;
		font-family: "Lexend", sans-serif;
		font-weight: 600;
		overflow-x: auto;
		width: 100%;
	}

	.center-second-table {
		display: flex;
		justify-content: center;
		margin: 2rem 0;
		overflow-x: auto;
		width: 100%;
		> * {
			min-width: 700px;
		}
	}

	.table-footer {
		text-align: center;
		font-family: "Lexend", sans-serif;
		font-size: 18px;
		color: #636b6f;
	}

	::v-deep(.vgt-table th),
	::v-deep(.vgt-table td) {
		padding: 12px 16px !important;
		text-align: center !important;
		vertical-align: middle !important;
		white-space: nowrap !important;
		border: 1px solid #ccc !important;
		font-family: "Lexend", sans-serif;
		font-size: 14px;
	}

	::v-deep(.vgt-table thead th) {
		background-color: #f5f8fd !important;
		font-weight: 600 !important;
		color: #3d8160 !important;
	}

	::v-deep(.vgt-table tbody tr:nth-child(even)) {
		background-color: #fafafa !important;
	}

	.analysis-selector {
		padding: 20px;
		background: white;
		margin: 20px;
		border-radius: 15px;
		box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

		h3 {
			text-align: center;
			color: #3d8160;
			font-family: "Lexend", sans-serif;
			font-weight: 600;
			margin-bottom: 20px;
		}

		@media (max-width: 768px) {
			margin: 10px;
			padding: 15px;

			h3 {
				font-size: 1.2rem;
				text-align: center;
				padding: 0;
			}
		}
	}

	.analysis-options {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
		gap: 15px;
	}

	.analysis-option {
		display: flex;
		align-items: center;
		padding: 15px;
		border: 2px solid #e9ecef;
		border-radius: 10px;
		cursor: pointer;
		transition: all 0.3s ease;
		background: white;

		&:hover {
			border-color: #3d8160;
			box-shadow: 0 4px 12px rgba(61, 129, 96, 0.15);
		}

		&.active {
			border-color: #3d8160;
			background: rgba(61, 129, 96, 0.05);
			box-shadow: 0 4px 12px rgba(61, 129, 96, 0.2);

			.option-content h4 {
				color: #3d8160;
			}
		}

		.option-icon {
			width: 50px;
			height: 50px;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 15px;
			color: white;
			font-size: 1.2rem;
		}

		.option-content {
			flex: 1;

			h4 {
				margin: 0 0 5px 0;
				font-size: 1.1rem;
				font-weight: 600;
				color: #333;
				font-family: "Lexend", sans-serif;
			}

			p {
				margin: 0;
				font-size: 0.9rem;
				color: #666;
				line-height: 1.4;
			}
		}
	}

	// Responsividade
	@media (max-width: 768px) {
		.analysis-options {
			grid-template-columns: 1fr;
		}
	}

  </style>
