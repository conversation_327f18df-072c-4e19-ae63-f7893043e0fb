<template>
	<div class="harvest-summary-container">
		<!-- Header Principal -->
		<div class="page-header">
			<div class="header-content">
				<back-button title="Voltar para propriedades"></back-button>
				<div class="header-icon">
					<i class="fas fa-table"></i>
				</div>
				<div class="header-text">
					<h1>Resumo da Safra</h1>
					<p>Acompanhe o resumo completo do desenvolvimento da safra com múltiplas visualizações</p>
				</div>
			</div>
		</div>

		<!-- Filtros -->
		<div class="filters-section">
			<div class="filters-card">
				<h3>Filtros de Análise</h3>
				<form @submit.prevent="calculateTotalCocoaData" class="filters-form">
					<div class="filters-grid">
						<div class="filter-group">
							<label for="propriedade">
								<i class="fas fa-map-marker-alt"></i>
								Propriedade
							</label>
							<select id="propriedade" v-model="selectedOptions.propriedade" class="filter-select">
								<option value="" disabled>Selecione uma propriedade</option>
								<option v-for="property in properties" :key="property.id" :value="property.id">
									{{ property.name }}
								</option>
							</select>
							<div v-if="properties.length === 0" class="empty-message">
								<i class="fas fa-exclamation-circle"></i>
								Nenhuma propriedade encontrada
							</div>
						</div>

						<div class="filter-group">
							<label for="safra">
								<i class="fas fa-calendar-alt"></i>
								Safra
							</label>
							<select id="safra" v-model="selectedOptions.safra" class="filter-select" :disabled="!selectedOptions.propriedade">
								<option value="" disabled>Selecione uma safra</option>
								<option v-for="date in datePeriods" :key="date" :value="date">
									{{ date }}
								</option>
							</select>
							<div v-if="selectedOptions.propriedade && datePeriods.length === 0" class="empty-message">
								<i class="fas fa-exclamation-circle"></i>
								Não há visitas cadastradas
							</div>
						</div>
					</div>
				</form>
			</div>
		</div>

		<!-- Conteúdo Principal -->
		<div v-if="!loading && selectedOptions.propriedade && selectedOptions.safra" class="content-section">
			<!-- Seletor de Visualização -->
			<div class="view-selector">
				<div class="selector-card">
					<h3>Tipo de Visualização</h3>
					<div class="view-options">
						<div
							v-for="viewType in viewTypes"
							:key="viewType.key"
							class="view-option"
							:class="{ active: selectedView === viewType.key }"
							@click="selectView(viewType.key)"
						>
									<div class="option-icon" :style="{ backgroundColor: viewType.color }">
										<i :class="viewType.icon"></i>
									</div>
									<div class="option-content">
										<h4>{{ viewType.title }}</h4>
										<p>{{ viewType.description }}</p>
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- Visualizações -->
					<div class="visualizations-section">
						<!-- Distribuição de Frutos -->
						<div v-if="selectedView === 'distribution'" class="visualization-card">
							<div class="visualization-header">
								<h3>Distribuição de Frutos na Safra: {{ selectedOptions.safra }}</h3>
								<p>Visualização em gráfico de pizza e tabela detalhada</p>
							</div>

							<div class="chart-section">
								<div class="chart-container">
									<PieChart :key="chartKey" :chart-data="chartData" :chart-options="chartOptions" />
								</div>
							</div>

							<div class="table-section">
								<div class="table-header">
									<h4>Resumo Detalhado da Safra</h4>
								</div>
								<div class="table-container">
									<vue-good-table
										title="Resumo da safra"
										:columns="summaryColumns"
										:rows="summaryRows"
										:style="{ textAlign: 'center', verticalAlign: 'middle', marginBottom: '1rem' }"
									>
										<template slot="table-row" slot-scope="props">
											<span v-if="props.column.field == 'data'">{{ props.row.data }}</span>
											<span v-if="props.column.field == 'witchs_broom'">{{ props.row.witchs_broom }}</span>
											<span v-if="props.column.field == 'rotten'">{{ props.row.rotten }}</span>
											<span v-if="props.column.field == 'rat'">{{ props.row.rat }}</span>
											<span v-if="props.column.field == 'piece'">{{ props.row.piece }}</span>
											<span v-if="props.column.field == 'loss'">{{ props.row.loss }}</span>
											<span v-if="props.column.field == 'lossEstimate'">{{ props.row.lossEstimate }}</span>
											<span v-if="props.column.field == 'harvested'">{{ props.row.harvested }}</span>
											<span v-if="props.column.field == 'totalCocoa'">{{ props.row.totalCocoa }}</span>
										</template>
									</vue-good-table>
								</div>
								<div class="table-legend">
									<div class="legend-item">
										<strong>TP</strong> = Total de Perdas (VB + PP + RA + PE + PD)
									</div>
									<div class="legend-item">
										<strong>TF</strong> = Total de Frutos (TP + TC)
									</div>
								</div>
							</div>
						</div>

					<!-- Frutos Colhidos por Mês -->
					<div v-if="selectedView === 'monthly'" class="visualization-card">
						<div class="visualization-header">
							<h3>Frutos Colhidos por Mês na Safra: {{ selectedOptions.safra }}</h3>
							<p>Visualização em gráfico de barras e tabela detalhada por mês</p>
						</div>

						<div class="chart-section">
							<div class="chart-container">
								<BarChart :key="chartKey" :chart-data="harvestChartData" :chart-options="chartOptions" />
							</div>
						</div>

						<div class="table-section">
							<div class="table-header">
								<h4>Total de Frutos Colhidos por Mês</h4>
							</div>
							<div class="table-container">
								<vue-good-table
									title="Frutos Colhidos"
									:columns="harvestColumns"
									:rows="harvestRows"
									:style="{ textAlign: 'center', verticalAlign: 'middle', marginBottom: '1rem' }"
								>
									<template slot="table-row" slot-scope="props">
										<span v-if="props.column.field == 'data'">{{ props.row.data }}</span>
										<span v-if="props.column.field == 'out'">{{ props.row.out }}</span>
										<span v-if="props.column.field == 'nov'">{{ props.row.nov }}</span>
										<span v-if="props.column.field == 'dez'">{{ props.row.dez }}</span>
										<span v-if="props.column.field == 'jan'">{{ props.row.jan }}</span>
										<span v-if="props.column.field == 'fev'">{{ props.row.fev }}</span>
										<span v-if="props.column.field == 'mar'">{{ props.row.mar }}</span>
										<span v-if="props.column.field == 'abr'">{{ props.row.abr }}</span>
										<span v-if="props.column.field == 'mai'">{{ props.row.mai }}</span>
										<span v-if="props.column.field == 'jun'">{{ props.row.jun }}</span>
										<span v-if="props.column.field == 'jul'">{{ props.row.jul }}</span>
										<span v-if="props.column.field == 'ago'">{{ props.row.ago }}</span>
										<span v-if="props.column.field == 'set'">{{ props.row.set }}</span>
										<span v-if="props.column.field == 'total'">{{ props.row.total }}</span>
									</template>
								</vue-good-table>
							</div>
						</div>
					</div>

					<!-- Frutos Colhidos por Mês -->
					<div v-if="selectedView === 'monthly'" class="visualization-card">
						<div class="visualization-header">
							<h3>Frutos Colhidos por Mês na Safra: {{ selectedOptions.safra }}</h3>
							<p>Visualização em gráfico de barras e tabela detalhada por mês</p>
						</div>

						<div class="chart-section">
							<div class="chart-container">
								<BarChart :key="chartKey" :chart-data="harvestChartData" :chart-options="chartOptions" />
							</div>
						</div>

						<div class="table-section">
							<div class="table-header">
								<h4>Total de Frutos Colhidos por Mês</h4>
							</div>
							<div class="table-container">
								<vue-good-table
									title="Frutos Colhidos"
									:columns="harvestColumns"
									:rows="harvestRows"
									:style="{ textAlign: 'center', verticalAlign: 'middle', marginBottom: '1rem' }"
								>
									<template slot="table-row" slot-scope="props">
										<span v-if="props.column.field == 'data'">{{ props.row.data }}</span>
										<span v-if="props.column.field == 'out'">{{ props.row.out }}</span>
										<span v-if="props.column.field == 'nov'">{{ props.row.nov }}</span>
										<span v-if="props.column.field == 'dez'">{{ props.row.dez }}</span>
										<span v-if="props.column.field == 'jan'">{{ props.row.jan }}</span>
										<span v-if="props.column.field == 'fev'">{{ props.row.fev }}</span>
										<span v-if="props.column.field == 'mar'">{{ props.row.mar }}</span>
										<span v-if="props.column.field == 'abr'">{{ props.row.abr }}</span>
										<span v-if="props.column.field == 'mai'">{{ props.row.mai }}</span>
										<span v-if="props.column.field == 'jun'">{{ props.row.jun }}</span>
										<span v-if="props.column.field == 'jul'">{{ props.row.jul }}</span>
										<span v-if="props.column.field == 'ago'">{{ props.row.ago }}</span>
										<span v-if="props.column.field == 'set'">{{ props.row.set }}</span>
										<span v-if="props.column.field == 'total'">{{ props.row.total }}</span>
									</template>
								</vue-good-table>
							</div>
						</div>
					</div>

					<!-- Estado vazio quando não há visualização selecionada -->
					<div v-if="!selectedView" class="no-selection-card">
						<div class="no-selection-content">
							<div class="no-selection-icon">
								<i class="fas fa-table"></i>
							</div>
							<h3>Selecione um Tipo de Visualização</h3>
							<p>Escolha uma das opções acima para visualizar o resumo da safra com diferentes perspectivas.</p>
						</div>
					</div>
				</div>
			</div>

			<!-- Loading State -->
			<div class="loading-section" v-if="loading">
				<div class="loading-content">
					<div class="loading-spinner">
						<div class="spinner"></div>
					</div>
					<h3>Processando dados...</h3>
					<p>Aguarde enquanto geramos o resumo da safra</p>
				</div>
			</div>

			<!-- Alerta de Vassoura de Bruxa -->
			<div v-if="!loading && totalWitchBroomPercentage > 5.0" class="alert-section" @click="showAlertModal">
				<Alert text="Alerta" />
			</div>
		</div>
</template>

<script src="./HarvestSummary.js"></script>

<style lang="scss" scoped>
// Variáveis
$primary-color: #3d8160;
$primary-dark: #2d6047;
$text-color: #2c3e50;
$text-light: #6c757d;
$white: #ffffff;
$background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
$shadow: rgba(0, 0, 0, 0.1);
$shadow-hover: rgba(0, 0, 0, 0.15);
$border-color: #dee2e6;
$success-color: #28a745;
$danger-color: #dc3545;

// Container principal
.harvest-summary-container {
	min-height: 100vh;
	background: $background;
	padding: 1rem;
	font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

// Header no estilo DataImport
.page-header {
	background: $white;
	border-radius: 20px;
	padding: 2rem;
	margin-bottom: 2rem;
	box-shadow: 0 10px 30px $shadow;
	border: 1px solid rgba(255, 255, 255, 0.2);
	backdrop-filter: blur(10px);

	.header-content {
		display: flex;
		align-items: center;
		gap: 1.5rem;
	}

	.header-icon {
		width: 70px;
		height: 70px;
		background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
		border-radius: 20px;
		display: flex;
		align-items: center;
		justify-content: center;
		color: $white;
		font-size: 2rem;
		box-shadow: 0 8px 25px rgba($primary-color, 0.3);
	}

	.header-text {
		h1 {
			font-size: 2.5rem;
			font-weight: 700;
			color: $text-color;
			margin: 0 0 0.5rem 0;
			background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			background-clip: text;
		}

		p {
			font-size: 1.1rem;
			color: $text-light;
			margin: 0;
			font-weight: 400;
		}
	}
}

// Seção de filtros
.filters-section {
	margin-bottom: 2rem;

	.filters-card {
		background: $white;
		border-radius: 16px;
		padding: 2rem;
		box-shadow: 0 4px 20px $shadow;
		border: 1px solid rgba(255, 255, 255, 0.2);

		h3 {
			font-size: 1.5rem;
			font-weight: 600;
			color: $text-color;
			margin: 0 0 1.5rem 0;
			display: flex;
			align-items: center;
			gap: 0.5rem;

			&::before {
				content: '';
				width: 4px;
				height: 24px;
				background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
				border-radius: 2px;
			}
		}
	}
}

.filters-form {
	.filters-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 1.5rem;
		margin-bottom: 2rem;
	}

	.filter-group {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;

		label {
			font-weight: 600;
			color: $text-color;
			font-size: 0.95rem;
			display: flex;
			align-items: center;
			gap: 0.5rem;

			i {
				color: $primary-color;
				font-size: 1rem;
			}
		}

		.filter-select {
			padding: 0.75rem 1rem;
			border: 2px solid $border-color;
			border-radius: 8px;
			background: $white;
			font-size: 1rem;
			color: $text-color;
			transition: all 0.3s ease;
			appearance: none;
			background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
			background-position: right 0.5rem center;
			background-repeat: no-repeat;
			background-size: 1.5em 1.5em;
			padding-right: 2.5rem;

			&:focus {
				outline: none;
				border-color: $primary-color;
				box-shadow: 0 0 0 3px rgba($primary-color, 0.1);
			}

			&:disabled {
				background-color: #f8f9fa;
				color: #6c757d;
				cursor: not-allowed;
			}
		}

		.empty-message {
			display: flex;
			align-items: center;
			gap: 0.5rem;
			color: $danger-color;
			font-size: 0.85rem;
			font-weight: 500;
			background: rgba($danger-color, 0.1);
			padding: 0.5rem 0.75rem;
			border-radius: 6px;
			border-left: 3px solid $danger-color;

			i {
				font-size: 0.9rem;
			}
		}
	}
}

// Seletor de visualização
.view-selector {
	margin-bottom: 2rem;

	.selector-card {
		background: $white;
		border-radius: 16px;
		padding: 2rem;
		box-shadow: 0 4px 20px $shadow;
		border: 1px solid rgba(255, 255, 255, 0.2);

		h3 {
			font-size: 1.5rem;
			font-weight: 600;
			color: $text-color;
			margin: 0 0 1.5rem 0;
			text-align: center;
		}
	}

	.view-options {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
		gap: 1rem;
	}

	.view-option {
		display: flex;
		align-items: center;
		padding: 1.5rem;
		border: 2px solid $border-color;
		border-radius: 12px;
		cursor: pointer;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		background: $white;

		&:hover {
			border-color: $primary-color;
			box-shadow: 0 8px 25px rgba($primary-color, 0.15);
			transform: translateY(-4px);
		}

		&.active {
			border-color: $primary-color;
			background: rgba($primary-color, 0.05);
			box-shadow: 0 8px 25px rgba($primary-color, 0.2);

			.option-content h4 {
				color: $primary-color;
			}
		}

		.option-icon {
			width: 60px;
			height: 60px;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 1rem;
			flex-shrink: 0;

			i {
				color: $white;
				font-size: 1.5rem;
			}
		}

		.option-content {
			flex: 1;

			h4 {
				margin: 0 0 0.5rem 0;
				font-size: 1.1rem;
				font-weight: 600;
				color: $text-color;
				transition: color 0.3s ease;
			}

			p {
				margin: 0;
				font-size: 0.9rem;
				color: $text-light;
				line-height: 1.4;
			}
		}
	}
}

// Seção de conteúdo
.content-section {
	margin-bottom: 2rem;
}

// Visualizações
.visualizations-section {
	.visualization-card {
		background: $white;
		border-radius: 16px;
		padding: 2rem;
		box-shadow: 0 4px 20px $shadow;
		border: 1px solid rgba(255, 255, 255, 0.2);
		margin-bottom: 2rem;

		.visualization-header {
			margin-bottom: 2rem;
			text-align: center;

			h3 {
				font-size: 1.5rem;
				font-weight: 600;
				color: $text-color;
				margin: 0 0 0.5rem 0;
			}

			p {
				color: $text-light;
				margin: 0;
			}
		}

		.chart-section {
			margin-bottom: 2rem;

			.chart-container {
				height: 500px;
				width: 100%;
			}
		}

		.table-section {
			.table-header {
				margin-bottom: 1rem;
				text-align: center;

				h4 {
					font-size: 1.25rem;
					font-weight: 600;
					color: $text-color;
					margin: 0;
				}
			}

			.table-container {
				overflow-x: auto;
				border-radius: 8px;
				box-shadow: 0 2px 10px rgba($shadow, 0.5);
			}

			.table-legend {
				margin-top: 1rem;
				padding: 1rem;
				background: rgba($primary-color, 0.05);
				border-radius: 8px;
				border-left: 4px solid $primary-color;

				.legend-item {
					margin-bottom: 0.5rem;
					font-size: 0.9rem;
					color: $text-color;

					&:last-child {
						margin-bottom: 0;
					}

					strong {
						color: $primary-color;
					}
				}
			}
		}
	}

	.no-selection-card {
		background: $white;
		border-radius: 16px;
		padding: 3rem;
		box-shadow: 0 4px 20px $shadow;
		text-align: center;

		.no-selection-content {
			.no-selection-icon {
				width: 80px;
				height: 80px;
				background: rgba($text-light, 0.1);
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0 auto 1.5rem auto;
				color: $text-light;
				font-size: 2rem;
			}

			h3 {
				font-size: 1.5rem;
				font-weight: 600;
				color: $text-color;
				margin: 0 0 1rem 0;
			}

			p {
				color: $text-light;
				margin: 0;
				max-width: 400px;
				margin: 0 auto;
			}
		}
	}
}

// Loading state
.loading-section {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 400px;
	background: $white;
	border-radius: 16px;
	box-shadow: 0 4px 20px $shadow;

	.loading-content {
		text-align: center;

		.loading-spinner {
			margin-bottom: 1.5rem;
			display: flex;
			justify-content: center;

			.spinner {
				width: 40px;
				height: 40px;
				border: 4px solid rgba($primary-color, 0.3);
				border-top: 4px solid $primary-color;
				border-radius: 50%;
				animation: spin 1s linear infinite;
			}
		}

		h3 {
			font-size: 1.5rem;
			font-weight: 600;
			color: $text-color;
			margin: 0 0 0.5rem 0;
		}

		p {
			color: $text-light;
			margin: 0;
		}
	}
}

// Seção de alerta
.alert-section {
	margin-top: 2rem;
	cursor: pointer;
	transition: transform 0.3s ease;

	&:hover {
		transform: translateY(-2px);
	}
}

// Estilos para tabelas vue-good-table
::v-deep(.vgt-table) {
	border-radius: 8px;
	overflow: hidden;
	box-shadow: 0 2px 10px rgba($shadow, 0.5);

	th, td {
		padding: 12px 16px !important;
		text-align: center !important;
		vertical-align: middle !important;
		white-space: nowrap !important;
		border: 1px solid $border-color !important;
		font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
		font-size: 0.9rem;
	}

	thead th {
		background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%) !important;
		color: $white !important;
		font-weight: 600 !important;
	}

	tbody tr:nth-child(even) {
		background-color: rgba($primary-color, 0.02) !important;
	}

	tbody tr:hover {
		background-color: rgba($primary-color, 0.05) !important;
	}
}

// Animações
@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

// Responsividade
@media (max-width: 768px) {
	.harvest-summary-container {
		padding: 0.5rem;
	}

	.page-header {
		padding: 1.5rem;
		margin-bottom: 1.5rem;

		.header-content {
			flex-direction: column;
			text-align: center;
			gap: 1rem;
		}

		.header-icon {
			width: 60px;
			height: 60px;
			font-size: 1.8rem;
		}

		.header-text h1 {
			font-size: 2rem;
		}

		.header-text p {
			font-size: 1rem;
		}
	}

	.filters-card {
		padding: 1.5rem;
	}

	.filters-grid {
		grid-template-columns: 1fr;
		gap: 1rem;
	}

	.view-options {
		grid-template-columns: 1fr;
	}

	.view-option {
		flex-direction: column;
		text-align: center;

		.option-icon {
			margin-right: 0;
			margin-bottom: 1rem;
		}
	}

	.visualization-card {
		padding: 1.5rem;

		.chart-section .chart-container {
			height: 400px;
		}
	}

	// Responsividade para tabelas
	::v-deep(.vgt-table) {
		font-size: 0.8rem;

		th, td {
			padding: 8px 12px !important;
		}
	}
}

@media (max-width: 480px) {
	.page-header .header-text h1 {
		font-size: 1.75rem;
	}

	.selector-card h3 {
		font-size: 1.25rem;
	}

	.visualization-card .chart-section .chart-container {
		height: 350px;
	}

	::v-deep(.vgt-table) {
		font-size: 0.75rem;

		th, td {
			padding: 6px 8px !important;
		}
	}
}
</style>
