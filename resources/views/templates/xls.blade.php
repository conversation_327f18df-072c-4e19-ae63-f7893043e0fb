<table>
    <thead>
        <tr>
            <th>Visita</th>
            {{-- <th><PERSON>rea Homogênea</th> --}}
            <th>Data</th>
            <th>Floração</th>
            <th>Refoliação</th>
            <th>Copa</th>
            <th>Podada</th>
            <th>Roçada</th>
            <th>Capinada</th>
            <th>Raleada</th>
            <th>Renovada</th>
            <th>Des<PERSON>tad<PERSON></th>
            <th>Fert<PERSON>zada</th>
            <th>Pulverizada</th>
            <th>C.P Parda</th>
            <th>Vento</th>
            <th>Estiagem</th>
            <th>Chuva</th>
            <th>Ratos</th>
            <th>Enchente</th>
            <th>Insetos</th>
            <th>Ausência de sombra</th>
            <th>Excesso de sombra</th>
            <th>Notas</th>
            <th>Propriedade</th>
        </tr>
    </thead>
    <tbody>
        @foreach ($data as $key => $practice)
            <tr>
                <td>{{ $key + 1 }}</td>
                {{-- <td>{{ $homogeneous_area->label }}</td> --}}
                <td>{{ date('d/m/Y', strtotime($practice->date)) }}</td>
                <td>{{ $practice->flowering == 0 ? 'Nenhuma/Ruim' : ($practice->flowering == 1 ? 'Pouca/Regular' : 'Muita/Boa') }}</td>
                <td>{{ $practice->refoliation == 0 ? 'Nenhuma/Ruim' : ($practice->refoliation == 1 ? 'Pouca/Regular' : 'Muita/Boa') }}</td>
                <td>{{ $practice->top == 0 ? 'Nenhuma/Ruim' : ($practice->top == 1 ? 'Pouca/Regular' : 'Muita/Boa') }}</td>
                <td>{{ $practice->pruned ? 'S' : 'N' }}</td>
                <td>{{ $practice->mowing ? 'S' : 'N' }}</td>
                <td>{{ $practice->weeding ? 'S' : 'N' }}</td>
                <td>{{ $practice->grated ? 'S' : 'N' }}</td>
                <td>{{ $practice->renewed ? 'S' : 'N' }}</td>
                <td>{{ $practice->unbounded ? 'S' : 'N' }}</td>
                <td>{{ $practice->fertilized ? 'S' : 'N' }}</td>
                <td>{{ $practice->pulverized ? 'S' : 'N' }}</td>
                <td>{{ $practice->brown_rot ? 'S' : 'N' }}</td>
                <td>{{ $practice->wind ? 'S' : 'N' }}</td>
                <td>{{ $practice->drought ? 'S' : 'N' }}</td>
                <td>{{ $practice->rain ? 'S' : 'N' }}</td>
                <td>{{ $practice->rat ? 'S' : 'N' }}</td>
                <td>{{ $practice->flood ? 'S' : 'N' }}</td>
                <td>{{ $practice->insect ? 'S' : 'N' }}</td>
                <td>{{ $practice->absence_of_shadow ? 'S' : 'N' }}</td>
                <td>{{ $practice->excess_shade ? 'S' : 'N' }}</td>
                <td>{{ $practice->note }}</td>
                <td>{{ $homogeneous_area->property()->first()->name }}</td>
            </tr>
        @endforeach
    </tbody>
</table>
