{"name": "plataformacacau/plataformacacau", "type": "project", "description": "PlataformaCacau project", "keywords": ["Cocoa", "Cacau"], "license": "MIT", "require": {"php": "^8.0.2", "barryvdh/laravel-dompdf": "^2.0.0", "fruitcake/laravel-cors": "^3.0", "fzaninotto/faker": "^1.9.1", "guzzlehttp/guzzle": "7.5.0", "laravel/framework": "^9.0.0", "laravel/tinker": "^2.0", "laravel/ui": "^4.1.1", "maatwebsite/excel": "^3.1", "php-ai/php-ml": "^0.10.0", "rap2hpoutre/fast-excel": "^3.1", "tymon/jwt-auth": "dev-develop"}, "require-dev": {"mockery/mockery": "^1.3.1", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^8.5", "spatie/laravel-ignition": "^1.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/"}, "classmap": ["database/seeds", "database/factories"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}