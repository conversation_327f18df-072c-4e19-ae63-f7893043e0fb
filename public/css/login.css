@import url(https://fonts.googleapis.com/css2?family=Roboto);html {
  font-family: "Roboto", sans-serif;
  font-weight: 200;
  background: url("/img/background_login.png") no-repeat center center fixed;
  background-size: cover;
}

html,
body {
  height: 100%;
  background-color: rgba(56, 56, 56, 0.431372549);
  color: #343a40;
}

body.login-box {
  background-color: #f7f9fb;
  font-size: 14px;
}

.login-box .brand {
  width: 9em;
  height: 9em;
  margin: 10px auto;
  position: relative;
  z-index: 1;
}

.login-box .card-wrapper {
  margin-top: 5%;
  width: 400px;
}

.login-box .card {
  border-color: transparent;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.login-box .card.fat {
  padding: 10px;
}

.login-box .card .card-title {
  margin-bottom: 30px;
}

.login-box .card .card-body .form-inputs {
  padding-bottom: 3.5%;
}

.login-box .card .card-body .form-inputs .input-group-text:focus {
  outline: none;
  box-shadow: none;
}

.login-box .card .form-control {
  background-color: rgba(231, 231, 231, 0.7254901961) !important;
  border: 0px;
  border-color: transparent;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.144);
}

@supports ((-webkit-backdrop-filter: none) or (backdrop-filter: none)) {
  .login-box .card {
    -webkit-backdrop-filter: blur(13px);
    backdrop-filter: blur(13px);
    background-color: rgba(255, 255, 255, 0.5);
  }
}

.login-box .form-control {
  border-width: 2.3px;
}

.login-box .form-group label {
  width: 100%;
}

.login-box .btn.btn-block {
  padding: 12px 10px;
}

.login-box .footer {
  margin: 3% 0 0 0;
  color: #ffffff;
  text-align: center;
}

@media screen and (max-width: 425px) {
  .login-box .card-wrapper {
    width: 90%;
    margin: 0 auto;
  }
}

@media screen and (max-width: 320px) {
  .login-box .card.fat {
    padding: 0;
  }

  .login-box .card.fat .card-body {
    padding: 15px;
  }
}

