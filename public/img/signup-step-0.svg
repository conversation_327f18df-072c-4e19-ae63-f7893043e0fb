<svg xmlns="http://www.w3.org/2000/svg" data-name="Layer 1" width="728.25201" height="598.94268" viewBox="0 0 728.25201 598.94268" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M828.54917,210.72518c4.65329-18.97853.22879-41.112-14.80423-53.59546a90.03142,90.03142,0,0,1-22.02014,57.75737c-5.39319,6.17053-12.03421,12.47209-12.29579,20.66319-.16286,5.0967,2.32533,9.992,5.75361,13.7667,3.42853,3.77476,7.75356,6.59957,12.02738,9.38134l.62765,1.10748C811.595,245.92878,823.89589,229.70372,828.54917,210.72518Z" transform="translate(-235.874 -150.52866)" fill="#f0f0f0"/><path d="M813.44286,157.407a76.95263,76.95263,0,0,1,2.45029,47.28239,33.13822,33.13822,0,0,1-4.0479,9.56134,19.00625,19.00625,0,0,1-7.557,6.55375c-2.82466,1.42853-5.87941,2.56265-8.30763,4.65294a11.63766,11.63766,0,0,0-3.9151,8.675c-.11729,4.08665,1.24081,7.97174,2.701,11.72487,1.62121,4.16713,3.37116,8.4698,2.99642,13.03223-.0454.5528.815.59093.86035.039.652-7.93785-4.7391-14.64278-5.59417-22.34206-.399-3.59262.28227-7.32726,2.93854-9.94662,2.32278-2.29051,5.47186-3.46272,8.34439-4.88442a20.28118,20.28118,0,0,0,7.62786-6.13285,30.63566,30.63566,0,0,0,4.40056-9.37043A74.65778,74.65778,0,0,0,819.32628,183.11a78.31733,78.31733,0,0,0-5.12087-26.10361c-.19722-.51646-.95848-.11249-.76255.40059Z" transform="translate(-235.874 -150.52866)" fill="#fff"/><path d="M817.53286,199.73673a11.54535,11.54535,0,0,0,12.51956-8.17089c.15574-.53179-.66331-.79878-.81926-.26629a10.69072,10.69072,0,0,1-11.66134,7.57683c-.54944-.07225-.58526.78851-.039.86035Z" transform="translate(-235.874 -150.52866)" fill="#fff"/><path d="M805.81589,220.26876a22.25274,22.25274,0,0,1-4.72081-15.51481c.04345-.55294-.817-.59124-.86034-.039A23.14617,23.14617,0,0,0,805.18,220.84957c.34535.4348.97938-.14837.63591-.58081Z" transform="translate(-235.874 -150.52866)" fill="#fff"/><path d="M818.08315,174.6582a6.5352,6.5352,0,0,1-5.67506-2.49961c-.33966-.439-.97336.14471-.63591.58081a7.32323,7.32323,0,0,0,6.272,2.77915.44508.44508,0,0,0,.44966-.41069.43279.43279,0,0,0-.41069-.44966Z" transform="translate(-235.874 -150.52866)" fill="#fff"/><path d="M748.735,169.4159c.2043.28013.40859.56025.61063.8473a86.06488,86.06488,0,0,1,7.22271,11.94751c.16595.32.3293.64673.4857.9708a90.73174,90.73174,0,0,1,8.8055,32.22333,88.11162,88.11162,0,0,1,.06225,13.3876c-.44061,6.15617-1.40816,12.796.16439,18.56826a15.11411,15.11411,0,0,0,.5826,1.77782l29.76911,11.34709c.0806-.00817.159-.00945.23987-.01749l1.16788.50741c.0325-.22876.06129-.46671.09378-.69546.01941-.13262.03218-.26776.05158-.40037.01084-.08922.02136-.17859.03623-.25848.00372-.02968.00711-.05949.00825-.08241.01488-.07988.02021-.15567.0325-.22875q.2469-1.99446.46661-3.99932c.0026-.0068.0026-.0068-.00145-.01613a129.55615,129.55615,0,0,0,.00711-30.63c-.04018-.30313-.07744-.61293-.13122-.92124a84.07923,84.07923,0,0,0-2.97684-13.394,74.32939,74.32939,0,0,0-2.60862-7.054A61.81,61.81,0,0,0,782.49715,186.96c-8.5283-9.70708-20.06617-16.57859-32.78938-17.48447C749.38227,169.45263,749.06309,169.432,748.735,169.4159Z" transform="translate(-235.874 -150.52866)" fill="#f0f0f0"/><path d="M748.65791,169.81924a76.95264,76.95264,0,0,1,30.42368,36.2771,33.13823,33.13823,0,0,1,2.52457,10.07131,19.00634,19.00634,0,0,1-2.088,9.78262c-1.39526,2.84124-3.15149,5.58595-3.83179,8.71688a11.63765,11.63765,0,0,0,2.09695,9.28364c2.3668,3.33359,5.79026,5.61793,9.21574,7.73549,3.80335,2.35115,7.79109,4.733,10.23878,8.60145.29658.46872,1.00653-.01886.7104-.48687-4.25856-6.73047-12.59986-8.83818-17.91809-14.47081-2.48159-2.62829-4.18614-6.02036-3.6423-9.711.47557-3.22733,2.28418-6.05924,3.72178-8.92384a20.28137,20.28137,0,0,0,2.398-9.48923,30.63556,30.63556,0,0,0-2.12805-10.13121,74.6579,74.6579,0,0,0-11.54911-20.27531,78.31745,78.31745,0,0,0-19.8049-17.75916c-.46841-.29362-.833.48726-.36767.779Z" transform="translate(-235.874 -150.52866)" fill="#fff"/><path d="M777.409,201.15472a11.54534,11.54534,0,0,0,5.07673-14.06164c-.19582-.51836-1.01054-.23842-.81446.28064a10.69071,10.69071,0,0,1-4.74915,13.0706c-.4822.27312.00743.982.48688.7104Z" transform="translate(-235.874 -150.52866)" fill="#fff"/><path d="M780.41534,224.60283a22.25275,22.25275,0,0,1-13.11029-9.54546c-.29822-.46765-1.00826.01979-.7104.48688a23.14622,23.14622,0,0,0,13.66264,9.90518c.53752.13924.69265-.70812.15805-.8466Z" transform="translate(-235.874 -150.52866)" fill="#fff"/><path d="M762.74935,180.79961a6.5352,6.5352,0,0,1-6.03615,1.421c-.53549-.146-.69006.70157-.15806.8466a7.32324,7.32324,0,0,0,6.68109-1.55718.44508.44508,0,0,0,.11176-.59864.43279.43279,0,0,0-.59864-.11176Z" transform="translate(-235.874 -150.52866)" fill="#fff"/><path d="M709.374,598.1153c-99.80371,0-181-81.19629-181-181s81.19629-181,181-181a182.729,182.729,0,0,1,31.477,2.72754.99988.99988,0,1,1-.34522,1.96973A180.7818,180.7818,0,0,0,709.374,238.1153c-98.70093,0-179,80.29883-179,179s80.29907,179,179,179,179-80.29883,179-179a178.54758,178.54758,0,0,0-46.69507-120.56884,1.0001,1.0001,0,0,1,1.478-1.34766,180.544,180.544,0,0,1,47.217,121.9165C890.374,516.919,809.17771,598.1153,709.374,598.1153Z" transform="translate(-235.874 -150.52866)" fill="#e4e4e4"/><circle cx="553.30743" cy="111.85222" r="35.81102" fill="#3d8160"/><path d="M784.29607,281.01885a3.98234,3.98234,0,0,1-3.18647-1.59372l-9.76979-13.02661a3.98339,3.98339,0,1,1,6.37358-4.77986l6.39173,8.52166,16.41634-24.62418a3.98356,3.98356,0,0,1,6.629,4.41936l-19.53959,29.30938a3.98519,3.98519,0,0,1-3.204,1.77267C784.37,281.0182,784.333,281.01885,784.29607,281.01885Z" transform="translate(-235.874 -150.52866)" fill="#fff"/><path d="M704.71733,389.02638,649.49369,235.39553l1.40039-.50342-10.15625-28.25684-1.40039.50391-16.95605-47.16944a14.25546,14.25546,0,0,0-18.23731-8.59326l-99.83252,35.88526a14.25569,14.25569,0,0,0-8.59375,18.2373l11.82227,32.88934-2.21271.85212,7.54676,19.5971,1.77136-.68219,1.567,4.35913-1.88507.726,7.54676,19.5971,1.44372-.556,54.7359,152.27405a14.2297,14.2297,0,0,0,18.23779,8.59375l99.832-35.88525A14.27289,14.27289,0,0,0,704.71733,389.02638Z" transform="translate(-235.874 -150.52866)" fill="#3f3d56"/><path d="M591.04106,438.086a6.84506,6.84506,0,0,1-6.42432-4.52441L501.41215,202.08745a6.83147,6.83147,0,0,1,4.11768-8.73877l21.24121-7.63525-.03174.74316a7.87374,7.87374,0,0,0,10.52978,7.74219L584.40971,177.254a7.97439,7.97439,0,0,0,4.48779-5.63623,7.8083,7.8083,0,0,0-1.86084-6.835l-.49756-.55323,21.94092-7.8872a6.8404,6.8404,0,0,1,8.73926,4.11816L696.604,381.3062a18.147,18.147,0,0,1-10.92675,23.188L593.356,437.67974A6.84154,6.84154,0,0,1,591.04106,438.086Z" transform="translate(-235.874 -150.52866)" fill="#fff"/><circle cx="347.03341" cy="96.37717" r="27" fill="#3d8160"/><path d="M649.81307,286.17024l-93.50226,33.60994a2.36173,2.36173,0,1,1-1.59779-4.445l93.50227-33.60994a2.36173,2.36173,0,0,1,1.59778,4.445Z" transform="translate(-235.874 -150.52866)" fill="#e6e6e6"/><path d="M622.046,307.48044l-30.75552,11.05525a2.36185,2.36185,0,1,1-1.59787-4.44523l30.75552-11.05526a2.36185,2.36185,0,0,1,1.59787,4.44524Z" transform="translate(-235.874 -150.52866)" fill="#e6e6e6"/><path d="M629.36123,327.83127l-30.75552,11.05525a2.36184,2.36184,0,1,1-1.59786-4.44523L627.76337,323.386a2.36184,2.36184,0,0,1,1.59786,4.44523Z" transform="translate(-235.874 -150.52866)" fill="#e6e6e6"/><path d="M633.108,338.25473,602.35248,349.31a2.36173,2.36173,0,1,1-1.59778-4.445l30.75552-11.05525a2.36173,2.36173,0,1,1,1.59779,4.445Z" transform="translate(-235.874 -150.52866)" fill="#e6e6e6"/><path d="M642.901,311.31292l-65.25284,23.45552a2.36173,2.36173,0,1,1-1.59779-4.445l65.25284-23.45552a2.36173,2.36173,0,1,1,1.59779,4.445Z" transform="translate(-235.874 -150.52866)" fill="#e6e6e6"/><path d="M629.75754,363.55813l-11.16718,4.01411a4.01609,4.01609,0,0,1-2.717-7.55869l11.16718-4.01411a4.01609,4.01609,0,0,1,2.717,7.55869Z" transform="translate(-235.874 -150.52866)" fill="#3d8160"/><circle cx="305.88244" cy="34.38035" r="1.55299" fill="#fff"/><path d="M363.15716,272.25245c17.40561-20.48841,45.647-33.4005,71.68354-26.70621a123.86328,123.86328,0,0,0-46.63455,71.1133c-2.53077,10.98713-4.20157,23.471-13.1221,30.36645-5.55047,4.29064-13.01382,5.46354-19.98575,4.68493-6.97225-.77882-13.60828-3.32291-20.155-5.84463l-1.74064.19317C337.58115,319.53481,345.75155,292.74085,363.15716,272.25245Z" transform="translate(-235.874 -150.52866)" fill="#f0f0f0"/><path d="M434.77516,246.10652a105.86974,105.86974,0,0,0-54.68571,35.38859,45.59124,45.59124,0,0,0-7.39544,12.22116,26.14855,26.14855,0,0,0-1.21348,13.70827c.68479,4.30063,1.88328,8.62041,1.51072,13.01262a16.01079,16.01079,0,0,1-6.51439,11.3585c-4.46122,3.42552-9.88723,5.04375-15.24841,6.44185-5.95256,1.55232-12.16,3.07039-16.94407,7.16649-.57965.4963-1.31585-.43215-.73707-.9277,8.32345-7.12651,20.14449-6.52223,29.417-11.77633,4.32673-2.45166,7.94083-6.22209,8.71933-11.29507.68078-4.43609-.55131-8.89174-1.28231-13.24023a27.90251,27.90251,0,0,1,.68712-13.44795,42.14811,42.14811,0,0,1,6.89829-12.46039,102.71249,102.71249,0,0,1,23.39145-21.98624A107.7471,107.7471,0,0,1,434.607,244.93346c.73475-.19651.89816.97782.16821,1.17306Z" transform="translate(-235.874 -150.52866)" fill="#fff"/><path d="M384.28866,275.67424a15.88383,15.88383,0,0,1-.98469-20.54432c.46728-.60236,1.42526.09546.95737.69862a14.70807,14.70807,0,0,0,.955,19.10863c.52352.55427-.40717,1.28817-.9277.73707Z" transform="translate(-235.874 -150.52866)" fill="#fff"/><path d="M370.84612,305.28968A30.61481,30.61481,0,0,0,391.948,298.044c.58139-.49422,1.31776.43407.73707.9277a31.844,31.844,0,0,1-21.97374,7.49514c-.76314-.03445-.6242-1.21143.1348-1.17717Z" transform="translate(-235.874 -150.52866)" fill="#fff"/><path d="M411.80244,254.84189a8.991,8.991,0,0,0,7.36192,4.31129c.7632.02476.62344,1.20177-.13479,1.17717a10.07516,10.07516,0,0,1-8.15482-4.75139.61232.61232,0,0,1,.09531-.83238.59542.59542,0,0,1,.83238.09531Z" transform="translate(-235.874 -150.52866)" fill="#fff"/><path d="M473.55664,327.92448c-.477-.00191-.954-.00381-1.43688.00238a118.4065,118.4065,0,0,0-19.14216,1.58051c-.49048.073-.98645.15431-1.47382.24122a124.82719,124.82719,0,0,0-43.02164,16.16286,121.222,121.222,0,0,0-14.97463,10.724c-6.50765,5.45443-13.12962,11.88622-20.83237,14.7869a20.79359,20.79359,0,0,1-2.4516.78384L333.5733,348.168c-.05588-.09643-.11762-.18481-.17391-.2815l-1.50721-.89286c.22881-.22066.47086-.4446.69967-.66525.1322-.12855.27256-.25174.40476-.38029.09071-.084.18187-.16779.25893-.24878.03009-.02807.06059-.05588.08523-.07563.07706-.081.15724-.148.22881-.22066q2.02436-1.88322,4.08223-3.74452c.00549-.00837.00549-.00837.01914-.01139,10.47745-9.42818,21.83406-18.06448,34.14045-24.70252.37032-.19959.74572-.40782,1.13279-.59643a115.67412,115.67412,0,0,1,17.33155-7.48,102.26438,102.26438,0,0,1,9.96684-2.779,85.0372,85.0372,0,0,1,26.53608-1.65815c17.69713,1.68125,34.65957,9.00364,45.92719,22.45709C472.99385,327.23251,473.27413,327.57174,473.55664,327.92448Z" transform="translate(-235.874 -150.52866)" fill="#f0f0f0"/><path d="M473.16915,328.3356a105.8697,105.8697,0,0,0-64.96986-4.66876,45.59087,45.59087,0,0,0-13.26282,5.30534,26.14858,26.14858,0,0,0-9.22223,10.21468c-2.04251,3.84611-3.68638,8.01679-6.62826,11.29941a16.01081,16.01081,0,0,1-12.04,5.147c-5.62443.04912-10.93109-1.92564-16.05344-4.03714-5.68739-2.34441-11.55768-4.86963-17.84361-4.47946-.76163.04727-.79045-1.13728-.03-1.18448,10.93645-.67883,20.01107,6.92074,30.578,8.30832,4.93071.64747,10.08643-.18706,13.76231-3.76883,3.21439-3.13211,4.91325-7.4315,6.94768-11.34364a27.90247,27.90247,0,0,1,8.64522-10.32374,42.14793,42.14793,0,0,1,13.00991-5.79567,102.71256,102.71256,0,0,1,31.914-3.47152,107.747,107.747,0,0,1,35.765,7.76058c.705.28546.12841,1.32148-.572,1.03789Z" transform="translate(-235.874 -150.52866)" fill="#fff"/><path d="M415.05669,321.54741a15.88383,15.88383,0,0,1,11.58289-16.99634c.73575-.19962,1.08051.93432.34378,1.13421a14.70808,14.70808,0,0,0-10.74219,15.83216c.0843.75775-1.10067.78339-1.18448.03Z" transform="translate(-235.874 -150.52866)" fill="#fff"/><path d="M386.493,337.10034a30.61477,30.61477,0,0,0,21.21105,6.91953c.76177-.04458.79082,1.14.03,1.18448a31.844,31.844,0,0,1-22.05739-7.24526c-.58858-.487.231-1.34307.81637-.85875Z" transform="translate(-235.874 -150.52866)" fill="#fff"/><path d="M449.56742,321.47914a8.991,8.991,0,0,0,3.28239,7.87471c.59447.47927-.22577,1.3349-.81636.85875a10.07517,10.07517,0,0,1-3.65051-8.70349.61233.61233,0,0,1,.57726-.60722.59541.59541,0,0,1,.60722.57725Z" transform="translate(-235.874 -150.52866)" fill="#fff"/><path d="M885.5236,666.04254c18.97853,4.65329,41.112.22879,53.59546-14.80423a90.03142,90.03142,0,0,1-57.75737-22.02014c-6.17052-5.39319-12.47208-12.03421-20.66319-12.29579-5.0967-.16286-9.992,2.32533-13.7667,5.75361-3.77476,3.42853-6.59957,7.75355-9.38134,12.02738l-1.10748.62765C850.32,649.08841,866.54506,661.38926,885.5236,666.04254Z" transform="translate(-235.874 -150.52866)" fill="#f0f0f0"/><path d="M938.84181,650.93623a76.95263,76.95263,0,0,1-47.28239,2.45029,33.13822,33.13822,0,0,1-9.56134-4.0479,19.00631,19.00631,0,0,1-6.55375-7.557c-1.42853-2.82466-2.56265-5.87941-4.65294-8.30763a11.63766,11.63766,0,0,0-8.675-3.9151c-4.08665-.11729-7.97173,1.24081-11.72487,2.701-4.16713,1.62121-8.4698,3.37116-13.03223,2.99642-.5528-.0454-.59093.815-.039.86035,7.93785.652,14.64278-4.7391,22.34206-5.59417,3.59263-.399,7.32726.28227,9.94662,2.93854,2.29051,2.32278,3.46272,5.47186,4.88442,8.34439a20.28118,20.28118,0,0,0,6.13285,7.62786,30.63577,30.63577,0,0,0,9.37043,4.40056,74.65747,74.65747,0,0,0,23.14206,2.98581,78.31707,78.31707,0,0,0,26.10361-5.12086c.51646-.19722.11249-.95848-.40059-.76255Z" transform="translate(-235.874 -150.52866)" fill="#fff"/><path d="M896.51205,655.02623a11.54535,11.54535,0,0,0,8.17089,12.51956c.53179.15574.79878-.66331.26629-.81926a10.69073,10.69073,0,0,1-7.57683-11.66134c.07225-.54944-.78851-.58527-.86035-.039Z" transform="translate(-235.874 -150.52866)" fill="#fff"/><path d="M875.98,643.30926a22.25275,22.25275,0,0,1,15.51481-4.72081c.55294.04345.59124-.817.039-.86034a23.14617,23.14617,0,0,0-16.13458,4.94524c-.4348.34535.14837.97938.58081.63591Z" transform="translate(-235.874 -150.52866)" fill="#fff"/><path d="M921.59058,655.57652a6.5352,6.5352,0,0,1,2.49961-5.67506c.439-.33966-.1447-.97336-.58081-.63591a7.32326,7.32326,0,0,0-2.77915,6.272.44508.44508,0,0,0,.41069.44966.43279.43279,0,0,0,.44966-.41069Z" transform="translate(-235.874 -150.52866)" fill="#fff"/><path d="M926.83288,586.22837c-.28012.2043-.56025.40859-.8473.61063a86.06488,86.06488,0,0,1-11.94751,7.22271c-.32.166-.64673.3293-.9708.4857a90.73174,90.73174,0,0,1-32.22333,8.8055,88.1115,88.1115,0,0,1-13.38759.06225c-6.15618-.44061-12.796-1.40816-18.56827.16439a15.11411,15.11411,0,0,0-1.77782.5826l-11.34708,29.76911c.00816.0806.00944.159.01748.23987l-.5074,1.16788c.22875.0325.4667.06129.69545.09378.13262.01941.26776.03218.40038.05158.08921.01084.17859.02136.25847.03623.02968.00372.05949.00711.08241.00825.07989.01488.15567.02021.22875.0325q1.99446.2469,3.99932.46661c.0068.0026.0068.0026.01613-.00145a129.55615,129.55615,0,0,0,30.63.00711c.30313-.04018.61293-.07744.92124-.13122a84.07923,84.07923,0,0,0,13.394-2.97684,74.32939,74.32939,0,0,0,7.05395-2.60862,61.80987,61.80987,0,0,0,16.33543-10.32642c9.70708-8.5283,16.57859-20.06617,17.48447-32.78938C926.79616,586.87564,926.81674,586.55646,926.83288,586.22837Z" transform="translate(-235.874 -150.52866)" fill="#f0f0f0"/><path d="M926.42954,586.15128a76.95264,76.95264,0,0,1-36.2771,30.42368,33.13823,33.13823,0,0,1-10.07131,2.52457,19.00634,19.00634,0,0,1-9.78262-2.088c-2.84124-1.39526-5.58594-3.15149-8.71688-3.83179a11.63765,11.63765,0,0,0-9.28364,2.097c-3.33358,2.3668-5.61793,5.79026-7.73549,9.21574-2.35114,3.80335-4.733,7.79109-8.60145,10.23878-.46872.29658.01886,1.00653.48687.7104,6.73047-4.25856,8.83818-12.59986,14.47081-17.91809,2.62829-2.48159,6.02036-4.18614,9.711-3.6423,3.22733.47557,6.05924,2.28418,8.92384,3.72178a20.28138,20.28138,0,0,0,9.48924,2.398,30.63556,30.63556,0,0,0,10.1312-2.128,74.6579,74.6579,0,0,0,20.27531-11.54911A78.31745,78.31745,0,0,0,927.2085,586.519c.29362-.46841-.48726-.833-.779-.36767Z" transform="translate(-235.874 -150.52866)" fill="#fff"/><path d="M895.09406,614.90235a11.54536,11.54536,0,0,0,14.06164,5.07673c.51837-.19582.23842-1.01054-.28064-.81446a10.69071,10.69071,0,0,1-13.0706-4.74915c-.27312-.4822-.98195.00743-.7104.48688Z" transform="translate(-235.874 -150.52866)" fill="#fff"/><path d="M871.646,617.90871a22.25269,22.25269,0,0,1,9.54545-13.11029c.46765-.29822-.01979-1.00826-.48688-.7104a23.14626,23.14626,0,0,0-9.90518,13.66264c-.13924.53752.70812.69265.84661.15805Z" transform="translate(-235.874 -150.52866)" fill="#fff"/><path d="M915.44917,600.24272a6.5352,6.5352,0,0,1-1.421-6.03615c.146-.53549-.70157-.69006-.8466-.15806a7.32324,7.32324,0,0,0,1.55718,6.68109.44509.44509,0,0,0,.59864.11176.43279.43279,0,0,0,.11176-.59864Z" transform="translate(-235.874 -150.52866)" fill="#fff"/><path d="M961.57054,483.63l-28.94,48.7,1.1.58-9.8,18.58-2.04-1.08-6.44,10.83,1.6.92-10.5,18.18-1.82-1.05-94.43,158.92a18.27388,18.27388,0,0,1-25.02,6.37L609.73051,640.27a18.27169,18.27169,0,0,1-6.37-25.02l151.27-254.58a18.274,18.274,0,0,1,25.02-6.37l175.55,104.31A18.28057,18.28057,0,0,1,961.57054,483.63Z" transform="translate(-235.874 -150.52866)" fill="#3f3d56"/><path d="M791.53706,734.753,615.36864,630.07648a5.6339,5.6339,0,0,1-1.96328-7.71245L765.67918,366.09037a5.63389,5.63389,0,0,1,7.71245-1.96328L949.56,468.80362a5.63389,5.63389,0,0,1,1.96329,7.71245L799.24951,732.78973A5.6339,5.6339,0,0,1,791.53706,734.753Z" transform="translate(-235.874 -150.52866)" fill="#fff"/><circle cx="628.02812" cy="260.33322" r="1.55299" fill="#fff"/><path d="M854.291,595.99351,707.23435,508.61477a4.066,4.066,0,1,1,4.15392-6.99095l147.05661,87.37874a4.066,4.066,0,1,1-4.15392,6.99095Z" transform="translate(-235.874 -150.52866)" fill="#e6e6e6"/><path d="M795.57233,582.454l-48.371-28.74133a4.06616,4.06616,0,1,1,4.15411-6.99128l48.371,28.74132a4.06616,4.06616,0,0,1-4.15411,6.99129Z" transform="translate(-235.874 -150.52866)" fill="#e6e6e6"/><path d="M776.55428,614.461l-48.371-28.74133a4.06616,4.06616,0,0,1,4.15411-6.99128l48.371,28.74132a4.06616,4.06616,0,1,1-4.15411,6.99129Z" transform="translate(-235.874 -150.52866)" fill="#e6e6e6"/><path d="M766.81346,630.85456l-48.371-28.74132a4.066,4.066,0,1,1,4.15391-6.991l48.371,28.74133a4.066,4.066,0,1,1-4.15391,6.99095Z" transform="translate(-235.874 -150.52866)" fill="#e6e6e6"/><path d="M813.32475,614.35192l-102.627-60.97939a4.066,4.066,0,0,1,4.15391-6.99095l102.627,60.97939a4.066,4.066,0,1,1-4.15391,6.99095Z" transform="translate(-235.874 -150.52866)" fill="#e6e6e6"/><path d="M793.76409,692.28367l-17.5633-10.43584a6.91412,6.91412,0,0,1,7.06367-11.888l17.56329,10.43584a6.91411,6.91411,0,0,1-7.06366,11.888Z" transform="translate(-235.874 -150.52866)" fill="#3d8160"/><circle cx="590.65864" cy="336.31445" r="36" fill="#3d8160"/><path d="M236.298,633.13417H723.47882V351.08608a7.98,7.98,0,0,0-7.971-7.97078H244.269a7.98,7.98,0,0,0-7.971,7.97078Z" transform="translate(-235.874 -150.52866)" fill="#cacaca"/><rect x="15.47615" y="203.61075" width="457.07652" height="259.49057" fill="#fff"/><path d="M235.874,625.5021v34.805a10.98729,10.98729,0,0,0,10.98725,10.98741H426.67588v68.68868h-2.38875a1.42719,1.42719,0,0,0-1.42728,1.42723V746.188a1.4272,1.4272,0,0,0,1.42728,1.42728H535.91369A1.42721,1.42721,0,0,0,537.341,746.188v-4.77756a1.4272,1.4272,0,0,0-1.42729-1.42723h-2.38875V671.29455H713.33957a10.98729,10.98729,0,0,0,10.98726-10.98741v-34.805Z" transform="translate(-235.874 -150.52866)" fill="#3f3d56"/><path d="M642.2873,440.923H425.83344a5.145,5.145,0,1,1,0-10.29H642.2873a5.145,5.145,0,1,1,0,10.29Z" transform="translate(-235.874 -150.52866)" fill="#e6e6e6"/><path d="M569.65913,464.14854H498.46137a5.14526,5.14526,0,1,1,0-10.29052h71.19776a5.14526,5.14526,0,1,1,0,10.29052Z" transform="translate(-235.874 -150.52866)" fill="#e6e6e6"/><path d="M569.65913,511.25987H498.46137a5.14526,5.14526,0,0,1,0-10.29053h71.19776a5.14526,5.14526,0,0,1,0,10.29053Z" transform="translate(-235.874 -150.52866)" fill="#e6e6e6"/><path d="M569.65913,535.38975H498.46137a5.145,5.145,0,1,1,0-10.29h71.19776a5.145,5.145,0,1,1,0,10.29Z" transform="translate(-235.874 -150.52866)" fill="#e6e6e6"/><path d="M609.58906,487.37364H458.53144a5.145,5.145,0,1,1,0-10.29H609.58906a5.145,5.145,0,1,1,0,10.29Z" transform="translate(-235.874 -150.52866)" fill="#e6e6e6"/><path d="M638.68354,584.79454H612.832a8.749,8.749,0,0,1,0-17.49805h25.85156a8.749,8.749,0,0,1,0,17.49805Z" transform="translate(-235.874 -150.52866)" fill="#3d8160"/><circle cx="121.89451" cy="325.76588" r="45" fill="#3d8160"/><path d="M884.3587,749.47134h-647.294a1.19069,1.19069,0,0,1,0-2.38137h647.294a1.19069,1.19069,0,0,1,0,2.38137Z" transform="translate(-235.874 -150.52866)" fill="#cacaca"/><circle cx="243.80241" cy="196.40268" r="1.69602" fill="#fff"/></svg>