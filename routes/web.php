<?php

use App\Http\Controllers\Client\PanelController;
use Illuminate\Support\Facades\Route;

Route::namespace('Client')->group(function () {
	Route::get('/', 'HomeController@index')->name('home');

	Route::group([
		'prefix' => 'panel',
		'middleware' => ['auth:api', 'panel']
	], function () {
		Route::get('/', 'PanelController@index')
			->middleware('role:admin,properties-manager,pre-registered')
			->name('panel');

		Route::get('/strata', 'PanelController@index')
			->middleware('role:admin,strata-manager,pre-registered');

		Route::get('/blocks', 'PanelController@index')
			->middleware('role:pre-registered');
		Route::get('/overview', 'PanelController@index')
			->middleware('role:admin,pre-registered');
		Route::get('/add-operational-unit', 'PanelController@index')
			->middleware('role:admin,properties-manager,pre-registered');
		Route::get('/edit-block', 'PanelController@index')
			->middleware('role:admin,properties-manager,pre-registered');

		Route::get('/homogeneous-area', 'PanelController@index')
			->middleware('role:admin,pre-registered');
		Route::get('/homogeneous-area-practice', 'PanelController@index')
		->middleware('role:admin,pre-registered');

		Route::get('/users', 'PanelController@index')
			->middleware('role:admin,users-manager,pre-registered');

		Route::get('/sampling-points', 'PanelController@index')
			->middleware('role:admin,sampling_points-manager,pre-registered');
		Route::get('/trees', 'PanelController@index')
			->middleware('role:admin,sampling_points-manager,pre-registered');
		Route::get('tree-visits', 'PanelController@index')
			->middleware('role:admin,sampling_points-manager,pre-registered');

		Route::get('/change-password', 'PanelController@index');

		Route::get('/properties', 'PanelController@index')
			->middleware('role:admin,properties-manager,pre-registered');
		Route::get('/add-property', 'PanelController@index')
			->middleware('role:admin,properties-manager,pre-registered');
		Route::get('/properties/shared', 'PanelController@index')
			->middleware('role:admin,properties-manager');

		Route::get('/alert-center', 'PanelController@index')
			->middleware('role:admin,properties-manager,pre-registered');
		Route::get('/map', 'PanelController@index')
			->middleware('role:admin,properties-manager,pre-registered');

		Route::get('/reports', 'PanelController@index')
			->middleware('role:admin,reports-manager,pre-registered');
		Route::get('/docs', 'PanelController@index')
			->middleware('role:admin');
		Route::get('/data-import', 'PanelController@index')
		->middleware('role:admin');
		Route::get('/total-cocoa', 'PanelController@index')
		->middleware('role:admin, properties-manager');
		Route::get('/total-piece', 'PanelController@index')
		->middleware('role:admin, properties-manager');
		Route::get('/total-witch-broom', 'PanelController@index')
		->middleware('role:admin, properties-manager');
		Route::get('/total-rat', 'PanelController@index')
		->middleware('role:admin, properties-manager');
		Route::get('/total-loss', 'PanelController@index')
		->middleware('role:admin, properties-manager');
		Route::get('/total-rotten', 'PanelController@index')
		->middleware('role:admin, properties-manager');
		Route::get('/total-harvested', 'PanelController@index')
		->middleware('role:admin, properties-manager');
		Route::get('/safra-results', 'PanelController@index')
		->middleware('role:admin, properties-manager');
		Route::get('/data-analysis', 'PanelController@index')
		->middleware('role:admin, properties-manager');
		Route::get('/data-percentage', 'PanelController@index')
		->middleware('role:admin, properties-manager');
		Route::get('/harvest-summary', 'PanelController@index')
		->middleware('role:admin, properties-manager');
		Route::get('/total-harvested-ha', 'PanelController@index')
		->middleware('role:admin, properties-manager');
		Route::get('/total-loss-ha', 'PanelController@index')
		->middleware('role:admin, properties-manager');
		Route::get('/total-piece-ha', 'PanelController@index')
		->middleware('role:admin, properties-manager');
		Route::get('/total-rotten-ha', 'PanelController@index')
		->middleware('role:admin, properties-manager');
		Route::get('/total-witch-broom-ha', 'PanelController@index')
		->middleware('role:admin, properties-manager');
		Route::get('/fruit-loss-by-area', 'PanelController@index')
		->middleware('role:admin, properties-manager');
		Route::get('/fruit-loss-by-harvest', 'PanelController@index')
		->middleware('role:admin, properties-manager');
		Route::get('/add-property2', 'PanelController@index')
			->middleware('role:admin,properties-manager,pre-registered');
		Route::get('/pruning', 'PanelController@index')
			->middleware('role:admin,properties-manager,pre-registered');
		Route::get('/witch-broom', 'PanelController@index')
			->middleware('role:admin,properties-manager,pre-registered');

		Route::get('/rfids', 'PanelController@index');
	});

	Route::get('login', 'AuthController@index')->name('login');
	Route::post('login', 'AuthController@login');
	Route::get('signup', 'AuthController@signup')->name('signup');
	Route::get('signup-unified', 'AuthController@signup')->name('signup-unified');
	Route::post('signup', 'AuthController@register');
	Route::post('logout', 'AuthController@logout')->name('logout');
	Route::post('change-password', 'AuthController@change_password');
	Route::get('/me', 'AuthController@me');

	Route::get('/{any}', 'SPAController@index')
		->where('any', '^(?!api\/)[\/\w\.-]*');
});
