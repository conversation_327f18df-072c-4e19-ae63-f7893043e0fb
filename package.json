{"private": true, "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "npm run development -- --watch", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --disable-host-check --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "commit": "cz"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "devDependencies": {"@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "axios": "^0.19", "bootstrap": "^4.0.0", "commitizen": "^4.2.1", "cross-env": "^7.0", "cz-conventional-changelog": "^3.3.0", "husky": "^4.3.0", "jquery": "^3.2", "laravel-mix": "^5.0.1", "lodash": "^4.17.19", "popper.js": "^1.12", "resolve-url-loader": "^2.3.1", "sass": "^1.20.1", "sass-loader": "^8.0.0", "vue": "^2.6.11", "vue-template-compiler": "^2.6.10"}, "dependencies": {"@fortawesome/fontawesome-free": "^5.13.0", "chart.js": "^2.9.4", "eslint": "^7.4.0", "gerador-validador-cpf": "^5.0.0-beta.0", "mapbox-gl": "^1.11.1", "sweetalert2": "^9.15.3", "v-mask": "^2.2.1", "vee-validate": "^2.2.15", "vue-chartjs": "^3.5.1", "vue-good-table": "^2.20.0", "vue-mapbox": "^0.4.1", "vue-multiselect": "^2.1.9", "vue-router": "^3.3.4", "vue-simple-spinner": "^1.2.10", "vue-the-mask": "^0.11.1"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}